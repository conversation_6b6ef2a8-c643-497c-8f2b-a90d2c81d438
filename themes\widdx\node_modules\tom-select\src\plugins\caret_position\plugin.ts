/**
 * Plugin: "dropdown_input" (Tom <PERSON>)
 * Copyright (c) contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at:
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
 * ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 *
 */

import type TomSelect from '../../tom-select.ts';
import { nodeIndex, removeClasses } from '../../vanilla.ts';


export default function(this:TomSelect) {
	var self = this;

	/**
	 * Moves the caret to the specified index.
	 *
	 * The input must be moved by leaving it in place and moving the
	 * siblings, due to the fact that focus cannot be restored once lost
	 * on mobile webkit devices
	 *
	 */
	self.hook('instead','setCaret',(new_pos:number) => {

		if( self.settings.mode === 'single' || !self.control.contains(self.control_input) ) {
			new_pos = self.items.length;
		} else {
			new_pos = Math.max(0, Math.min(self.items.length, new_pos));

			if( new_pos != self.caretPos && !self.isPending ){

				self.controlChildren().forEach((child,j) => {
					if( j < new_pos ){
						self.control_input.insertAdjacentElement('beforebegin', child );
					} else {
						self.control.appendChild( child );
					}
				});
			}
		}

		self.caretPos = new_pos;
	});

	self.hook('instead','moveCaret',(direction:number) => {

		if( !self.isFocused ) return;

		// move caret before or after selected items
		const last_active		= self.getLastActive(direction);
		if( last_active ){
			const idx = nodeIndex(last_active);
			self.setCaret(direction > 0 ? idx + 1: idx);
			self.setActiveItem();
			removeClasses(last_active as HTMLElement,'last-active');

		// move caret left or right of current position
		}else{
			self.setCaret(self.caretPos + direction);

		}

	});

};
