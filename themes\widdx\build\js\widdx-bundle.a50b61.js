(()=>{var t={34:(t,e,n)=>{"use strict";var r=n(4901);t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},81:(t,e,n)=>{"use strict";var r=n(9565),i=n(9306),o=n(8551),s=n(6823),a=n(851),c=TypeError;t.exports=function(t,e){var n=arguments.length<2?a(t):e;if(i(n))return o(r(n,t));throw new c(s(t)+" is not iterable")}},235:(t,e,n)=>{"use strict";var r=n(9213).forEach,i=n(4598)("forEach");t.exports=i?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},283:(t,e,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(4901),s=n(9297),a=n(3724),c=n(350).CONFIGURABLE,l=n(3706),u=n(1181),f=u.enforce,d=u.get,p=String,h=Object.defineProperty,g=r("".slice),m=r("".replace),v=r([].join),y=a&&!i(function(){return 8!==h(function(){},"length",{value:8}).length}),b=String(String).split("String"),_=t.exports=function(t,e,n){"Symbol("===g(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!s(t,"name")||c&&t.name!==e)&&(a?h(t,"name",{value:e,configurable:!0}):t.name=e),y&&n&&s(n,"arity")&&t.length!==n.arity&&h(t,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&h(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var r=f(t);return s(r,"source")||(r.source=v(b,"string"==typeof e?e:"")),t};Function.prototype.toString=_(function(){return o(this)&&d(this).source||l(this)},"toString")},298:(t,e,n)=>{"use strict";var r=n(2195),i=n(5397),o=n(8480).f,s=n(7680),a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return a&&"Window"===r(t)?function(t){try{return o(t)}catch(t){return s(a)}}(t):o(i(t))}},350:(t,e,n)=>{"use strict";var r=n(3724),i=n(9297),o=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,a=i(o,"name"),c=a&&"something"===function(){}.name,l=a&&(!r||r&&s(o,"name").configurable);t.exports={EXISTS:a,PROPER:c,CONFIGURABLE:l}},397:(t,e,n)=>{"use strict";var r=n(7751);t.exports=r("document","documentElement")},421:t=>{"use strict";t.exports={}},511:(t,e,n)=>{"use strict";var r=n(9167),i=n(9297),o=n(1951),s=n(4913).f;t.exports=function(t){var e=r.Symbol||(r.Symbol={});i(e,t)||s(e,t,{value:o.f(t)})}},597:(t,e,n)=>{"use strict";var r=n(9039),i=n(8227),o=n(9519),s=i("species");t.exports=function(t){return o>=51||!r(function(){var e=[];return(e.constructor={})[s]=function(){return{foo:1}},1!==e[t](Boolean).foo})}},616:(t,e,n)=>{"use strict";var r=n(9039);t.exports=!r(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})},655:(t,e,n)=>{"use strict";var r=n(6955),i=String;t.exports=function(t){if("Symbol"===r(t))throw new TypeError("Cannot convert a Symbol value to a string");return i(t)}},687:(t,e,n)=>{"use strict";var r=n(4913).f,i=n(9297),o=n(8227)("toStringTag");t.exports=function(t,e,n){t&&!n&&(t=t.prototype),t&&!i(t,o)&&r(t,o,{configurable:!0,value:e})}},741:t=>{"use strict";var e=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var r=+t;return(r>0?n:e)(r)}},757:(t,e,n)=>{"use strict";var r=n(7751),i=n(4901),o=n(1625),s=n(7040),a=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return i(e)&&o(e.prototype,a(t))}},851:(t,e,n)=>{"use strict";var r=n(6955),i=n(5966),o=n(4117),s=n(6269),a=n(8227)("iterator");t.exports=function(t){if(!o(t))return i(t,a)||i(t,"@@iterator")||s[r(t)]}},1034:(t,e,n)=>{"use strict";var r=n(9565),i=n(9297),o=n(1625),s=n(5213),a=n(7979),c=RegExp.prototype;t.exports=s.correct?function(t){return t.flags}:function(t){return s.correct||!o(c,t)||i(t,"flags")?t.flags:r(a,t)}},1072:(t,e,n)=>{"use strict";var r=n(1828),i=n(8727);t.exports=Object.keys||function(t){return r(t,i)}},1088:(t,e,n)=>{"use strict";var r=n(6518),i=n(9565),o=n(6395),s=n(350),a=n(4901),c=n(3994),l=n(2787),u=n(2967),f=n(687),d=n(6699),p=n(6840),h=n(8227),g=n(6269),m=n(7657),v=s.PROPER,y=s.CONFIGURABLE,b=m.IteratorPrototype,_=m.BUGGY_SAFARI_ITERATORS,x=h("iterator"),w="keys",T="values",E="entries",A=function(){return this};t.exports=function(t,e,n,s,h,m,S){c(n,e,s);var C,O,k,j=function(t){if(t===h&&I)return I;if(!_&&t&&t in N)return N[t];switch(t){case w:case T:case E:return function(){return new n(this,t)}}return function(){return new n(this)}},D=e+" Iterator",L=!1,N=t.prototype,P=N[x]||N["@@iterator"]||h&&N[h],I=!_&&P||j(h),M="Array"===e&&N.entries||P;if(M&&(C=l(M.call(new t)))!==Object.prototype&&C.next&&(o||l(C)===b||(u?u(C,b):a(C[x])||p(C,x,A)),f(C,D,!0,!0),o&&(g[D]=A)),v&&h===T&&P&&P.name!==T&&(!o&&y?d(N,"name",T):(L=!0,I=function(){return i(P,this)})),h)if(O={values:j(T),keys:m?I:j(w),entries:j(E)},S)for(k in O)(_||L||!(k in N))&&p(N,k,O[k]);else r({target:e,proto:!0,forced:_||L},O);return o&&!S||N[x]===I||p(N,x,I,{name:h}),g[e]=I,O}},1181:(t,e,n)=>{"use strict";var r,i,o,s=n(8622),a=n(4576),c=n(34),l=n(6699),u=n(9297),f=n(7629),d=n(6119),p=n(421),h="Object already initialized",g=a.TypeError,m=a.WeakMap;if(s||f.state){var v=f.state||(f.state=new m);v.get=v.get,v.has=v.has,v.set=v.set,r=function(t,e){if(v.has(t))throw new g(h);return e.facade=t,v.set(t,e),e},i=function(t){return v.get(t)||{}},o=function(t){return v.has(t)}}else{var y=d("state");p[y]=!0,r=function(t,e){if(u(t,y))throw new g(h);return e.facade=t,l(t,y,e),e},i=function(t){return u(t,y)?t[y]:{}},o=function(t){return u(t,y)}}t.exports={set:r,get:i,has:o,enforce:function(t){return o(t)?i(t):r(t,{})},getterFor:function(t){return function(e){var n;if(!c(e)||(n=i(e)).type!==t)throw new g("Incompatible receiver, "+t+" required");return n}}}},1291:(t,e,n)=>{"use strict";var r=n(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:r(e)}},1296:(t,e,n)=>{"use strict";var r=n(4495);t.exports=r&&!!Symbol.for&&!!Symbol.keyFor},1469:(t,e,n)=>{"use strict";var r=n(7433);t.exports=function(t,e){return new(r(t))(0===e?0:e)}},1510:(t,e,n)=>{"use strict";var r=n(6518),i=n(7751),o=n(9297),s=n(655),a=n(5745),c=n(1296),l=a("string-to-symbol-registry"),u=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!c},{for:function(t){var e=s(t);if(o(l,e))return l[e];var n=i("Symbol")(e);return l[e]=n,u[n]=e,n}})},1625:(t,e,n)=>{"use strict";var r=n(9504);t.exports=r({}.isPrototypeOf)},1629:(t,e,n)=>{"use strict";var r=n(6518),i=n(235);r({target:"Array",proto:!0,forced:[].forEach!==i},{forEach:i})},1828:(t,e,n)=>{"use strict";var r=n(9504),i=n(9297),o=n(5397),s=n(9617).indexOf,a=n(421),c=r([].push);t.exports=function(t,e){var n,r=o(t),l=0,u=[];for(n in r)!i(a,n)&&i(r,n)&&c(u,n);for(;e.length>l;)i(r,n=e[l++])&&(~s(u,n)||c(u,n));return u}},1951:(t,e,n)=>{"use strict";var r=n(8227);e.f=r},2010:(t,e,n)=>{"use strict";var r=n(3724),i=n(350).EXISTS,o=n(9504),s=n(2106),a=Function.prototype,c=o(a.toString),l=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,u=o(l.exec);r&&!i&&s(a,"name",{configurable:!0,get:function(){try{return u(l,c(this))[1]}catch(t){return""}}})},2062:(t,e,n)=>{"use strict";var r=n(6518),i=n(9213).map;r({target:"Array",proto:!0,forced:!n(597)("map")},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},2106:(t,e,n)=>{"use strict";var r=n(283),i=n(4913);t.exports=function(t,e,n){return n.get&&r(n.get,e,{getter:!0}),n.set&&r(n.set,e,{setter:!0}),i.f(t,e,n)}},2140:(t,e,n)=>{"use strict";var r={};r[n(8227)("toStringTag")]="z",t.exports="[object z]"===String(r)},2195:(t,e,n)=>{"use strict";var r=n(9504),i=r({}.toString),o=r("".slice);t.exports=function(t){return o(i(t),8,-1)}},2211:(t,e,n)=>{"use strict";var r=n(9039);t.exports=!r(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},2259:(t,e,n)=>{"use strict";n(511)("iterator")},2360:(t,e,n)=>{"use strict";var r,i=n(8551),o=n(6801),s=n(8727),a=n(421),c=n(397),l=n(4055),u=n(6119),f="prototype",d="script",p=u("IE_PROTO"),h=function(){},g=function(t){return"<"+d+">"+t+"</"+d+">"},m=function(t){t.write(g("")),t.close();var e=t.parentWindow.Object;return t=null,e},v=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}var t,e,n;v="undefined"!=typeof document?document.domain&&r?m(r):(e=l("iframe"),n="java"+d+":",e.style.display="none",c.appendChild(e),e.src=String(n),(t=e.contentWindow.document).open(),t.write(g("document.F=Object")),t.close(),t.F):m(r);for(var i=s.length;i--;)delete v[f][s[i]];return v()};a[p]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(h[f]=i(t),n=new h,h[f]=null,n[p]=t):n=v(),void 0===e?n:o.f(n,e)}},2480:(t,e,n)=>{"use strict";n(5081)},2529:t=>{"use strict";t.exports=function(t,e){return{value:t,done:e}}},2675:(t,e,n)=>{"use strict";n(6761),n(1510),n(7812),n(3110),n(9773)},2777:(t,e,n)=>{"use strict";var r=n(9565),i=n(34),o=n(757),s=n(5966),a=n(4270),c=n(8227),l=TypeError,u=c("toPrimitive");t.exports=function(t,e){if(!i(t)||o(t))return t;var n,c=s(t,u);if(c){if(void 0===e&&(e="default"),n=r(c,t,e),!i(n)||o(n))return n;throw new l("Can't convert object to primitive value")}return void 0===e&&(e="number"),a(t,e)}},2787:(t,e,n)=>{"use strict";var r=n(9297),i=n(4901),o=n(8981),s=n(6119),a=n(2211),c=s("IE_PROTO"),l=Object,u=l.prototype;t.exports=a?l.getPrototypeOf:function(t){var e=o(t);if(r(e,c))return e[c];var n=e.constructor;return i(n)&&e instanceof n?n.prototype:e instanceof l?u:null}},2796:(t,e,n)=>{"use strict";var r=n(9039),i=n(4901),o=/#|\.prototype\./,s=function(t,e){var n=c[a(t)];return n===u||n!==l&&(i(e)?r(e):!!e)},a=s.normalize=function(t){return String(t).replace(o,".").toLowerCase()},c=s.data={},l=s.NATIVE="N",u=s.POLYFILL="P";t.exports=s},2839:(t,e,n)=>{"use strict";var r=n(4576).navigator,i=r&&r.userAgent;t.exports=i?String(i):""},2953:(t,e,n)=>{"use strict";var r=n(4576),i=n(7400),o=n(9296),s=n(3792),a=n(6699),c=n(687),l=n(8227)("iterator"),u=s.values,f=function(t,e){if(t){if(t[l]!==u)try{a(t,l,u)}catch(e){t[l]=u}if(c(t,e,!0),i[e])for(var n in s)if(t[n]!==s[n])try{a(t,n,s[n])}catch(e){t[n]=s[n]}}};for(var d in i)f(r[d]&&r[d].prototype,d);f(o,"DOMTokenList")},2967:(t,e,n)=>{"use strict";var r=n(6706),i=n(34),o=n(7750),s=n(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=r(Object.prototype,"__proto__","set"))(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return o(n),s(r),i(n)?(e?t(n,r):n.__proto__=r,n):n}}():void 0)},3110:(t,e,n)=>{"use strict";var r=n(6518),i=n(7751),o=n(8745),s=n(9565),a=n(9504),c=n(9039),l=n(4901),u=n(757),f=n(7680),d=n(6933),p=n(4495),h=String,g=i("JSON","stringify"),m=a(/./.exec),v=a("".charAt),y=a("".charCodeAt),b=a("".replace),_=a(1.1.toString),x=/[\uD800-\uDFFF]/g,w=/^[\uD800-\uDBFF]$/,T=/^[\uDC00-\uDFFF]$/,E=!p||c(function(){var t=i("Symbol")("stringify detection");return"[null]"!==g([t])||"{}"!==g({a:t})||"{}"!==g(Object(t))}),A=c(function(){return'"\\udf06\\ud834"'!==g("\udf06\ud834")||'"\\udead"'!==g("\udead")}),S=function(t,e){var n=f(arguments),r=d(e);if(l(r)||void 0!==t&&!u(t))return n[1]=function(t,e){if(l(r)&&(e=s(r,this,h(t),e)),!u(e))return e},o(g,null,n)},C=function(t,e,n){var r=v(n,e-1),i=v(n,e+1);return m(w,t)&&!m(T,i)||m(T,t)&&!m(w,r)?"\\u"+_(y(t,0),16):t};g&&r({target:"JSON",stat:!0,arity:3,forced:E||A},{stringify:function(t,e,n){var r=f(arguments),i=o(E?S:g,null,r);return A&&"string"==typeof i?b(i,x,C):i}})},3179:(t,e,n)=>{"use strict";var r=n(2140),i=n(6955);t.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},3288:(t,e,n)=>{"use strict";var r=n(9504),i=n(6840),o=Date.prototype,s="Invalid Date",a="toString",c=r(o[a]),l=r(o.getTime);String(new Date(NaN))!==s&&i(o,a,function(){var t=l(this);return t==t?c(this):s})},3392:(t,e,n)=>{"use strict";var r=n(9504),i=0,o=Math.random(),s=r(1.1.toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++i+o,36)}},3418:(t,e,n)=>{"use strict";var r=n(6518),i=n(7916);r({target:"Array",stat:!0,forced:!n(4428)(function(t){Array.from(t)})},{from:i})},3500:(t,e,n)=>{"use strict";var r=n(4576),i=n(7400),o=n(9296),s=n(235),a=n(6699),c=function(t){if(t&&t.forEach!==s)try{a(t,"forEach",s)}catch(e){t.forEach=s}};for(var l in i)i[l]&&c(r[l]&&r[l].prototype);c(o)},3506:(t,e,n)=>{"use strict";var r=n(3925),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o("Can't set "+i(t)+" as a prototype")}},3517:(t,e,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(4901),s=n(6955),a=n(7751),c=n(3706),l=function(){},u=a("Reflect","construct"),f=/^\s*(?:class|function)\b/,d=r(f.exec),p=!f.test(l),h=function(t){if(!o(t))return!1;try{return u(l,[],t),!0}catch(t){return!1}},g=function(t){if(!o(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!d(f,c(t))}catch(t){return!0}};g.sham=!0,t.exports=!u||i(function(){var t;return h(h.call)||!h(Object)||!h(function(){t=!0})||t})?g:h},3635:(t,e,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp;t.exports=r(function(){var t=i(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})},3706:(t,e,n)=>{"use strict";var r=n(9504),i=n(4901),o=n(7629),s=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(t){return s(t)}),t.exports=o.inspectSource},3717:(t,e)=>{"use strict";e.f=Object.getOwnPropertySymbols},3724:(t,e,n)=>{"use strict";var r=n(9039);t.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},3792:(t,e,n)=>{"use strict";var r=n(5397),i=n(6469),o=n(6269),s=n(1181),a=n(4913).f,c=n(1088),l=n(2529),u=n(6395),f=n(3724),d="Array Iterator",p=s.set,h=s.getterFor(d);t.exports=c(Array,"Array",function(t,e){p(this,{type:d,target:r(t),index:0,kind:e})},function(){var t=h(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=null,l(void 0,!0);switch(t.kind){case"keys":return l(n,!1);case"values":return l(e[n],!1)}return l([n,e[n]],!1)},"values");var g=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!u&&f&&"values"!==g.name)try{a(g,"name",{value:"values"})}catch(t){}},3925:(t,e,n)=>{"use strict";var r=n(34);t.exports=function(t){return r(t)||null===t}},3994:(t,e,n)=>{"use strict";var r=n(7657).IteratorPrototype,i=n(2360),o=n(6980),s=n(687),a=n(6269),c=function(){return this};t.exports=function(t,e,n,l){var u=e+" Iterator";return t.prototype=i(r,{next:o(+!l,n)}),s(t,u,!1,!0),a[u]=c,t}},4055:(t,e,n)=>{"use strict";var r=n(4576),i=n(34),o=r.document,s=i(o)&&i(o.createElement);t.exports=function(t){return s?o.createElement(t):{}}},4117:t=>{"use strict";t.exports=function(t){return null==t}},4209:(t,e,n)=>{"use strict";var r=n(8227),i=n(6269),o=r("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||s[o]===t)}},4270:(t,e,n)=>{"use strict";var r=n(9565),i=n(4901),o=n(34),s=TypeError;t.exports=function(t,e){var n,a;if("string"===e&&i(n=t.toString)&&!o(a=r(n,t)))return a;if(i(n=t.valueOf)&&!o(a=r(n,t)))return a;if("string"!==e&&i(n=t.toString)&&!o(a=r(n,t)))return a;throw new s("Can't convert object to primitive value")}},4346:(t,e,n)=>{"use strict";n(6518)({target:"Array",stat:!0},{isArray:n(4376)})},4376:(t,e,n)=>{"use strict";var r=n(2195);t.exports=Array.isArray||function(t){return"Array"===r(t)}},4428:(t,e,n)=>{"use strict";var r=n(8227)("iterator"),i=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){i=!0}};s[r]=function(){return this},Array.from(s,function(){throw 2})}catch(t){}t.exports=function(t,e){try{if(!e&&!i)return!1}catch(t){return!1}var n=!1;try{var o={};o[r]=function(){return{next:function(){return{done:n=!0}}}},t(o)}catch(t){}return n}},4495:(t,e,n)=>{"use strict";var r=n(9519),i=n(9039),o=n(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!i(function(){var t=Symbol("symbol detection");return!o(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41})},4576:function(t,e,n){"use strict";var r=function(t){return t&&t.Math===Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4598:(t,e,n)=>{"use strict";var r=n(9039);t.exports=function(t,e){var n=[][t];return!!n&&r(function(){n.call(null,e||function(){return 1},1)})}},4659:(t,e,n)=>{"use strict";var r=n(3724),i=n(4913),o=n(6980);t.exports=function(t,e,n){r?i.f(t,e,o(0,n)):t[e]=n}},4692:function(t,e){var n;!function(e,n){"use strict";"object"==typeof t.exports?t.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)}("undefined"!=typeof window?window:this,function(r,i){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,c=o.flat?function(t){return o.flat.call(t)}:function(t){return o.concat.apply([],t)},l=o.push,u=o.indexOf,f={},d=f.toString,p=f.hasOwnProperty,h=p.toString,g=h.call(Object),m={},v=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},y=function(t){return null!=t&&t===t.window},b=r.document,_={type:!0,src:!0,nonce:!0,noModule:!0};function x(t,e,n){var r,i,o=(n=n||b).createElement("script");if(o.text=t,e)for(r in _)(i=e[r]||e.getAttribute&&e.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function w(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?f[d.call(t)]||"object":typeof t}var T="3.7.1",E=/HTML$/i,A=function(t,e){return new A.fn.init(t,e)};function S(t){var e=!!t&&"length"in t&&t.length,n=w(t);return!v(t)&&!y(t)&&("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t)}function C(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}A.fn=A.prototype={jquery:T,constructor:A,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=A.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return A.each(this,t)},map:function(t){return this.pushStack(A.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(A.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(A.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:o.sort,splice:o.splice},A.extend=A.fn.extend=function(){var t,e,n,r,i,o,s=arguments[0]||{},a=1,c=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[a]||{},a++),"object"==typeof s||v(s)||(s={}),a===c&&(s=this,a--);a<c;a++)if(null!=(t=arguments[a]))for(e in t)r=t[e],"__proto__"!==e&&s!==r&&(l&&r&&(A.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[e],o=i&&!Array.isArray(n)?[]:i||A.isPlainObject(n)?n:{},i=!1,s[e]=A.extend(l,o,r)):void 0!==r&&(s[e]=r));return s},A.extend({expando:"jQuery"+(T+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==d.call(t))&&(!(e=s(t))||"function"==typeof(n=p.call(e,"constructor")&&e.constructor)&&h.call(n)===g)},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){x(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,r=0;if(S(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},text:function(t){var e,n="",r=0,i=t.nodeType;if(!i)for(;e=t[r++];)n+=A.text(e);return 1===i||11===i?t.textContent:9===i?t.documentElement.textContent:3===i||4===i?t.nodeValue:n},makeArray:function(t,e){var n=e||[];return null!=t&&(S(Object(t))?A.merge(n,"string"==typeof t?[t]:t):l.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:u.call(e,t,n)},isXMLDoc:function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!E.test(e||n&&n.nodeName||"HTML")},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!==s&&r.push(t[i]);return r},map:function(t,e,n){var r,i,o=0,s=[];if(S(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&s.push(i);else for(o in t)null!=(i=e(t[o],o,n))&&s.push(i);return c(s)},guid:1,support:m}),"function"==typeof Symbol&&(A.fn[Symbol.iterator]=o[Symbol.iterator]),A.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){f["[object "+e+"]"]=e.toLowerCase()});var O=o.pop,k=o.sort,j=o.splice,D="[\\x20\\t\\r\\n\\f]",L=new RegExp("^"+D+"+|((?:^|[^\\\\])(?:\\\\.)*)"+D+"+$","g");A.contains=function(t,e){var n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(t.contains?t.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))};var N=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function P(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}A.escapeSelector=function(t){return(t+"").replace(N,P)};var I=b,M=l;!function(){var t,e,n,i,s,c,l,f,d,h,g=M,v=A.expando,y=0,b=0,_=tt(),x=tt(),w=tt(),T=tt(),E=function(t,e){return t===e&&(s=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",N="(?:\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",P="\\["+D+"*("+N+")(?:"+D+"*([*^$|!~]?=)"+D+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+N+"))|)"+D+"*\\]",$=":("+N+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+P+")*)|.*)\\)|)",H=new RegExp(D+"+","g"),R=new RegExp("^"+D+"*,"+D+"*"),F=new RegExp("^"+D+"*([>+~]|"+D+")"+D+"*"),q=new RegExp(D+"|>"),W=new RegExp($),B=new RegExp("^"+N+"$"),z={ID:new RegExp("^#("+N+")"),CLASS:new RegExp("^\\.("+N+")"),TAG:new RegExp("^("+N+"|[*])"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+$),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+D+"*(even|odd|(([+-]|)(\\d*)n|)"+D+"*(?:([+-]|)"+D+"*(\\d+)|))"+D+"*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^"+D+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+D+"*((?:-\\d)?\\d*)"+D+"*\\)|)(?=[^-]|$)","i")},V=/^(?:input|select|textarea|button)$/i,U=/^h\d$/i,X=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Y=/[+~]/,G=new RegExp("\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\([^\\r\\n\\f])","g"),K=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},Q=function(){ct()},J=dt(function(t){return!0===t.disabled&&C(t,"fieldset")},{dir:"parentNode",next:"legend"});try{g.apply(o=a.call(I.childNodes),I.childNodes),o[I.childNodes.length].nodeType}catch(t){g={apply:function(t,e){M.apply(t,a.call(e))},call:function(t){M.apply(t,a.call(arguments,1))}}}function Z(t,e,n,r){var i,o,s,a,l,u,p,h=e&&e.ownerDocument,y=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==y&&9!==y&&11!==y)return n;if(!r&&(ct(e),e=e||c,f)){if(11!==y&&(l=X.exec(t)))if(i=l[1]){if(9===y){if(!(s=e.getElementById(i)))return n;if(s.id===i)return g.call(n,s),n}else if(h&&(s=h.getElementById(i))&&Z.contains(e,s)&&s.id===i)return g.call(n,s),n}else{if(l[2])return g.apply(n,e.getElementsByTagName(t)),n;if((i=l[3])&&e.getElementsByClassName)return g.apply(n,e.getElementsByClassName(i)),n}if(!(T[t+" "]||d&&d.test(t))){if(p=t,h=e,1===y&&(q.test(t)||F.test(t))){for((h=Y.test(t)&&at(e.parentNode)||e)==e&&m.scope||((a=e.getAttribute("id"))?a=A.escapeSelector(a):e.setAttribute("id",a=v)),o=(u=ut(t)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+ft(u[o]);p=u.join(",")}try{return g.apply(n,h.querySelectorAll(p)),n}catch(e){T(t,!0)}finally{a===v&&e.removeAttribute("id")}}}return yt(t.replace(L,"$1"),e,n,r)}function tt(){var t=[];return function n(r,i){return t.push(r+" ")>e.cacheLength&&delete n[t.shift()],n[r+" "]=i}}function et(t){return t[v]=!0,t}function nt(t){var e=c.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function rt(t){return function(e){return C(e,"input")&&e.type===t}}function it(t){return function(e){return(C(e,"input")||C(e,"button"))&&e.type===t}}function ot(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&J(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function st(t){return et(function(e){return e=+e,et(function(n,r){for(var i,o=t([],n.length,e),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))})})}function at(t){return t&&void 0!==t.getElementsByTagName&&t}function ct(t){var n,r=t?t.ownerDocument||t:I;return r!=c&&9===r.nodeType&&r.documentElement?(l=(c=r).documentElement,f=!A.isXMLDoc(c),h=l.matches||l.webkitMatchesSelector||l.msMatchesSelector,l.msMatchesSelector&&I!=c&&(n=c.defaultView)&&n.top!==n&&n.addEventListener("unload",Q),m.getById=nt(function(t){return l.appendChild(t).id=A.expando,!c.getElementsByName||!c.getElementsByName(A.expando).length}),m.disconnectedMatch=nt(function(t){return h.call(t,"*")}),m.scope=nt(function(){return c.querySelectorAll(":scope")}),m.cssHas=nt(function(){try{return c.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}}),m.getById?(e.filter.ID=function(t){var e=t.replace(G,K);return function(t){return t.getAttribute("id")===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&f){var n=e.getElementById(t);return n?[n]:[]}}):(e.filter.ID=function(t){var e=t.replace(G,K);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},e.find.ID=function(t,e){if(void 0!==e.getElementById&&f){var n,r,i,o=e.getElementById(t);if(o){if((n=o.getAttributeNode("id"))&&n.value===t)return[o];for(i=e.getElementsByName(t),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===t)return[o]}return[]}}),e.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},e.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&f)return e.getElementsByClassName(t)},d=[],nt(function(t){var e;l.appendChild(t).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||d.push("\\["+D+"*(?:value|"+S+")"),t.querySelectorAll("[id~="+v+"-]").length||d.push("~="),t.querySelectorAll("a#"+v+"+*").length||d.push(".#.+[+~]"),t.querySelectorAll(":checked").length||d.push(":checked"),(e=c.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),l.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&d.push(":enabled",":disabled"),(e=c.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||d.push("\\["+D+"*name"+D+"*="+D+"*(?:''|\"\")")}),m.cssHas||d.push(":has"),d=d.length&&new RegExp(d.join("|")),E=function(t,e){if(t===e)return s=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!m.sortDetached&&e.compareDocumentPosition(t)===n?t===c||t.ownerDocument==I&&Z.contains(I,t)?-1:e===c||e.ownerDocument==I&&Z.contains(I,e)?1:i?u.call(i,t)-u.call(i,e):0:4&n?-1:1)},c):c}for(t in Z.matches=function(t,e){return Z(t,null,null,e)},Z.matchesSelector=function(t,e){if(ct(t),f&&!T[e+" "]&&(!d||!d.test(e)))try{var n=h.call(t,e);if(n||m.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){T(e,!0)}return Z(e,c,null,[t]).length>0},Z.contains=function(t,e){return(t.ownerDocument||t)!=c&&ct(t),A.contains(t,e)},Z.attr=function(t,n){(t.ownerDocument||t)!=c&&ct(t);var r=e.attrHandle[n.toLowerCase()],i=r&&p.call(e.attrHandle,n.toLowerCase())?r(t,n,!f):void 0;return void 0!==i?i:t.getAttribute(n)},Z.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},A.uniqueSort=function(t){var e,n=[],r=0,o=0;if(s=!m.sortStable,i=!m.sortStable&&a.call(t,0),k.call(t,E),s){for(;e=t[o++];)e===t[o]&&(r=n.push(o));for(;r--;)j.call(t,n[r],1)}return i=null,t},A.fn.uniqueSort=function(){return this.pushStack(A.uniqueSort(a.apply(this)))},e=A.expr={cacheLength:50,createPseudo:et,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(G,K),t[3]=(t[3]||t[4]||t[5]||"").replace(G,K),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||Z.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&Z.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return z.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&W.test(n)&&(e=ut(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(G,K).toLowerCase();return"*"===t?function(){return!0}:function(t){return C(t,e)}},CLASS:function(t){var e=_[t+" "];return e||(e=new RegExp("(^|"+D+")"+t+"("+D+"|$)"))&&_(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,n){return function(r){var i=Z.attr(r,t);return null==i?"!="===e:!e||(i+="","="===e?i===n:"!="===e?i!==n:"^="===e?n&&0===i.indexOf(n):"*="===e?n&&i.indexOf(n)>-1:"$="===e?n&&i.slice(-n.length)===n:"~="===e?(" "+i.replace(H," ")+" ").indexOf(n)>-1:"|="===e&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,r,i){var o="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===r&&0===i?function(t){return!!t.parentNode}:function(e,n,c){var l,u,f,d,p,h=o!==s?"nextSibling":"previousSibling",g=e.parentNode,m=a&&e.nodeName.toLowerCase(),b=!c&&!a,_=!1;if(g){if(o){for(;h;){for(f=e;f=f[h];)if(a?C(f,m):1===f.nodeType)return!1;p=h="only"===t&&!p&&"nextSibling"}return!0}if(p=[s?g.firstChild:g.lastChild],s&&b){for(_=(d=(l=(u=g[v]||(g[v]={}))[t]||[])[0]===y&&l[1])&&l[2],f=d&&g.childNodes[d];f=++d&&f&&f[h]||(_=d=0)||p.pop();)if(1===f.nodeType&&++_&&f===e){u[t]=[y,d,_];break}}else if(b&&(_=d=(l=(u=e[v]||(e[v]={}))[t]||[])[0]===y&&l[1]),!1===_)for(;(f=++d&&f&&f[h]||(_=d=0)||p.pop())&&(!(a?C(f,m):1===f.nodeType)||!++_||(b&&((u=f[v]||(f[v]={}))[t]=[y,_]),f!==e)););return(_-=i)===r||_%r===0&&_/r>=0}}},PSEUDO:function(t,n){var r,i=e.pseudos[t]||e.setFilters[t.toLowerCase()]||Z.error("unsupported pseudo: "+t);return i[v]?i(n):i.length>1?(r=[t,t,"",n],e.setFilters.hasOwnProperty(t.toLowerCase())?et(function(t,e){for(var r,o=i(t,n),s=o.length;s--;)t[r=u.call(t,o[s])]=!(e[r]=o[s])}):function(t){return i(t,0,r)}):i}},pseudos:{not:et(function(t){var e=[],n=[],r=vt(t.replace(L,"$1"));return r[v]?et(function(t,e,n,i){for(var o,s=r(t,null,i,[]),a=t.length;a--;)(o=s[a])&&(t[a]=!(e[a]=o))}):function(t,i,o){return e[0]=t,r(e,null,o,n),e[0]=null,!n.pop()}}),has:et(function(t){return function(e){return Z(t,e).length>0}}),contains:et(function(t){return t=t.replace(G,K),function(e){return(e.textContent||A.text(e)).indexOf(t)>-1}}),lang:et(function(t){return B.test(t||"")||Z.error("unsupported lang: "+t),t=t.replace(G,K).toLowerCase(),function(e){var n;do{if(n=f?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(t){var e=r.location&&r.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===l},focus:function(t){return t===function(){try{return c.activeElement}catch(t){}}()&&c.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:ot(!1),disabled:ot(!0),checked:function(t){return C(t,"input")&&!!t.checked||C(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!e.pseudos.empty(t)},header:function(t){return U.test(t.nodeName)},input:function(t){return V.test(t.nodeName)},button:function(t){return C(t,"input")&&"button"===t.type||C(t,"button")},text:function(t){var e;return C(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:st(function(){return[0]}),last:st(function(t,e){return[e-1]}),eq:st(function(t,e,n){return[n<0?n+e:n]}),even:st(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:st(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:st(function(t,e,n){var r;for(r=n<0?n+e:n>e?e:n;--r>=0;)t.push(r);return t}),gt:st(function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t})}},e.pseudos.nth=e.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})e.pseudos[t]=rt(t);for(t in{submit:!0,reset:!0})e.pseudos[t]=it(t);function lt(){}function ut(t,n){var r,i,o,s,a,c,l,u=x[t+" "];if(u)return n?0:u.slice(0);for(a=t,c=[],l=e.preFilter;a;){for(s in r&&!(i=R.exec(a))||(i&&(a=a.slice(i[0].length)||a),c.push(o=[])),r=!1,(i=F.exec(a))&&(r=i.shift(),o.push({value:r,type:i[0].replace(L," ")}),a=a.slice(r.length)),e.filter)!(i=z[s].exec(a))||l[s]&&!(i=l[s](i))||(r=i.shift(),o.push({value:r,type:s,matches:i}),a=a.slice(r.length));if(!r)break}return n?a.length:a?Z.error(t):x(t,c).slice(0)}function ft(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function dt(t,e,n){var r=e.dir,i=e.next,o=i||r,s=n&&"parentNode"===o,a=b++;return e.first?function(e,n,i){for(;e=e[r];)if(1===e.nodeType||s)return t(e,n,i);return!1}:function(e,n,c){var l,u,f=[y,a];if(c){for(;e=e[r];)if((1===e.nodeType||s)&&t(e,n,c))return!0}else for(;e=e[r];)if(1===e.nodeType||s)if(u=e[v]||(e[v]={}),i&&C(e,i))e=e[r]||e;else{if((l=u[o])&&l[0]===y&&l[1]===a)return f[2]=l[2];if(u[o]=f,f[2]=t(e,n,c))return!0}return!1}}function pt(t){return t.length>1?function(e,n,r){for(var i=t.length;i--;)if(!t[i](e,n,r))return!1;return!0}:t[0]}function ht(t,e,n,r,i){for(var o,s=[],a=0,c=t.length,l=null!=e;a<c;a++)(o=t[a])&&(n&&!n(o,r,i)||(s.push(o),l&&e.push(a)));return s}function gt(t,e,n,r,i,o){return r&&!r[v]&&(r=gt(r)),i&&!i[v]&&(i=gt(i,o)),et(function(o,s,a,c){var l,f,d,p,h=[],m=[],v=s.length,y=o||function(t,e,n){for(var r=0,i=e.length;r<i;r++)Z(t,e[r],n);return n}(e||"*",a.nodeType?[a]:a,[]),b=!t||!o&&e?y:ht(y,h,t,a,c);if(n?n(b,p=i||(o?t:v||r)?[]:s,a,c):p=b,r)for(l=ht(p,m),r(l,[],a,c),f=l.length;f--;)(d=l[f])&&(p[m[f]]=!(b[m[f]]=d));if(o){if(i||t){if(i){for(l=[],f=p.length;f--;)(d=p[f])&&l.push(b[f]=d);i(null,p=[],l,c)}for(f=p.length;f--;)(d=p[f])&&(l=i?u.call(o,d):h[f])>-1&&(o[l]=!(s[l]=d))}}else p=ht(p===s?p.splice(v,p.length):p),i?i(null,s,p,c):g.apply(s,p)})}function mt(t){for(var r,i,o,s=t.length,a=e.relative[t[0].type],c=a||e.relative[" "],l=a?1:0,f=dt(function(t){return t===r},c,!0),d=dt(function(t){return u.call(r,t)>-1},c,!0),p=[function(t,e,i){var o=!a&&(i||e!=n)||((r=e).nodeType?f(t,e,i):d(t,e,i));return r=null,o}];l<s;l++)if(i=e.relative[t[l].type])p=[dt(pt(p),i)];else{if((i=e.filter[t[l].type].apply(null,t[l].matches))[v]){for(o=++l;o<s&&!e.relative[t[o].type];o++);return gt(l>1&&pt(p),l>1&&ft(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(L,"$1"),i,l<o&&mt(t.slice(l,o)),o<s&&mt(t=t.slice(o)),o<s&&ft(t))}p.push(i)}return pt(p)}function vt(t,r){var i,o=[],s=[],a=w[t+" "];if(!a){for(r||(r=ut(t)),i=r.length;i--;)(a=mt(r[i]))[v]?o.push(a):s.push(a);a=w(t,function(t,r){var i=r.length>0,o=t.length>0,s=function(s,a,l,u,d){var p,h,m,v=0,b="0",_=s&&[],x=[],w=n,T=s||o&&e.find.TAG("*",d),E=y+=null==w?1:Math.random()||.1,S=T.length;for(d&&(n=a==c||a||d);b!==S&&null!=(p=T[b]);b++){if(o&&p){for(h=0,a||p.ownerDocument==c||(ct(p),l=!f);m=t[h++];)if(m(p,a||c,l)){g.call(u,p);break}d&&(y=E)}i&&((p=!m&&p)&&v--,s&&_.push(p))}if(v+=b,i&&b!==v){for(h=0;m=r[h++];)m(_,x,a,l);if(s){if(v>0)for(;b--;)_[b]||x[b]||(x[b]=O.call(u));x=ht(x)}g.apply(u,x),d&&!s&&x.length>0&&v+r.length>1&&A.uniqueSort(u)}return d&&(y=E,n=w),_};return i?et(s):s}(s,o)),a.selector=t}return a}function yt(t,n,r,i){var o,s,a,c,l,u="function"==typeof t&&t,d=!i&&ut(t=u.selector||t);if(r=r||[],1===d.length){if((s=d[0]=d[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===n.nodeType&&f&&e.relative[s[1].type]){if(!(n=(e.find.ID(a.matches[0].replace(G,K),n)||[])[0]))return r;u&&(n=n.parentNode),t=t.slice(s.shift().value.length)}for(o=z.needsContext.test(t)?0:s.length;o--&&(a=s[o],!e.relative[c=a.type]);)if((l=e.find[c])&&(i=l(a.matches[0].replace(G,K),Y.test(s[0].type)&&at(n.parentNode)||n))){if(s.splice(o,1),!(t=i.length&&ft(s)))return g.apply(r,i),r;break}}return(u||vt(t,d))(i,n,!f,r,!n||Y.test(t)&&at(n.parentNode)||n),r}lt.prototype=e.filters=e.pseudos,e.setFilters=new lt,m.sortStable=v.split("").sort(E).join("")===v,ct(),m.sortDetached=nt(function(t){return 1&t.compareDocumentPosition(c.createElement("fieldset"))}),A.find=Z,A.expr[":"]=A.expr.pseudos,A.unique=A.uniqueSort,Z.compile=vt,Z.select=yt,Z.setDocument=ct,Z.tokenize=ut,Z.escape=A.escapeSelector,Z.getText=A.text,Z.isXML=A.isXMLDoc,Z.selectors=A.expr,Z.support=A.support,Z.uniqueSort=A.uniqueSort}();var $=function(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&A(t).is(n))break;r.push(t)}return r},H=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},R=A.expr.match.needsContext,F=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function q(t,e,n){return v(e)?A.grep(t,function(t,r){return!!e.call(t,r,t)!==n}):e.nodeType?A.grep(t,function(t){return t===e!==n}):"string"!=typeof e?A.grep(t,function(t){return u.call(e,t)>-1!==n}):A.filter(e,t,n)}A.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?A.find.matchesSelector(r,t)?[r]:[]:A.find.matches(t,A.grep(e,function(t){return 1===t.nodeType}))},A.fn.extend({find:function(t){var e,n,r=this.length,i=this;if("string"!=typeof t)return this.pushStack(A(t).filter(function(){for(e=0;e<r;e++)if(A.contains(i[e],this))return!0}));for(n=this.pushStack([]),e=0;e<r;e++)A.find(t,i[e],n);return r>1?A.uniqueSort(n):n},filter:function(t){return this.pushStack(q(this,t||[],!1))},not:function(t){return this.pushStack(q(this,t||[],!0))},is:function(t){return!!q(this,"string"==typeof t&&R.test(t)?A(t):t||[],!1).length}});var W,B=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(A.fn.init=function(t,e,n){var r,i;if(!t)return this;if(n=n||W,"string"==typeof t){if(!(r="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:B.exec(t))||!r[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(r[1]){if(e=e instanceof A?e[0]:e,A.merge(this,A.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:b,!0)),F.test(r[1])&&A.isPlainObject(e))for(r in e)v(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==n.ready?n.ready(t):t(A):A.makeArray(t,this)}).prototype=A.fn,W=A(b);var z=/^(?:parents|prev(?:Until|All))/,V={children:!0,contents:!0,next:!0,prev:!0};function U(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}A.fn.extend({has:function(t){var e=A(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(A.contains(this,e[t]))return!0})},closest:function(t,e){var n,r=0,i=this.length,o=[],s="string"!=typeof t&&A(t);if(!R.test(t))for(;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&A.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(o.length>1?A.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?u.call(A(t),this[0]):u.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(A.uniqueSort(A.merge(this.get(),A(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),A.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return $(t,"parentNode")},parentsUntil:function(t,e,n){return $(t,"parentNode",n)},next:function(t){return U(t,"nextSibling")},prev:function(t){return U(t,"previousSibling")},nextAll:function(t){return $(t,"nextSibling")},prevAll:function(t){return $(t,"previousSibling")},nextUntil:function(t,e,n){return $(t,"nextSibling",n)},prevUntil:function(t,e,n){return $(t,"previousSibling",n)},siblings:function(t){return H((t.parentNode||{}).firstChild,t)},children:function(t){return H(t.firstChild)},contents:function(t){return null!=t.contentDocument&&s(t.contentDocument)?t.contentDocument:(C(t,"template")&&(t=t.content||t),A.merge([],t.childNodes))}},function(t,e){A.fn[t]=function(n,r){var i=A.map(this,e,n);return"Until"!==t.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=A.filter(r,i)),this.length>1&&(V[t]||A.uniqueSort(i),z.test(t)&&i.reverse()),this.pushStack(i)}});var X=/[^\x20\t\r\n\f]+/g;function Y(t){return t}function G(t){throw t}function K(t,e,n,r){var i;try{t&&v(i=t.promise)?i.call(t).done(e).fail(n):t&&v(i=t.then)?i.call(t,e,n):e.apply(void 0,[t].slice(r))}catch(t){n.apply(void 0,[t])}}A.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return A.each(t.match(X)||[],function(t,n){e[n]=!0}),e}(t):A.extend({},t);var e,n,r,i,o=[],s=[],a=-1,c=function(){for(i=i||t.once,r=e=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&t.stopOnFalse&&(a=o.length,n=!1);t.memory||(n=!1),e=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!e&&(a=o.length-1,s.push(n)),function e(n){A.each(n,function(n,r){v(r)?t.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==w(r)&&e(r)})}(arguments),n&&!e&&c()),this},remove:function(){return A.each(arguments,function(t,e){for(var n;(n=A.inArray(e,o,n))>-1;)o.splice(n,1),n<=a&&a--}),this},has:function(t){return t?A.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||e||(o=n=""),this},locked:function(){return!!i},fireWith:function(t,n){return i||(n=[t,(n=n||[]).slice?n.slice():n],s.push(n),e||c()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},A.extend({Deferred:function(t){var e=[["notify","progress",A.Callbacks("memory"),A.Callbacks("memory"),2],["resolve","done",A.Callbacks("once memory"),A.Callbacks("once memory"),0,"resolved"],["reject","fail",A.Callbacks("once memory"),A.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return i.then(null,t)},pipe:function(){var t=arguments;return A.Deferred(function(n){A.each(e,function(e,r){var i=v(t[r[4]])&&t[r[4]];o[r[1]](function(){var t=i&&i.apply(this,arguments);t&&v(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[t]:arguments)})}),t=null}).promise()},then:function(t,n,i){var o=0;function s(t,e,n,i){return function(){var a=this,c=arguments,l=function(){var r,l;if(!(t<o)){if((r=n.apply(a,c))===e.promise())throw new TypeError("Thenable self-resolution");l=r&&("object"==typeof r||"function"==typeof r)&&r.then,v(l)?i?l.call(r,s(o,e,Y,i),s(o,e,G,i)):(o++,l.call(r,s(o,e,Y,i),s(o,e,G,i),s(o,e,Y,e.notifyWith))):(n!==Y&&(a=void 0,c=[r]),(i||e.resolveWith)(a,c))}},u=i?l:function(){try{l()}catch(r){A.Deferred.exceptionHook&&A.Deferred.exceptionHook(r,u.error),t+1>=o&&(n!==G&&(a=void 0,c=[r]),e.rejectWith(a,c))}};t?u():(A.Deferred.getErrorHook?u.error=A.Deferred.getErrorHook():A.Deferred.getStackHook&&(u.error=A.Deferred.getStackHook()),r.setTimeout(u))}}return A.Deferred(function(r){e[0][3].add(s(0,r,v(i)?i:Y,r.notifyWith)),e[1][3].add(s(0,r,v(t)?t:Y)),e[2][3].add(s(0,r,v(n)?n:G))}).promise()},promise:function(t){return null!=t?A.extend(t,i):i}},o={};return A.each(e,function(t,r){var s=r[2],a=r[5];i[r[1]]=s.add,a&&s.add(function(){n=a},e[3-t][2].disable,e[3-t][3].disable,e[0][2].lock,e[0][3].lock),s.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=s.fireWith}),i.promise(o),t&&t.call(o,o),o},when:function(t){var e=arguments.length,n=e,r=Array(n),i=a.call(arguments),o=A.Deferred(),s=function(t){return function(n){r[t]=this,i[t]=arguments.length>1?a.call(arguments):n,--e||o.resolveWith(r,i)}};if(e<=1&&(K(t,o.done(s(n)).resolve,o.reject,!e),"pending"===o.state()||v(i[n]&&i[n].then)))return o.then();for(;n--;)K(i[n],s(n),o.reject);return o.promise()}});var Q=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;A.Deferred.exceptionHook=function(t,e){r.console&&r.console.warn&&t&&Q.test(t.name)&&r.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},A.readyException=function(t){r.setTimeout(function(){throw t})};var J=A.Deferred();function Z(){b.removeEventListener("DOMContentLoaded",Z),r.removeEventListener("load",Z),A.ready()}A.fn.ready=function(t){return J.then(t).catch(function(t){A.readyException(t)}),this},A.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--A.readyWait:A.isReady)||(A.isReady=!0,!0!==t&&--A.readyWait>0||J.resolveWith(b,[A]))}}),A.ready.then=J.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(A.ready):(b.addEventListener("DOMContentLoaded",Z),r.addEventListener("load",Z));var tt=function(t,e,n,r,i,o,s){var a=0,c=t.length,l=null==n;if("object"===w(n))for(a in i=!0,n)tt(t,e,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,v(r)||(s=!0),l&&(s?(e.call(t,r),e=null):(l=e,e=function(t,e,n){return l.call(A(t),n)})),e))for(;a<c;a++)e(t[a],n,s?r:r.call(t[a],a,e(t[a],n)));return i?t:l?e.call(t):c?e(t[0],n):o},et=/^-ms-/,nt=/-([a-z])/g;function rt(t,e){return e.toUpperCase()}function it(t){return t.replace(et,"ms-").replace(nt,rt)}var ot=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function st(){this.expando=A.expando+st.uid++}st.uid=1,st.prototype={cache:function(t){var e=t[this.expando];return e||(e={},ot(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[it(e)]=n;else for(r in e)i[it(r)]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][it(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r=t[this.expando];if(void 0!==r){if(void 0!==e){n=(e=Array.isArray(e)?e.map(it):(e=it(e))in r?[e]:e.match(X)||[]).length;for(;n--;)delete r[e[n]]}(void 0===e||A.isEmptyObject(r))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!A.isEmptyObject(e)}};var at=new st,ct=new st,lt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ut=/[A-Z]/g;function ft(t,e,n){var r;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(ut,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:lt.test(t)?JSON.parse(t):t)}(n)}catch(t){}ct.set(t,e,n)}else n=void 0;return n}A.extend({hasData:function(t){return ct.hasData(t)||at.hasData(t)},data:function(t,e,n){return ct.access(t,e,n)},removeData:function(t,e){ct.remove(t,e)},_data:function(t,e,n){return at.access(t,e,n)},_removeData:function(t,e){at.remove(t,e)}}),A.fn.extend({data:function(t,e){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===t){if(this.length&&(i=ct.get(o),1===o.nodeType&&!at.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=it(r.slice(5)),ft(o,r,i[r]));at.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof t?this.each(function(){ct.set(this,t)}):tt(this,function(e){var n;if(o&&void 0===e)return void 0!==(n=ct.get(o,t))||void 0!==(n=ft(o,t))?n:void 0;this.each(function(){ct.set(this,t,e)})},null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each(function(){ct.remove(this,t)})}}),A.extend({queue:function(t,e,n){var r;if(t)return e=(e||"fx")+"queue",r=at.get(t,e),n&&(!r||Array.isArray(n)?r=at.access(t,e,A.makeArray(n)):r.push(n)),r||[]},dequeue:function(t,e){e=e||"fx";var n=A.queue(t,e),r=n.length,i=n.shift(),o=A._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,function(){A.dequeue(t,e)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return at.get(t,n)||at.access(t,n,{empty:A.Callbacks("once memory").add(function(){at.remove(t,[e+"queue",n])})})}}),A.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?A.queue(this[0],t):void 0===e?this:this.each(function(){var n=A.queue(this,t,e);A._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&A.dequeue(this,t)})},dequeue:function(t){return this.each(function(){A.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,r=1,i=A.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(n=at.get(o[s],t+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(e)}});var dt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pt=new RegExp("^(?:([+-])=|)("+dt+")([a-z%]*)$","i"),ht=["Top","Right","Bottom","Left"],gt=b.documentElement,mt=function(t){return A.contains(t.ownerDocument,t)},vt={composed:!0};gt.getRootNode&&(mt=function(t){return A.contains(t.ownerDocument,t)||t.getRootNode(vt)===t.ownerDocument});var yt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&mt(t)&&"none"===A.css(t,"display")};function bt(t,e,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return A.css(t,e,"")},c=a(),l=n&&n[3]||(A.cssNumber[e]?"":"px"),u=t.nodeType&&(A.cssNumber[e]||"px"!==l&&+c)&&pt.exec(A.css(t,e));if(u&&u[3]!==l){for(c/=2,l=l||u[3],u=+c||1;s--;)A.style(t,e,u+l),(1-o)*(1-(o=a()/c||.5))<=0&&(s=0),u/=o;u*=2,A.style(t,e,u+l),n=n||[]}return n&&(u=+u||+c||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=u,r.end=i)),i}var _t={};function xt(t){var e,n=t.ownerDocument,r=t.nodeName,i=_t[r];return i||(e=n.body.appendChild(n.createElement(r)),i=A.css(e,"display"),e.parentNode.removeChild(e),"none"===i&&(i="block"),_t[r]=i,i)}function wt(t,e){for(var n,r,i=[],o=0,s=t.length;o<s;o++)(r=t[o]).style&&(n=r.style.display,e?("none"===n&&(i[o]=at.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&yt(r)&&(i[o]=xt(r))):"none"!==n&&(i[o]="none",at.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(t[o].style.display=i[o]);return t}A.fn.extend({show:function(){return wt(this,!0)},hide:function(){return wt(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){yt(this)?A(this).show():A(this).hide()})}});var Tt,Et,At=/^(?:checkbox|radio)$/i,St=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ct=/^$|^module$|\/(?:java|ecma)script/i;Tt=b.createDocumentFragment().appendChild(b.createElement("div")),(Et=b.createElement("input")).setAttribute("type","radio"),Et.setAttribute("checked","checked"),Et.setAttribute("name","t"),Tt.appendChild(Et),m.checkClone=Tt.cloneNode(!0).cloneNode(!0).lastChild.checked,Tt.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Tt.cloneNode(!0).lastChild.defaultValue,Tt.innerHTML="<option></option>",m.option=!!Tt.lastChild;var Ot={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function kt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&C(t,e)?A.merge([t],n):n}function jt(t,e){for(var n=0,r=t.length;n<r;n++)at.set(t[n],"globalEval",!e||at.get(e[n],"globalEval"))}Ot.tbody=Ot.tfoot=Ot.colgroup=Ot.caption=Ot.thead,Ot.th=Ot.td,m.option||(Ot.optgroup=Ot.option=[1,"<select multiple='multiple'>","</select>"]);var Dt=/<|&#?\w+;/;function Lt(t,e,n,r,i){for(var o,s,a,c,l,u,f=e.createDocumentFragment(),d=[],p=0,h=t.length;p<h;p++)if((o=t[p])||0===o)if("object"===w(o))A.merge(d,o.nodeType?[o]:o);else if(Dt.test(o)){for(s=s||f.appendChild(e.createElement("div")),a=(St.exec(o)||["",""])[1].toLowerCase(),c=Ot[a]||Ot._default,s.innerHTML=c[1]+A.htmlPrefilter(o)+c[2],u=c[0];u--;)s=s.lastChild;A.merge(d,s.childNodes),(s=f.firstChild).textContent=""}else d.push(e.createTextNode(o));for(f.textContent="",p=0;o=d[p++];)if(r&&A.inArray(o,r)>-1)i&&i.push(o);else if(l=mt(o),s=kt(f.appendChild(o),"script"),l&&jt(s),n)for(u=0;o=s[u++];)Ct.test(o.type||"")&&n.push(o);return f}var Nt=/^([^.]*)(?:\.(.+)|)/;function Pt(){return!0}function It(){return!1}function Mt(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)Mt(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=It;else if(!i)return t;return 1===o&&(s=i,i=function(t){return A().off(t),s.apply(this,arguments)},i.guid=s.guid||(s.guid=A.guid++)),t.each(function(){A.event.add(this,e,i,r,n)})}function $t(t,e,n){n?(at.set(t,e,!1),A.event.add(t,e,{namespace:!1,handler:function(t){var n,r=at.get(this,e);if(1&t.isTrigger&&this[e]){if(r)(A.event.special[e]||{}).delegateType&&t.stopPropagation();else if(r=a.call(arguments),at.set(this,e,r),this[e](),n=at.get(this,e),at.set(this,e,!1),r!==n)return t.stopImmediatePropagation(),t.preventDefault(),n}else r&&(at.set(this,e,A.event.trigger(r[0],r.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Pt)}})):void 0===at.get(t,e)&&A.event.add(t,e,Pt)}A.event={global:{},add:function(t,e,n,r,i){var o,s,a,c,l,u,f,d,p,h,g,m=at.get(t);if(ot(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&A.find.matchesSelector(gt,i),n.guid||(n.guid=A.guid++),(c=m.events)||(c=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==A&&A.event.triggered!==e.type?A.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(X)||[""]).length;l--;)p=g=(a=Nt.exec(e[l])||[])[1],h=(a[2]||"").split(".").sort(),p&&(f=A.event.special[p]||{},p=(i?f.delegateType:f.bindType)||p,f=A.event.special[p]||{},u=A.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&A.expr.match.needsContext.test(i),namespace:h.join(".")},o),(d=c[p])||((d=c[p]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,r,h,s)||t.addEventListener&&t.addEventListener(p,s)),f.add&&(f.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,u):d.push(u),A.event.global[p]=!0)},remove:function(t,e,n,r,i){var o,s,a,c,l,u,f,d,p,h,g,m=at.hasData(t)&&at.get(t);if(m&&(c=m.events)){for(l=(e=(e||"").match(X)||[""]).length;l--;)if(p=g=(a=Nt.exec(e[l])||[])[1],h=(a[2]||"").split(".").sort(),p){for(f=A.event.special[p]||{},d=c[p=(r?f.delegateType:f.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)u=d[o],!i&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(d.splice(o,1),u.selector&&d.delegateCount--,f.remove&&f.remove.call(t,u));s&&!d.length&&(f.teardown&&!1!==f.teardown.call(t,h,m.handle)||A.removeEvent(t,p,m.handle),delete c[p])}else for(p in c)A.event.remove(t,p+e[l],n,r,!0);A.isEmptyObject(c)&&at.remove(t,"handle events")}},dispatch:function(t){var e,n,r,i,o,s,a=new Array(arguments.length),c=A.event.fix(t),l=(at.get(this,"events")||Object.create(null))[c.type]||[],u=A.event.special[c.type]||{};for(a[0]=c,e=1;e<arguments.length;e++)a[e]=arguments[e];if(c.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,c)){for(s=A.event.handlers.call(this,c,l),e=0;(i=s[e++])&&!c.isPropagationStopped();)for(c.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!c.isImmediatePropagationStopped();)c.rnamespace&&!1!==o.namespace&&!c.rnamespace.test(o.namespace)||(c.handleObj=o,c.data=o.data,void 0!==(r=((A.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(c.result=r)&&(c.preventDefault(),c.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,c),c.result}},handlers:function(t,e){var n,r,i,o,s,a=[],c=e.delegateCount,l=t.target;if(c&&l.nodeType&&!("click"===t.type&&t.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==t.type||!0!==l.disabled)){for(o=[],s={},n=0;n<c;n++)void 0===s[i=(r=e[n]).selector+" "]&&(s[i]=r.needsContext?A(i,this).index(l)>-1:A.find(i,this,null,[l]).length),s[i]&&o.push(r);o.length&&a.push({elem:l,handlers:o})}return l=this,c<e.length&&a.push({elem:l,handlers:e.slice(c)}),a},addProp:function(t,e){Object.defineProperty(A.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[A.expando]?t:new A.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return At.test(e.type)&&e.click&&C(e,"input")&&$t(e,"click",!0),!1},trigger:function(t){var e=this||t;return At.test(e.type)&&e.click&&C(e,"input")&&$t(e,"click"),!0},_default:function(t){var e=t.target;return At.test(e.type)&&e.click&&C(e,"input")&&at.get(e,"click")||C(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},A.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},A.Event=function(t,e){if(!(this instanceof A.Event))return new A.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Pt:It,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&A.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[A.expando]=!0},A.Event.prototype={constructor:A.Event,isDefaultPrevented:It,isPropagationStopped:It,isImmediatePropagationStopped:It,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Pt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Pt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Pt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},A.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},A.event.addProp),A.each({focus:"focusin",blur:"focusout"},function(t,e){function n(t){if(b.documentMode){var n=at.get(this,"handle"),r=A.event.fix(t);r.type="focusin"===t.type?"focus":"blur",r.isSimulated=!0,n(t),r.target===r.currentTarget&&n(r)}else A.event.simulate(e,t.target,A.event.fix(t))}A.event.special[t]={setup:function(){var r;if($t(this,t,!0),!b.documentMode)return!1;(r=at.get(this,e))||this.addEventListener(e,n),at.set(this,e,(r||0)+1)},trigger:function(){return $t(this,t),!0},teardown:function(){var t;if(!b.documentMode)return!1;(t=at.get(this,e)-1)?at.set(this,e,t):(this.removeEventListener(e,n),at.remove(this,e))},_default:function(e){return at.get(e.target,t)},delegateType:e},A.event.special[e]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=at.get(i,e);o||(b.documentMode?this.addEventListener(e,n):r.addEventListener(t,n,!0)),at.set(i,e,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=at.get(i,e)-1;o?at.set(i,e,o):(b.documentMode?this.removeEventListener(e,n):r.removeEventListener(t,n,!0),at.remove(i,e))}}}),A.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){A.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,r=t.relatedTarget,i=t.handleObj;return r&&(r===this||A.contains(this,r))||(t.type=i.origType,n=i.handler.apply(this,arguments),t.type=e),n}}}),A.fn.extend({on:function(t,e,n,r){return Mt(this,t,e,n,r)},one:function(t,e,n,r){return Mt(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,A(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof t){for(i in t)this.off(i,e,t[i]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=It),this.each(function(){A.event.remove(this,t,n,e)})}});var Ht=/<script|<style|<link/i,Rt=/checked\s*(?:[^=]|=\s*.checked.)/i,Ft=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function qt(t,e){return C(t,"table")&&C(11!==e.nodeType?e:e.firstChild,"tr")&&A(t).children("tbody")[0]||t}function Wt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Bt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function zt(t,e){var n,r,i,o,s,a;if(1===e.nodeType){if(at.hasData(t)&&(a=at.get(t).events))for(i in at.remove(e,"handle events"),a)for(n=0,r=a[i].length;n<r;n++)A.event.add(e,i,a[i][n]);ct.hasData(t)&&(o=ct.access(t),s=A.extend({},o),ct.set(e,s))}}function Vt(t,e){var n=e.nodeName.toLowerCase();"input"===n&&At.test(t.type)?e.checked=t.checked:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}function Ut(t,e,n,r){e=c(e);var i,o,s,a,l,u,f=0,d=t.length,p=d-1,h=e[0],g=v(h);if(g||d>1&&"string"==typeof h&&!m.checkClone&&Rt.test(h))return t.each(function(i){var o=t.eq(i);g&&(e[0]=h.call(this,i,o.html())),Ut(o,e,n,r)});if(d&&(o=(i=Lt(e,t[0].ownerDocument,!1,t,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=A.map(kt(i,"script"),Wt)).length;f<d;f++)l=i,f!==p&&(l=A.clone(l,!0,!0),a&&A.merge(s,kt(l,"script"))),n.call(t[f],l,f);if(a)for(u=s[s.length-1].ownerDocument,A.map(s,Bt),f=0;f<a;f++)l=s[f],Ct.test(l.type||"")&&!at.access(l,"globalEval")&&A.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?A._evalUrl&&!l.noModule&&A._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):x(l.textContent.replace(Ft,""),l,u))}return t}function Xt(t,e,n){for(var r,i=e?A.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||A.cleanData(kt(r)),r.parentNode&&(n&&mt(r)&&jt(kt(r,"script")),r.parentNode.removeChild(r));return t}A.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var r,i,o,s,a=t.cloneNode(!0),c=mt(t);if(!(m.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||A.isXMLDoc(t)))for(s=kt(a),r=0,i=(o=kt(t)).length;r<i;r++)Vt(o[r],s[r]);if(e)if(n)for(o=o||kt(t),s=s||kt(a),r=0,i=o.length;r<i;r++)zt(o[r],s[r]);else zt(t,a);return(s=kt(a,"script")).length>0&&jt(s,!c&&kt(t,"script")),a},cleanData:function(t){for(var e,n,r,i=A.event.special,o=0;void 0!==(n=t[o]);o++)if(ot(n)){if(e=n[at.expando]){if(e.events)for(r in e.events)i[r]?A.event.remove(n,r):A.removeEvent(n,r,e.handle);n[at.expando]=void 0}n[ct.expando]&&(n[ct.expando]=void 0)}}}),A.fn.extend({detach:function(t){return Xt(this,t,!0)},remove:function(t){return Xt(this,t)},text:function(t){return tt(this,function(t){return void 0===t?A.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Ut(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||qt(this,t).appendChild(t)})},prepend:function(){return Ut(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=qt(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Ut(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Ut(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(A.cleanData(kt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return A.clone(this,t,e)})},html:function(t){return tt(this,function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Ht.test(t)&&!Ot[(St.exec(t)||["",""])[1].toLowerCase()]){t=A.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(A.cleanData(kt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Ut(this,arguments,function(e){var n=this.parentNode;A.inArray(this,t)<0&&(A.cleanData(kt(this)),n&&n.replaceChild(e,this))},t)}}),A.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){A.fn[t]=function(t){for(var n,r=[],i=A(t),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),A(i[s])[e](n),l.apply(r,n.get());return this.pushStack(r)}});var Yt=new RegExp("^("+dt+")(?!px)[a-z%]+$","i"),Gt=/^--/,Kt=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=r),e.getComputedStyle(t)},Qt=function(t,e,n){var r,i,o={};for(i in e)o[i]=t.style[i],t.style[i]=e[i];for(i in r=n.call(t),e)t.style[i]=o[i];return r},Jt=new RegExp(ht.join("|"),"i");function Zt(t,e,n){var r,i,o,s,a=Gt.test(e),c=t.style;return(n=n||Kt(t))&&(s=n.getPropertyValue(e)||n[e],a&&s&&(s=s.replace(L,"$1")||void 0),""!==s||mt(t)||(s=A.style(t,e)),!m.pixelBoxStyles()&&Yt.test(s)&&Jt.test(e)&&(r=c.width,i=c.minWidth,o=c.maxWidth,c.minWidth=c.maxWidth=c.width=s,s=n.width,c.width=r,c.minWidth=i,c.maxWidth=o)),void 0!==s?s+"":s}function te(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function t(){if(u){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",gt.appendChild(l).appendChild(u);var t=r.getComputedStyle(u);n="1%"!==t.top,c=12===e(t.marginLeft),u.style.right="60%",s=36===e(t.right),i=36===e(t.width),u.style.position="absolute",o=12===e(u.offsetWidth/3),gt.removeChild(l),u=null}}function e(t){return Math.round(parseFloat(t))}var n,i,o,s,a,c,l=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,A.extend(m,{boxSizingReliable:function(){return t(),i},pixelBoxStyles:function(){return t(),s},pixelPosition:function(){return t(),n},reliableMarginLeft:function(){return t(),c},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,e,n,i;return null==a&&(t=b.createElement("table"),e=b.createElement("tr"),n=b.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="box-sizing:content-box;border:1px solid",e.style.height="1px",n.style.height="9px",n.style.display="block",gt.appendChild(t).appendChild(e).appendChild(n),i=r.getComputedStyle(e),a=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===e.offsetHeight,gt.removeChild(t)),a}}))}();var ee=["Webkit","Moz","ms"],ne=b.createElement("div").style,re={};function ie(t){var e=A.cssProps[t]||re[t];return e||(t in ne?t:re[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=ee.length;n--;)if((t=ee[n]+e)in ne)return t}(t)||t)}var oe=/^(none|table(?!-c[ea]).+)/,se={position:"absolute",visibility:"hidden",display:"block"},ae={letterSpacing:"0",fontWeight:"400"};function ce(t,e,n){var r=pt.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function le(t,e,n,r,i,o){var s="width"===e?1:0,a=0,c=0,l=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=A.css(t,n+ht[s],!0,i)),r?("content"===n&&(c-=A.css(t,"padding"+ht[s],!0,i)),"margin"!==n&&(c-=A.css(t,"border"+ht[s]+"Width",!0,i))):(c+=A.css(t,"padding"+ht[s],!0,i),"padding"!==n?c+=A.css(t,"border"+ht[s]+"Width",!0,i):a+=A.css(t,"border"+ht[s]+"Width",!0,i));return!r&&o>=0&&(c+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-c-a-.5))||0),c+l}function ue(t,e,n){var r=Kt(t),i=(!m.boxSizingReliable()||n)&&"border-box"===A.css(t,"boxSizing",!1,r),o=i,s=Zt(t,e,r),a="offset"+e[0].toUpperCase()+e.slice(1);if(Yt.test(s)){if(!n)return s;s="auto"}return(!m.boxSizingReliable()&&i||!m.reliableTrDimensions()&&C(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===A.css(t,"display",!1,r))&&t.getClientRects().length&&(i="border-box"===A.css(t,"boxSizing",!1,r),(o=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+le(t,e,n||(i?"border":"content"),o,r,s)+"px"}function fe(t,e,n,r,i){return new fe.prototype.init(t,e,n,r,i)}A.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Zt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,o,s,a=it(e),c=Gt.test(e),l=t.style;if(c||(e=ie(a)),s=A.cssHooks[e]||A.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:l[e];"string"===(o=typeof n)&&(i=pt.exec(n))&&i[1]&&(n=bt(t,e,i),o="number"),null!=n&&n==n&&("number"!==o||c||(n+=i&&i[3]||(A.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==n||0!==e.indexOf("background")||(l[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,r))||(c?l.setProperty(e,n):l[e]=n))}},css:function(t,e,n,r){var i,o,s,a=it(e);return Gt.test(e)||(e=ie(a)),(s=A.cssHooks[e]||A.cssHooks[a])&&"get"in s&&(i=s.get(t,!0,n)),void 0===i&&(i=Zt(t,e,r)),"normal"===i&&e in ae&&(i=ae[e]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),A.each(["height","width"],function(t,e){A.cssHooks[e]={get:function(t,n,r){if(n)return!oe.test(A.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ue(t,e,r):Qt(t,se,function(){return ue(t,e,r)})},set:function(t,n,r){var i,o=Kt(t),s=!m.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===A.css(t,"boxSizing",!1,o),c=r?le(t,e,r,a,o):0;return a&&s&&(c-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-le(t,e,"border",!1,o)-.5)),c&&(i=pt.exec(n))&&"px"!==(i[3]||"px")&&(t.style[e]=n,n=A.css(t,e)),ce(0,n,c)}}}),A.cssHooks.marginLeft=te(m.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Zt(t,"marginLeft"))||t.getBoundingClientRect().left-Qt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),A.each({margin:"",padding:"",border:"Width"},function(t,e){A.cssHooks[t+e]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[t+ht[r]+e]=o[r]||o[r-2]||o[0];return i}},"margin"!==t&&(A.cssHooks[t+e].set=ce)}),A.fn.extend({css:function(t,e){return tt(this,function(t,e,n){var r,i,o={},s=0;if(Array.isArray(e)){for(r=Kt(t),i=e.length;s<i;s++)o[e[s]]=A.css(t,e[s],!1,r);return o}return void 0!==n?A.style(t,e,n):A.css(t,e)},t,e,arguments.length>1)}}),A.Tween=fe,fe.prototype={constructor:fe,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||A.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(A.cssNumber[n]?"":"px")},cur:function(){var t=fe.propHooks[this.prop];return t&&t.get?t.get(this):fe.propHooks._default.get(this)},run:function(t){var e,n=fe.propHooks[this.prop];return this.options.duration?this.pos=e=A.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):fe.propHooks._default.set(this),this}},fe.prototype.init.prototype=fe.prototype,fe.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=A.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){A.fx.step[t.prop]?A.fx.step[t.prop](t):1!==t.elem.nodeType||!A.cssHooks[t.prop]&&null==t.elem.style[ie(t.prop)]?t.elem[t.prop]=t.now:A.style(t.elem,t.prop,t.now+t.unit)}}},fe.propHooks.scrollTop=fe.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},A.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},A.fx=fe.prototype.init,A.fx.step={};var de,pe,he=/^(?:toggle|show|hide)$/,ge=/queueHooks$/;function me(){pe&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(me):r.setTimeout(me,A.fx.interval),A.fx.tick())}function ve(){return r.setTimeout(function(){de=void 0}),de=Date.now()}function ye(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=ht[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function be(t,e,n){for(var r,i=(_e.tweeners[e]||[]).concat(_e.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function _e(t,e,n){var r,i,o=0,s=_e.prefilters.length,a=A.Deferred().always(function(){delete c.elem}),c=function(){if(i)return!1;for(var e=de||ve(),n=Math.max(0,l.startTime+l.duration-e),r=1-(n/l.duration||0),o=0,s=l.tweens.length;o<s;o++)l.tweens[o].run(r);return a.notifyWith(t,[l,r,n]),r<1&&s?n:(s||a.notifyWith(t,[l,1,0]),a.resolveWith(t,[l]),!1)},l=a.promise({elem:t,props:A.extend({},e),opts:A.extend(!0,{specialEasing:{},easing:A.easing._default},n),originalProperties:e,originalOptions:n,startTime:de||ve(),duration:n.duration,tweens:[],createTween:function(e,n){var r=A.Tween(t,l.opts,e,n,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(r),r},stop:function(e){var n=0,r=e?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return e?(a.notifyWith(t,[l,1,0]),a.resolveWith(t,[l,e])):a.rejectWith(t,[l,e]),this}}),u=l.props;for(!function(t,e){var n,r,i,o,s;for(n in t)if(i=e[r=it(n)],o=t[n],Array.isArray(o)&&(i=o[1],o=t[n]=o[0]),n!==r&&(t[r]=o,delete t[n]),(s=A.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete t[r],o)n in t||(t[n]=o[n],e[n]=i);else e[r]=i}(u,l.opts.specialEasing);o<s;o++)if(r=_e.prefilters[o].call(l,t,u,l.opts))return v(r.stop)&&(A._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return A.map(u,be,l),v(l.opts.start)&&l.opts.start.call(t,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),A.fx.timer(A.extend(c,{elem:t,anim:l,queue:l.opts.queue})),l}A.Animation=A.extend(_e,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return bt(n.elem,t,pt.exec(e),n),n}]},tweener:function(t,e){v(t)?(e=t,t=["*"]):t=t.match(X);for(var n,r=0,i=t.length;r<i;r++)n=t[r],_e.tweeners[n]=_e.tweeners[n]||[],_e.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var r,i,o,s,a,c,l,u,f="width"in e||"height"in e,d=this,p={},h=t.style,g=t.nodeType&&yt(t),m=at.get(t,"fxshow");for(r in n.queue||(null==(s=A._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,A.queue(t,"fx").length||s.empty.fire()})})),e)if(i=e[r],he.test(i)){if(delete e[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}p[r]=m&&m[r]||A.style(t,r)}if((c=!A.isEmptyObject(e))||!A.isEmptyObject(p))for(r in f&&1===t.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=m&&m.display)&&(l=at.get(t,"display")),"none"===(u=A.css(t,"display"))&&(l?u=l:(wt([t],!0),l=t.style.display||l,u=A.css(t,"display"),wt([t]))),("inline"===u||"inline-block"===u&&null!=l)&&"none"===A.css(t,"float")&&(c||(d.done(function(){h.display=l}),null==l&&(u=h.display,l="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",d.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),c=!1,p)c||(m?"hidden"in m&&(g=m.hidden):m=at.access(t,"fxshow",{display:l}),o&&(m.hidden=!g),g&&wt([t],!0),d.done(function(){for(r in g||wt([t]),at.remove(t,"fxshow"),p)A.style(t,r,p[r])})),c=be(g?m[r]:0,r,d),r in m||(m[r]=c.start,g&&(c.end=c.start,c.start=0))}],prefilter:function(t,e){e?_e.prefilters.unshift(t):_e.prefilters.push(t)}}),A.speed=function(t,e,n){var r=t&&"object"==typeof t?A.extend({},t):{complete:n||!n&&e||v(t)&&t,duration:t,easing:n&&e||e&&!v(e)&&e};return A.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in A.fx.speeds?r.duration=A.fx.speeds[r.duration]:r.duration=A.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&A.dequeue(this,r.queue)},r},A.fn.extend({fadeTo:function(t,e,n,r){return this.filter(yt).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(t,e,n,r){var i=A.isEmptyObject(t),o=A.speed(e,n,r),s=function(){var e=_e(this,A.extend({},t),o);(i||at.get(this,"finish"))&&e.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(t,e,n){var r=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,i=null!=t&&t+"queueHooks",o=A.timers,s=at.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&ge.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=t&&o[i].queue!==t||(o[i].anim.stop(n),e=!1,o.splice(i,1));!e&&n||A.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=at.get(this),r=n[t+"queue"],i=n[t+"queueHooks"],o=A.timers,s=r?r.length:0;for(n.finish=!0,A.queue(this,t,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<s;e++)r[e]&&r[e].finish&&r[e].finish.call(this);delete n.finish})}}),A.each(["toggle","show","hide"],function(t,e){var n=A.fn[e];A.fn[e]=function(t,r,i){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ye(e,!0),t,r,i)}}),A.each({slideDown:ye("show"),slideUp:ye("hide"),slideToggle:ye("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){A.fn[t]=function(t,n,r){return this.animate(e,t,n,r)}}),A.timers=[],A.fx.tick=function(){var t,e=0,n=A.timers;for(de=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||A.fx.stop(),de=void 0},A.fx.timer=function(t){A.timers.push(t),A.fx.start()},A.fx.interval=13,A.fx.start=function(){pe||(pe=!0,me())},A.fx.stop=function(){pe=null},A.fx.speeds={slow:600,fast:200,_default:400},A.fn.delay=function(t,e){return t=A.fx&&A.fx.speeds[t]||t,e=e||"fx",this.queue(e,function(e,n){var i=r.setTimeout(e,t);n.stop=function(){r.clearTimeout(i)}})},function(){var t=b.createElement("input"),e=b.createElement("select").appendChild(b.createElement("option"));t.type="checkbox",m.checkOn=""!==t.value,m.optSelected=e.selected,(t=b.createElement("input")).value="t",t.type="radio",m.radioValue="t"===t.value}();var xe,we=A.expr.attrHandle;A.fn.extend({attr:function(t,e){return tt(this,A.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){A.removeAttr(this,t)})}}),A.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?A.prop(t,e,n):(1===o&&A.isXMLDoc(t)||(i=A.attrHooks[e.toLowerCase()]||(A.expr.match.bool.test(e)?xe:void 0)),void 0!==n?null===n?void A.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))?r:null==(r=A.find.attr(t,e))?void 0:r)},attrHooks:{type:{set:function(t,e){if(!m.radioValue&&"radio"===e&&C(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,r=0,i=e&&e.match(X);if(i&&1===t.nodeType)for(;n=i[r++];)t.removeAttribute(n)}}),xe={set:function(t,e,n){return!1===e?A.removeAttr(t,n):t.setAttribute(n,n),n}},A.each(A.expr.match.bool.source.match(/\w+/g),function(t,e){var n=we[e]||A.find.attr;we[e]=function(t,e,r){var i,o,s=e.toLowerCase();return r||(o=we[s],we[s]=i,i=null!=n(t,e,r)?s:null,we[s]=o),i}});var Te=/^(?:input|select|textarea|button)$/i,Ee=/^(?:a|area)$/i;function Ae(t){return(t.match(X)||[]).join(" ")}function Se(t){return t.getAttribute&&t.getAttribute("class")||""}function Ce(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(X)||[]}A.fn.extend({prop:function(t,e){return tt(this,A.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each(function(){delete this[A.propFix[t]||t]})}}),A.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&A.isXMLDoc(t)||(e=A.propFix[e]||e,i=A.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=A.find.attr(t,"tabindex");return e?parseInt(e,10):Te.test(t.nodeName)||Ee.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(A.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),A.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){A.propFix[this.toLowerCase()]=this}),A.fn.extend({addClass:function(t){var e,n,r,i,o,s;return v(t)?this.each(function(e){A(this).addClass(t.call(this,e,Se(this)))}):(e=Ce(t)).length?this.each(function(){if(r=Se(this),n=1===this.nodeType&&" "+Ae(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");s=Ae(n),r!==s&&this.setAttribute("class",s)}}):this},removeClass:function(t){var e,n,r,i,o,s;return v(t)?this.each(function(e){A(this).removeClass(t.call(this,e,Se(this)))}):arguments.length?(e=Ce(t)).length?this.each(function(){if(r=Se(this),n=1===this.nodeType&&" "+Ae(r)+" "){for(o=0;o<e.length;o++)for(i=e[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");s=Ae(n),r!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(t,e){var n,r,i,o,s=typeof t,a="string"===s||Array.isArray(t);return v(t)?this.each(function(n){A(this).toggleClass(t.call(this,n,Se(this),e),e)}):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(n=Ce(t),this.each(function(){if(a)for(o=A(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!==s||((r=Se(this))&&at.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":at.get(this,"__className__")||""))}))},hasClass:function(t){var e,n,r=0;for(e=" "+t+" ";n=this[r++];)if(1===n.nodeType&&(" "+Ae(Se(n))+" ").indexOf(e)>-1)return!0;return!1}});var Oe=/\r/g;A.fn.extend({val:function(t){var e,n,r,i=this[0];return arguments.length?(r=v(t),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?t.call(this,n,A(this).val()):t)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=A.map(i,function(t){return null==t?"":t+""})),(e=A.valHooks[this.type]||A.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,i,"value")||(this.value=i))})):i?(e=A.valHooks[i.type]||A.valHooks[i.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(Oe,""):null==n?"":n:void 0}}),A.extend({valHooks:{option:{get:function(t){var e=A.find.attr(t,"value");return null!=e?e:Ae(A.text(t))}},select:{get:function(t){var e,n,r,i=t.options,o=t.selectedIndex,s="select-one"===t.type,a=s?null:[],c=s?o+1:i.length;for(r=o<0?c:s?o:0;r<c;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!C(n.parentNode,"optgroup"))){if(e=A(n).val(),s)return e;a.push(e)}return a},set:function(t,e){for(var n,r,i=t.options,o=A.makeArray(e),s=i.length;s--;)((r=i[s]).selected=A.inArray(A.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),A.each(["radio","checkbox"],function(){A.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=A.inArray(A(t).val(),e)>-1}},m.checkOn||(A.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var ke=r.location,je={guid:Date.now()},De=/\?/;A.parseXML=function(t){var e,n;if(!t||"string"!=typeof t)return null;try{e=(new r.DOMParser).parseFromString(t,"text/xml")}catch(t){}return n=e&&e.getElementsByTagName("parsererror")[0],e&&!n||A.error("Invalid XML: "+(n?A.map(n.childNodes,function(t){return t.textContent}).join("\n"):t)),e};var Le=/^(?:focusinfocus|focusoutblur)$/,Ne=function(t){t.stopPropagation()};A.extend(A.event,{trigger:function(t,e,n,i){var o,s,a,c,l,u,f,d,h=[n||b],g=p.call(t,"type")?t.type:t,m=p.call(t,"namespace")?t.namespace.split("."):[];if(s=d=a=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!Le.test(g+A.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),l=g.indexOf(":")<0&&"on"+g,(t=t[A.expando]?t:new A.Event(g,"object"==typeof t&&t)).isTrigger=i?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:A.makeArray(e,[t]),f=A.event.special[g]||{},i||!f.trigger||!1!==f.trigger.apply(n,e))){if(!i&&!f.noBubble&&!y(n)){for(c=f.delegateType||g,Le.test(c+g)||(s=s.parentNode);s;s=s.parentNode)h.push(s),a=s;a===(n.ownerDocument||b)&&h.push(a.defaultView||a.parentWindow||r)}for(o=0;(s=h[o++])&&!t.isPropagationStopped();)d=s,t.type=o>1?c:f.bindType||g,(u=(at.get(s,"events")||Object.create(null))[t.type]&&at.get(s,"handle"))&&u.apply(s,e),(u=l&&s[l])&&u.apply&&ot(s)&&(t.result=u.apply(s,e),!1===t.result&&t.preventDefault());return t.type=g,i||t.isDefaultPrevented()||f._default&&!1!==f._default.apply(h.pop(),e)||!ot(n)||l&&v(n[g])&&!y(n)&&((a=n[l])&&(n[l]=null),A.event.triggered=g,t.isPropagationStopped()&&d.addEventListener(g,Ne),n[g](),t.isPropagationStopped()&&d.removeEventListener(g,Ne),A.event.triggered=void 0,a&&(n[l]=a)),t.result}},simulate:function(t,e,n){var r=A.extend(new A.Event,n,{type:t,isSimulated:!0});A.event.trigger(r,null,e)}}),A.fn.extend({trigger:function(t,e){return this.each(function(){A.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return A.event.trigger(t,e,n,!0)}});var Pe=/\[\]$/,Ie=/\r?\n/g,Me=/^(?:submit|button|image|reset|file)$/i,$e=/^(?:input|select|textarea|keygen)/i;function He(t,e,n,r){var i;if(Array.isArray(e))A.each(e,function(e,i){n||Pe.test(t)?r(t,i):He(t+"["+("object"==typeof i&&null!=i?e:"")+"]",i,n,r)});else if(n||"object"!==w(e))r(t,e);else for(i in e)He(t+"["+i+"]",e[i],n,r)}A.param=function(t,e){var n,r=[],i=function(t,e){var n=v(e)?e():e;r[r.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!A.isPlainObject(t))A.each(t,function(){i(this.name,this.value)});else for(n in t)He(n,t[n],e,i);return r.join("&")},A.fn.extend({serialize:function(){return A.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=A.prop(this,"elements");return t?A.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!A(this).is(":disabled")&&$e.test(this.nodeName)&&!Me.test(t)&&(this.checked||!At.test(t))}).map(function(t,e){var n=A(this).val();return null==n?null:Array.isArray(n)?A.map(n,function(t){return{name:e.name,value:t.replace(Ie,"\r\n")}}):{name:e.name,value:n.replace(Ie,"\r\n")}}).get()}});var Re=/%20/g,Fe=/#.*$/,qe=/([?&])_=[^&]*/,We=/^(.*?):[ \t]*([^\r\n]*)$/gm,Be=/^(?:GET|HEAD)$/,ze=/^\/\//,Ve={},Ue={},Xe="*/".concat("*"),Ye=b.createElement("a");function Ge(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var r,i=0,o=e.toLowerCase().match(X)||[];if(v(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(t[r]=t[r]||[]).unshift(n)):(t[r]=t[r]||[]).push(n)}}function Ke(t,e,n,r){var i={},o=t===Ue;function s(a){var c;return i[a]=!0,A.each(t[a]||[],function(t,a){var l=a(e,n,r);return"string"!=typeof l||o||i[l]?o?!(c=l):void 0:(e.dataTypes.unshift(l),s(l),!1)}),c}return s(e.dataTypes[0])||!i["*"]&&s("*")}function Qe(t,e){var n,r,i=A.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r||(r={}))[n]=e[n]);return r&&A.extend(!0,t,r),t}Ye.href=ke.href,A.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ke.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ke.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Xe,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":A.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Qe(Qe(t,A.ajaxSettings),e):Qe(A.ajaxSettings,t)},ajaxPrefilter:Ge(Ve),ajaxTransport:Ge(Ue),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,i,o,s,a,c,l,u,f,d,p=A.ajaxSetup({},e),h=p.context||p,g=p.context&&(h.nodeType||h.jquery)?A(h):A.event,m=A.Deferred(),v=A.Callbacks("once memory"),y=p.statusCode||{},_={},x={},w="canceled",T={readyState:0,getResponseHeader:function(t){var e;if(l){if(!s)for(s={};e=We.exec(o);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(t,e){return null==l&&(t=x[t.toLowerCase()]=x[t.toLowerCase()]||t,_[t]=e),this},overrideMimeType:function(t){return null==l&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(l)T.always(t[T.status]);else for(e in t)y[e]=[y[e],t[e]];return this},abort:function(t){var e=t||w;return n&&n.abort(e),E(0,e),this}};if(m.promise(T),p.url=((t||p.url||ke.href)+"").replace(ze,ke.protocol+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(X)||[""],null==p.crossDomain){c=b.createElement("a");try{c.href=p.url,c.href=c.href,p.crossDomain=Ye.protocol+"//"+Ye.host!=c.protocol+"//"+c.host}catch(t){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=A.param(p.data,p.traditional)),Ke(Ve,p,e,T),l)return T;for(f in(u=A.event&&p.global)&&0===A.active++&&A.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Be.test(p.type),i=p.url.replace(Fe,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Re,"+")):(d=p.url.slice(i.length),p.data&&(p.processData||"string"==typeof p.data)&&(i+=(De.test(i)?"&":"?")+p.data,delete p.data),!1===p.cache&&(i=i.replace(qe,"$1"),d=(De.test(i)?"&":"?")+"_="+je.guid+++d),p.url=i+d),p.ifModified&&(A.lastModified[i]&&T.setRequestHeader("If-Modified-Since",A.lastModified[i]),A.etag[i]&&T.setRequestHeader("If-None-Match",A.etag[i])),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&T.setRequestHeader("Content-Type",p.contentType),T.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Xe+"; q=0.01":""):p.accepts["*"]),p.headers)T.setRequestHeader(f,p.headers[f]);if(p.beforeSend&&(!1===p.beforeSend.call(h,T,p)||l))return T.abort();if(w="abort",v.add(p.complete),T.done(p.success),T.fail(p.error),n=Ke(Ue,p,e,T)){if(T.readyState=1,u&&g.trigger("ajaxSend",[T,p]),l)return T;p.async&&p.timeout>0&&(a=r.setTimeout(function(){T.abort("timeout")},p.timeout));try{l=!1,n.send(_,E)}catch(t){if(l)throw t;E(-1,t)}}else E(-1,"No Transport");function E(t,e,s,c){var f,d,b,_,x,w=e;l||(l=!0,a&&r.clearTimeout(a),n=void 0,o=c||"",T.readyState=t>0?4:0,f=t>=200&&t<300||304===t,s&&(_=function(t,e,n){for(var r,i,o,s,a=t.contents,c=t.dataTypes;"*"===c[0];)c.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){c.unshift(i);break}if(c[0]in n)o=c[0];else{for(i in n){if(!c[0]||t.converters[i+" "+c[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==c[0]&&c.unshift(o),n[o]}(p,T,s)),!f&&A.inArray("script",p.dataTypes)>-1&&A.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),_=function(t,e,n,r){var i,o,s,a,c,l={},u=t.dataTypes.slice();if(u[1])for(s in t.converters)l[s.toLowerCase()]=t.converters[s];for(o=u.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!c&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),c=o,o=u.shift())if("*"===o)o=c;else if("*"!==c&&c!==o){if(!(s=l[c+" "+o]||l["* "+o]))for(i in l)if((a=i.split(" "))[1]===o&&(s=l[c+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+c+" to "+o}}}return{state:"success",data:e}}(p,_,T,f),f?(p.ifModified&&((x=T.getResponseHeader("Last-Modified"))&&(A.lastModified[i]=x),(x=T.getResponseHeader("etag"))&&(A.etag[i]=x)),204===t||"HEAD"===p.type?w="nocontent":304===t?w="notmodified":(w=_.state,d=_.data,f=!(b=_.error))):(b=w,!t&&w||(w="error",t<0&&(t=0))),T.status=t,T.statusText=(e||w)+"",f?m.resolveWith(h,[d,w,T]):m.rejectWith(h,[T,w,b]),T.statusCode(y),y=void 0,u&&g.trigger(f?"ajaxSuccess":"ajaxError",[T,p,f?d:b]),v.fireWith(h,[T,w]),u&&(g.trigger("ajaxComplete",[T,p]),--A.active||A.event.trigger("ajaxStop")))}return T},getJSON:function(t,e,n){return A.get(t,e,n,"json")},getScript:function(t,e){return A.get(t,void 0,e,"script")}}),A.each(["get","post"],function(t,e){A[e]=function(t,n,r,i){return v(n)&&(i=i||r,r=n,n=void 0),A.ajax(A.extend({url:t,type:e,dataType:i,data:n,success:r},A.isPlainObject(t)&&t))}}),A.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),A._evalUrl=function(t,e,n){return A.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){A.globalEval(t,e,n)}})},A.fn.extend({wrapAll:function(t){var e;return this[0]&&(v(t)&&(t=t.call(this[0])),e=A(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return v(t)?this.each(function(e){A(this).wrapInner(t.call(this,e))}):this.each(function(){var e=A(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=v(t);return this.each(function(n){A(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){A(this).replaceWith(this.childNodes)}),this}}),A.expr.pseudos.hidden=function(t){return!A.expr.pseudos.visible(t)},A.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},A.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(t){}};var Je={0:200,1223:204},Ze=A.ajaxSettings.xhr();m.cors=!!Ze&&"withCredentials"in Ze,m.ajax=Ze=!!Ze,A.ajaxTransport(function(t){var e,n;if(m.cors||Ze&&!t.crossDomain)return{send:function(i,o){var s,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];for(s in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);e=function(t){return function(){e&&(e=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Je[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=e(),n=a.onerror=a.ontimeout=e("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&r.setTimeout(function(){e&&n()})},e=e("abort");try{a.send(t.hasContent&&t.data||null)}catch(t){if(e)throw t}},abort:function(){e&&e()}}}),A.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),A.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return A.globalEval(t),t}}}),A.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),A.ajaxTransport("script",function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(r,i){e=A("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&i("error"===t.type?404:200,t.type)}),b.head.appendChild(e[0])},abort:function(){n&&n()}}});var tn,en=[],nn=/(=)\?(?=&|$)|\?\?/;A.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=en.pop()||A.expando+"_"+je.guid++;return this[t]=!0,t}}),A.ajaxPrefilter("json jsonp",function(t,e,n){var i,o,s,a=!1!==t.jsonp&&(nn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(nn,"$1"+i):!1!==t.jsonp&&(t.url+=(De.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return s||A.error(i+" was not called"),s[0]},t.dataTypes[0]="json",o=r[i],r[i]=function(){s=arguments},n.always(function(){void 0===o?A(r).removeProp(i):r[i]=o,t[i]&&(t.jsonpCallback=e.jsonpCallback,en.push(i)),s&&v(o)&&o(s[0]),s=o=void 0}),"script"}),m.createHTMLDocument=((tn=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===tn.childNodes.length),A.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(m.createHTMLDocument?((r=(e=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,e.head.appendChild(r)):e=b),o=!n&&[],(i=F.exec(t))?[e.createElement(i[1])]:(i=Lt([t],e,o),o&&o.length&&A(o).remove(),A.merge([],i.childNodes)));var r,i,o},A.fn.load=function(t,e,n){var r,i,o,s=this,a=t.indexOf(" ");return a>-1&&(r=Ae(t.slice(a)),t=t.slice(0,a)),v(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),s.length>0&&A.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done(function(t){o=arguments,s.html(r?A("<div>").append(A.parseHTML(t)).find(r):t)}).always(n&&function(t,e){s.each(function(){n.apply(this,o||[t.responseText,e,t])})}),this},A.expr.pseudos.animated=function(t){return A.grep(A.timers,function(e){return t===e.elem}).length},A.offset={setOffset:function(t,e,n){var r,i,o,s,a,c,l=A.css(t,"position"),u=A(t),f={};"static"===l&&(t.style.position="relative"),a=u.offset(),o=A.css(t,"top"),c=A.css(t,"left"),("absolute"===l||"fixed"===l)&&(o+c).indexOf("auto")>-1?(s=(r=u.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(c)||0),v(e)&&(e=e.call(t,n,A.extend({},a))),null!=e.top&&(f.top=e.top-a.top+s),null!=e.left&&(f.left=e.left-a.left+i),"using"in e?e.using.call(t,f):u.css(f)}},A.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){A.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,r=this[0],i={top:0,left:0};if("fixed"===A.css(r,"position"))e=r.getBoundingClientRect();else{for(e=this.offset(),n=r.ownerDocument,t=r.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===A.css(t,"position");)t=t.parentNode;t&&t!==r&&1===t.nodeType&&((i=A(t).offset()).top+=A.css(t,"borderTopWidth",!0),i.left+=A.css(t,"borderLeftWidth",!0))}return{top:e.top-i.top-A.css(r,"marginTop",!0),left:e.left-i.left-A.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===A.css(t,"position");)t=t.offsetParent;return t||gt})}}),A.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n="pageYOffset"===e;A.fn[t]=function(r){return tt(this,function(t,r,i){var o;if(y(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===i)return o?o[e]:t[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):t[r]=i},t,r,arguments.length)}}),A.each(["top","left"],function(t,e){A.cssHooks[e]=te(m.pixelPosition,function(t,n){if(n)return n=Zt(t,e),Yt.test(n)?A(t).position()[e]+"px":n})}),A.each({Height:"height",Width:"width"},function(t,e){A.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,r){A.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return tt(this,function(e,n,i){var o;return y(e)?0===r.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===i?A.css(e,n,a):A.style(e,n,i,a)},e,s?i:void 0,s)}})}),A.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){A.fn[e]=function(t){return this.on(e,t)}}),A.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),A.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){A.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}});var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;A.proxy=function(t,e){var n,r,i;if("string"==typeof e&&(n=t[e],e=t,t=n),v(t))return r=a.call(arguments,2),i=function(){return t.apply(e||this,r.concat(a.call(arguments)))},i.guid=t.guid=t.guid||A.guid++,i},A.holdReady=function(t){t?A.readyWait++:A.ready(!0)},A.isArray=Array.isArray,A.parseJSON=JSON.parse,A.nodeName=C,A.isFunction=v,A.isWindow=y,A.camelCase=it,A.type=w,A.now=Date.now,A.isNumeric=function(t){var e=A.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},A.trim=function(t){return null==t?"":(t+"").replace(rn,"$1")},void 0===(n=function(){return A}.apply(e,[]))||(t.exports=n);var on=r.jQuery,sn=r.$;return A.noConflict=function(t){return r.$===A&&(r.$=sn),t&&r.jQuery===A&&(r.jQuery=on),A},void 0===i&&(r.jQuery=r.$=A),A})},4782:(t,e,n)=>{"use strict";var r=n(6518),i=n(4376),o=n(3517),s=n(34),a=n(5610),c=n(6198),l=n(5397),u=n(4659),f=n(8227),d=n(597),p=n(7680),h=d("slice"),g=f("species"),m=Array,v=Math.max;r({target:"Array",proto:!0,forced:!h},{slice:function(t,e){var n,r,f,d=l(this),h=c(d),y=a(t,h),b=a(void 0===e?h:e,h);if(i(d)&&(n=d.constructor,(o(n)&&(n===m||i(n.prototype))||s(n)&&null===(n=n[g]))&&(n=void 0),n===m||void 0===n))return p(d,y,b);for(r=new(void 0===n?m:n)(v(b-y,0)),f=0;y<b;y++,f++)y in d&&u(r,f,d[y]);return r.length=f,r}})},4901:t=>{"use strict";var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},4913:(t,e,n)=>{"use strict";var r=n(3724),i=n(5917),o=n(8686),s=n(8551),a=n(6969),c=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,f="enumerable",d="configurable",p="writable";e.f=r?o?function(t,e,n){if(s(t),e=a(e),s(n),"function"==typeof t&&"prototype"===e&&"value"in n&&p in n&&!n[p]){var r=u(t,e);r&&r[p]&&(t[e]=n.value,n={configurable:d in n?n[d]:r[d],enumerable:f in n?n[f]:r[f],writable:!1})}return l(t,e,n)}:l:function(t,e,n){if(s(t),e=a(e),s(n),i)try{return l(t,e,n)}catch(t){}if("get"in n||"set"in n)throw new c("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},5031:(t,e,n)=>{"use strict";var r=n(7751),i=n(9504),o=n(8480),s=n(3717),a=n(8551),c=i([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=o.f(a(t)),n=s.f;return n?c(e,n(t)):e}},5081:(t,e,n)=>{"use strict";var r=n(6518),i=n(4576);r({global:!0,forced:i.globalThis!==i},{globalThis:i})},5213:(t,e,n)=>{"use strict";var r=n(4576),i=n(9039),o=r.RegExp,s=!i(function(){var t=!0;try{o(".","d")}catch(e){t=!1}var e={},n="",r=t?"dgimsy":"gimsy",i=function(t,r){Object.defineProperty(e,t,{get:function(){return n+=r,!0}})},s={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var a in t&&(s.hasIndices="d"),s)i(a,s[a]);return Object.getOwnPropertyDescriptor(o.prototype,"flags").get.call(e)!==r||n!==r});t.exports={correct:s}},5397:(t,e,n)=>{"use strict";var r=n(7055),i=n(7750);t.exports=function(t){return r(i(t))}},5610:(t,e,n)=>{"use strict";var r=n(1291),i=Math.max,o=Math.min;t.exports=function(t,e){var n=r(t);return n<0?i(n+e,0):o(n,e)}},5745:(t,e,n)=>{"use strict";var r=n(7629);t.exports=function(t,e){return r[t]||(r[t]=e||{})}},5917:(t,e,n)=>{"use strict";var r=n(3724),i=n(9039),o=n(4055);t.exports=!r&&!i(function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a})},5966:(t,e,n)=>{"use strict";var r=n(9306),i=n(4117);t.exports=function(t,e){var n=t[e];return i(n)?void 0:r(n)}},6080:(t,e,n)=>{"use strict";var r=n(7476),i=n(9306),o=n(616),s=r(r.bind);t.exports=function(t,e){return i(t),void 0===e?t:o?s(t,e):function(){return t.apply(e,arguments)}}},6099:(t,e,n)=>{"use strict";var r=n(2140),i=n(6840),o=n(3179);r||i(Object.prototype,"toString",o,{unsafe:!0})},6119:(t,e,n)=>{"use strict";var r=n(5745),i=n(3392),o=r("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},6198:(t,e,n)=>{"use strict";var r=n(8014);t.exports=function(t){return r(t.length)}},6269:t=>{"use strict";t.exports={}},6319:(t,e,n)=>{"use strict";var r=n(8551),i=n(9539);t.exports=function(t,e,n,o){try{return o?e(r(n)[0],n[1]):e(n)}catch(e){i(t,"throw",e)}}},6395:t=>{"use strict";t.exports=!1},6469:(t,e,n)=>{"use strict";var r=n(8227),i=n(2360),o=n(4913).f,s=r("unscopables"),a=Array.prototype;void 0===a[s]&&o(a,s,{configurable:!0,value:i(null)}),t.exports=function(t){a[s][t]=!0}},6518:(t,e,n)=>{"use strict";var r=n(4576),i=n(7347).f,o=n(6699),s=n(6840),a=n(9433),c=n(7740),l=n(2796);t.exports=function(t,e){var n,u,f,d,p,h=t.target,g=t.global,m=t.stat;if(n=g?r:m?r[h]||a(h,{}):r[h]&&r[h].prototype)for(u in e){if(d=e[u],f=t.dontCallGetSet?(p=i(n,u))&&p.value:n[u],!l(g?u:h+(m?".":"#")+u,t.forced)&&void 0!==f){if(typeof d==typeof f)continue;c(d,f)}(t.sham||f&&f.sham)&&o(d,"sham",!0),s(n,u,d,t)}}},6699:(t,e,n)=>{"use strict";var r=n(3724),i=n(4913),o=n(6980);t.exports=r?function(t,e,n){return i.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},6706:(t,e,n)=>{"use strict";var r=n(9504),i=n(9306);t.exports=function(t,e,n){try{return r(i(Object.getOwnPropertyDescriptor(t,e)[n]))}catch(t){}}},6761:(t,e,n)=>{"use strict";var r=n(6518),i=n(4576),o=n(9565),s=n(9504),a=n(6395),c=n(3724),l=n(4495),u=n(9039),f=n(9297),d=n(1625),p=n(8551),h=n(5397),g=n(6969),m=n(655),v=n(6980),y=n(2360),b=n(1072),_=n(8480),x=n(298),w=n(3717),T=n(7347),E=n(4913),A=n(6801),S=n(8773),C=n(6840),O=n(2106),k=n(5745),j=n(6119),D=n(421),L=n(3392),N=n(8227),P=n(1951),I=n(511),M=n(8242),$=n(687),H=n(1181),R=n(9213).forEach,F=j("hidden"),q="Symbol",W="prototype",B=H.set,z=H.getterFor(q),V=Object[W],U=i.Symbol,X=U&&U[W],Y=i.RangeError,G=i.TypeError,K=i.QObject,Q=T.f,J=E.f,Z=x.f,tt=S.f,et=s([].push),nt=k("symbols"),rt=k("op-symbols"),it=k("wks"),ot=!K||!K[W]||!K[W].findChild,st=function(t,e,n){var r=Q(V,e);r&&delete V[e],J(t,e,n),r&&t!==V&&J(V,e,r)},at=c&&u(function(){return 7!==y(J({},"a",{get:function(){return J(this,"a",{value:7}).a}})).a})?st:J,ct=function(t,e){var n=nt[t]=y(X);return B(n,{type:q,tag:t,description:e}),c||(n.description=e),n},lt=function(t,e,n){t===V&&lt(rt,e,n),p(t);var r=g(e);return p(n),f(nt,r)?(n.enumerable?(f(t,F)&&t[F][r]&&(t[F][r]=!1),n=y(n,{enumerable:v(0,!1)})):(f(t,F)||J(t,F,v(1,y(null))),t[F][r]=!0),at(t,r,n)):J(t,r,n)},ut=function(t,e){p(t);var n=h(e),r=b(n).concat(ht(n));return R(r,function(e){c&&!o(ft,n,e)||lt(t,e,n[e])}),t},ft=function(t){var e=g(t),n=o(tt,this,e);return!(this===V&&f(nt,e)&&!f(rt,e))&&(!(n||!f(this,e)||!f(nt,e)||f(this,F)&&this[F][e])||n)},dt=function(t,e){var n=h(t),r=g(e);if(n!==V||!f(nt,r)||f(rt,r)){var i=Q(n,r);return!i||!f(nt,r)||f(n,F)&&n[F][r]||(i.enumerable=!0),i}},pt=function(t){var e=Z(h(t)),n=[];return R(e,function(t){f(nt,t)||f(D,t)||et(n,t)}),n},ht=function(t){var e=t===V,n=Z(e?rt:h(t)),r=[];return R(n,function(t){!f(nt,t)||e&&!f(V,t)||et(r,nt[t])}),r};l||(C(X=(U=function(){if(d(X,this))throw new G("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?m(arguments[0]):void 0,e=L(t),n=function(t){var r=void 0===this?i:this;r===V&&o(n,rt,t),f(r,F)&&f(r[F],e)&&(r[F][e]=!1);var s=v(1,t);try{at(r,e,s)}catch(t){if(!(t instanceof Y))throw t;st(r,e,s)}};return c&&ot&&at(V,e,{configurable:!0,set:n}),ct(e,t)})[W],"toString",function(){return z(this).tag}),C(U,"withoutSetter",function(t){return ct(L(t),t)}),S.f=ft,E.f=lt,A.f=ut,T.f=dt,_.f=x.f=pt,w.f=ht,P.f=function(t){return ct(N(t),t)},c&&(O(X,"description",{configurable:!0,get:function(){return z(this).description}}),a||C(V,"propertyIsEnumerable",ft,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!l,sham:!l},{Symbol:U}),R(b(it),function(t){I(t)}),r({target:q,stat:!0,forced:!l},{useSetter:function(){ot=!0},useSimple:function(){ot=!1}}),r({target:"Object",stat:!0,forced:!l,sham:!c},{create:function(t,e){return void 0===e?y(t):ut(y(t),e)},defineProperty:lt,defineProperties:ut,getOwnPropertyDescriptor:dt}),r({target:"Object",stat:!0,forced:!l},{getOwnPropertyNames:pt}),M(),$(U,q),D[F]=!0},6801:(t,e,n)=>{"use strict";var r=n(3724),i=n(8686),o=n(4913),s=n(8551),a=n(5397),c=n(1072);e.f=r&&!i?Object.defineProperties:function(t,e){s(t);for(var n,r=a(e),i=c(e),l=i.length,u=0;l>u;)o.f(t,n=i[u++],r[n]);return t}},6823:t=>{"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},6840:(t,e,n)=>{"use strict";var r=n(4901),i=n(4913),o=n(283),s=n(9433);t.exports=function(t,e,n,a){a||(a={});var c=a.enumerable,l=void 0!==a.name?a.name:e;if(r(n)&&o(n,l,a),a.global)c?t[e]=n:s(e,n);else{try{a.unsafe?t[e]&&(c=!0):delete t[e]}catch(t){}c?t[e]=n:i.f(t,e,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return t}},6933:(t,e,n)=>{"use strict";var r=n(9504),i=n(4376),o=n(4901),s=n(2195),a=n(655),c=r([].push);t.exports=function(t){if(o(t))return t;if(i(t)){for(var e=t.length,n=[],r=0;r<e;r++){var l=t[r];"string"==typeof l?c(n,l):"number"!=typeof l&&"Number"!==s(l)&&"String"!==s(l)||c(n,a(l))}var u=n.length,f=!0;return function(t,e){if(f)return f=!1,e;if(i(this))return e;for(var r=0;r<u;r++)if(n[r]===t)return e}}}},6955:(t,e,n)=>{"use strict";var r=n(2140),i=n(4901),o=n(2195),s=n(8227)("toStringTag"),a=Object,c="Arguments"===o(function(){return arguments}());t.exports=r?o:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=a(t),s))?n:c?o(e):"Object"===(r=o(e))&&i(e.callee)?"Arguments":r}},6969:(t,e,n)=>{"use strict";var r=n(2777),i=n(757);t.exports=function(t){var e=r(t,"string");return i(e)?e:e+""}},6980:t=>{"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},7040:(t,e,n)=>{"use strict";var r=n(4495);t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:(t,e,n)=>{"use strict";var r=n(9504),i=n(9039),o=n(2195),s=Object,a=r("".split);t.exports=i(function(){return!s("z").propertyIsEnumerable(0)})?function(t){return"String"===o(t)?a(t,""):s(t)}:s},7323:(t,e,n)=>{"use strict";var r,i,o=n(9565),s=n(9504),a=n(655),c=n(7979),l=n(8429),u=n(5745),f=n(2360),d=n(1181).get,p=n(3635),h=n(8814),g=u("native-string-replace",String.prototype.replace),m=RegExp.prototype.exec,v=m,y=s("".charAt),b=s("".indexOf),_=s("".replace),x=s("".slice),w=(i=/b*/g,o(m,r=/a/,"a"),o(m,i,"a"),0!==r.lastIndex||0!==i.lastIndex),T=l.BROKEN_CARET,E=void 0!==/()??/.exec("")[1];(w||E||T||p||h)&&(v=function(t){var e,n,r,i,s,l,u,p=this,h=d(p),A=a(t),S=h.raw;if(S)return S.lastIndex=p.lastIndex,e=o(v,S,A),p.lastIndex=S.lastIndex,e;var C=h.groups,O=T&&p.sticky,k=o(c,p),j=p.source,D=0,L=A;if(O&&(k=_(k,"y",""),-1===b(k,"g")&&(k+="g"),L=x(A,p.lastIndex),p.lastIndex>0&&(!p.multiline||p.multiline&&"\n"!==y(A,p.lastIndex-1))&&(j="(?: "+j+")",L=" "+L,D++),n=new RegExp("^(?:"+j+")",k)),E&&(n=new RegExp("^"+j+"$(?!\\s)",k)),w&&(r=p.lastIndex),i=o(m,O?n:p,L),O?i?(i.input=x(i.input,D),i[0]=x(i[0],D),i.index=p.lastIndex,p.lastIndex+=i[0].length):p.lastIndex=0:w&&i&&(p.lastIndex=p.global?i.index+i[0].length:r),E&&i&&i.length>1&&o(g,i[0],n,function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)}),i&&C)for(i.groups=l=f(null),s=0;s<C.length;s++)l[(u=C[s])[0]]=i[u[1]];return i}),t.exports=v},7347:(t,e,n)=>{"use strict";var r=n(3724),i=n(9565),o=n(8773),s=n(6980),a=n(5397),c=n(6969),l=n(9297),u=n(5917),f=Object.getOwnPropertyDescriptor;e.f=r?f:function(t,e){if(t=a(t),e=c(e),u)try{return f(t,e)}catch(t){}if(l(t,e))return s(!i(o.f,t,e),t[e])}},7400:t=>{"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},7433:(t,e,n)=>{"use strict";var r=n(4376),i=n(3517),o=n(34),s=n(8227)("species"),a=Array;t.exports=function(t){var e;return r(t)&&(e=t.constructor,(i(e)&&(e===a||r(e.prototype))||o(e)&&null===(e=e[s]))&&(e=void 0)),void 0===e?a:e}},7476:(t,e,n)=>{"use strict";var r=n(2195),i=n(9504);t.exports=function(t){if("Function"===r(t))return i(t)}},7495:(t,e,n)=>{"use strict";var r=n(6518),i=n(7323);r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},7629:(t,e,n)=>{"use strict";var r=n(6395),i=n(4576),o=n(9433),s="__core-js_shared__",a=t.exports=i[s]||o(s,{});(a.versions||(a.versions=[])).push({version:"3.44.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7657:(t,e,n)=>{"use strict";var r,i,o,s=n(9039),a=n(4901),c=n(34),l=n(2360),u=n(2787),f=n(6840),d=n(8227),p=n(6395),h=d("iterator"),g=!1;[].keys&&("next"in(o=[].keys())?(i=u(u(o)))!==Object.prototype&&(r=i):g=!0),!c(r)||s(function(){var t={};return r[h].call(t)!==t})?r={}:p&&(r=l(r)),a(r[h])||f(r,h,function(){return this}),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:g}},7680:(t,e,n)=>{"use strict";var r=n(9504);t.exports=r([].slice)},7740:(t,e,n)=>{"use strict";var r=n(9297),i=n(5031),o=n(7347),s=n(4913);t.exports=function(t,e,n){for(var a=i(e),c=s.f,l=o.f,u=0;u<a.length;u++){var f=a[u];r(t,f)||n&&r(n,f)||c(t,f,l(e,f))}}},7750:(t,e,n)=>{"use strict";var r=n(4117),i=TypeError;t.exports=function(t){if(r(t))throw new i("Can't call method on "+t);return t}},7751:(t,e,n)=>{"use strict";var r=n(4576),i=n(4901);t.exports=function(t,e){return arguments.length<2?(n=r[t],i(n)?n:void 0):r[t]&&r[t][e];var n}},7764:(t,e,n)=>{"use strict";var r=n(8183).charAt,i=n(655),o=n(1181),s=n(1088),a=n(2529),c="String Iterator",l=o.set,u=o.getterFor(c);s(String,"String",function(t){l(this,{type:c,string:i(t),index:0})},function(){var t,e=u(this),n=e.string,i=e.index;return i>=n.length?a(void 0,!0):(t=r(n,i),e.index+=t.length,a(t,!1))})},7812:(t,e,n)=>{"use strict";var r=n(6518),i=n(9297),o=n(757),s=n(6823),a=n(5745),c=n(1296),l=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!c},{keyFor:function(t){if(!o(t))throw new TypeError(s(t)+" is not a symbol");if(i(l,t))return l[t]}})},7916:(t,e,n)=>{"use strict";var r=n(6080),i=n(9565),o=n(8981),s=n(6319),a=n(4209),c=n(3517),l=n(6198),u=n(4659),f=n(81),d=n(851),p=Array;t.exports=function(t){var e=o(t),n=c(this),h=arguments.length,g=h>1?arguments[1]:void 0,m=void 0!==g;m&&(g=r(g,h>2?arguments[2]:void 0));var v,y,b,_,x,w,T=d(e),E=0;if(!T||this===p&&a(T))for(v=l(e),y=n?new this(v):p(v);v>E;E++)w=m?g(e[E],E):e[E],u(y,E,w);else for(y=n?new this:[],x=(_=f(e,T)).next;!(b=i(x,_)).done;E++)w=m?s(_,g,[b.value,E],!0):b.value,u(y,E,w);return y.length=E,y}},7979:(t,e,n)=>{"use strict";var r=n(8551);t.exports=function(){var t=r(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},8014:(t,e,n)=>{"use strict";var r=n(1291),i=Math.min;t.exports=function(t){var e=r(t);return e>0?i(e,9007199254740991):0}},8183:(t,e,n)=>{"use strict";var r=n(9504),i=n(1291),o=n(655),s=n(7750),a=r("".charAt),c=r("".charCodeAt),l=r("".slice),u=function(t){return function(e,n){var r,u,f=o(s(e)),d=i(n),p=f.length;return d<0||d>=p?t?"":void 0:(r=c(f,d))<55296||r>56319||d+1===p||(u=c(f,d+1))<56320||u>57343?t?a(f,d):r:t?l(f,d,d+2):u-56320+(r-55296<<10)+65536}};t.exports={codeAt:u(!1),charAt:u(!0)}},8227:(t,e,n)=>{"use strict";var r=n(4576),i=n(5745),o=n(9297),s=n(3392),a=n(4495),c=n(7040),l=r.Symbol,u=i("wks"),f=c?l.for||l:l&&l.withoutSetter||s;t.exports=function(t){return o(u,t)||(u[t]=a&&o(l,t)?l[t]:f("Symbol."+t)),u[t]}},8242:(t,e,n)=>{"use strict";var r=n(9565),i=n(7751),o=n(8227),s=n(6840);t.exports=function(){var t=i("Symbol"),e=t&&t.prototype,n=e&&e.valueOf,a=o("toPrimitive");e&&!e[a]&&s(e,a,function(t){return r(n,this)},{arity:1})}},8429:(t,e,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp,o=r(function(){var t=i("a","y");return t.lastIndex=2,null!==t.exec("abcd")}),s=o||r(function(){return!i("a","y").sticky}),a=o||r(function(){var t=i("^r","gy");return t.lastIndex=2,null!==t.exec("str")});t.exports={BROKEN_CARET:a,MISSED_STICKY:s,UNSUPPORTED_Y:o}},8480:(t,e,n)=>{"use strict";var r=n(1828),i=n(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,i)}},8551:(t,e,n)=>{"use strict";var r=n(34),i=String,o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not an object")}},8622:(t,e,n)=>{"use strict";var r=n(4576),i=n(4901),o=r.WeakMap;t.exports=i(o)&&/native code/.test(String(o))},8686:(t,e,n)=>{"use strict";var r=n(3724),i=n(9039);t.exports=r&&i(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8727:t=>{"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8745:(t,e,n)=>{"use strict";var r=n(616),i=Function.prototype,o=i.apply,s=i.call;t.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(o):function(){return s.apply(o,arguments)})},8773:(t,e)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);e.f=i?function(t){var e=r(this,t);return!!e&&e.enumerable}:n},8781:(t,e,n)=>{"use strict";var r=n(350).PROPER,i=n(6840),o=n(8551),s=n(655),a=n(9039),c=n(1034),l="toString",u=RegExp.prototype,f=u[l],d=a(function(){return"/a/b"!==f.call({source:"a",flags:"b"})}),p=r&&f.name!==l;(d||p)&&i(u,l,function(){var t=o(this);return"/"+s(t.source)+"/"+s(c(t))},{unsafe:!0})},8814:(t,e,n)=>{"use strict";var r=n(9039),i=n(4576).RegExp;t.exports=r(function(){var t=i("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})},8981:(t,e,n)=>{"use strict";var r=n(7750),i=Object;t.exports=function(t){return i(r(t))}},9039:t=>{"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},9167:(t,e,n)=>{"use strict";var r=n(4576);t.exports=r},9213:(t,e,n)=>{"use strict";var r=n(6080),i=n(9504),o=n(7055),s=n(8981),a=n(6198),c=n(1469),l=i([].push),u=function(t){var e=1===t,n=2===t,i=3===t,u=4===t,f=6===t,d=7===t,p=5===t||f;return function(h,g,m,v){for(var y,b,_=s(h),x=o(_),w=a(x),T=r(g,m),E=0,A=v||c,S=e?A(h,w):n||d?A(h,0):void 0;w>E;E++)if((p||E in x)&&(b=T(y=x[E],E,_),t))if(e)S[E]=b;else if(b)switch(t){case 3:return!0;case 5:return y;case 6:return E;case 2:l(S,y)}else switch(t){case 4:return!1;case 7:l(S,y)}return f?-1:i||u?u:S}};t.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},9296:(t,e,n)=>{"use strict";var r=n(4055)("span").classList,i=r&&r.constructor&&r.constructor.prototype;t.exports=i===Object.prototype?void 0:i},9297:(t,e,n)=>{"use strict";var r=n(9504),i=n(8981),o=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return o(i(t),e)}},9306:(t,e,n)=>{"use strict";var r=n(4901),i=n(6823),o=TypeError;t.exports=function(t){if(r(t))return t;throw new o(i(t)+" is not a function")}},9433:(t,e,n)=>{"use strict";var r=n(4576),i=Object.defineProperty;t.exports=function(t,e){try{i(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},9463:(t,e,n)=>{"use strict";var r=n(6518),i=n(3724),o=n(4576),s=n(9504),a=n(9297),c=n(4901),l=n(1625),u=n(655),f=n(2106),d=n(7740),p=o.Symbol,h=p&&p.prototype;if(i&&c(p)&&(!("description"in h)||void 0!==p().description)){var g={},m=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:u(arguments[0]),e=l(h,this)?new p(t):void 0===t?p():p(t);return""===t&&(g[e]=!0),e};d(m,p),m.prototype=h,h.constructor=m;var v="Symbol(description detection)"===String(p("description detection")),y=s(h.valueOf),b=s(h.toString),_=/^Symbol\((.*)\)[^)]+$/,x=s("".replace),w=s("".slice);f(h,"description",{configurable:!0,get:function(){var t=y(this);if(a(g,t))return"";var e=b(t),n=v?w(e,7,-1):x(e,_,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:m})}},9504:(t,e,n)=>{"use strict";var r=n(616),i=Function.prototype,o=i.call,s=r&&i.bind.bind(o,o);t.exports=r?s:function(t){return function(){return o.apply(t,arguments)}}},9519:(t,e,n)=>{"use strict";var r,i,o=n(4576),s=n(2839),a=o.process,c=o.Deno,l=a&&a.versions||c&&c.version,u=l&&l.v8;u&&(i=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&s&&(!(r=s.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/))&&(i=+r[1]),t.exports=i},9539:(t,e,n)=>{"use strict";var r=n(9565),i=n(8551),o=n(5966);t.exports=function(t,e,n){var s,a;i(t);try{if(!(s=o(t,"return"))){if("throw"===e)throw n;return n}s=r(s,t)}catch(t){a=!0,s=t}if("throw"===e)throw n;if(a)throw s;return i(s),n}},9565:(t,e,n)=>{"use strict";var r=n(616),i=Function.prototype.call;t.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},9617:(t,e,n)=>{"use strict";var r=n(5397),i=n(5610),o=n(6198),s=function(t){return function(e,n,s){var a=r(e),c=o(a);if(0===c)return!t&&-1;var l,u=i(s,c);if(t&&n!=n){for(;c>u;)if((l=a[u++])!=l)return!0}else for(;c>u;u++)if((t||u in a)&&a[u]===n)return t||u||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},9773:(t,e,n)=>{"use strict";var r=n(6518),i=n(4495),o=n(9039),s=n(3717),a=n(8981);r({target:"Object",stat:!0,forced:!i||o(function(){s.f(1)})},{getOwnPropertySymbols:function(t){var e=s.f;return e?e(a(t)):[]}})}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{"use strict";var t={};n.r(t),n.d(t,{afterMain:()=>E,afterRead:()=>x,afterWrite:()=>C,applyStyles:()=>P,arrow:()=>tt,auto:()=>l,basePlacements:()=>u,beforeMain:()=>w,beforeRead:()=>b,beforeWrite:()=>A,bottom:()=>s,clippingParents:()=>p,computeStyles:()=>it,createPopper:()=>Pt,createPopperBase:()=>Nt,createPopperLite:()=>It,detectOverflow:()=>_t,end:()=>d,eventListeners:()=>st,flip:()=>xt,hide:()=>Et,left:()=>c,main:()=>T,modifierPhases:()=>O,offset:()=>At,placements:()=>y,popper:()=>g,popperGenerator:()=>Lt,popperOffsets:()=>St,preventOverflow:()=>Ct,read:()=>_,reference:()=>m,right:()=>a,start:()=>f,top:()=>o,variationPlacements:()=>v,viewport:()=>h,write:()=>S});var e={};n.r(e),n.d(e,{Alert:()=>De,Button:()=>Ne,Carousel:()=>hn,Collapse:()=>On,Dropdown:()=>Zn,Modal:()=>Pr,Offcanvas:()=>Jr,Popover:()=>Ei,ScrollSpy:()=>Ii,Tab:()=>io,Toast:()=>_o,Tooltip:()=>bi});n(2675),n(9463),n(2259),n(1629),n(3418),n(4346),n(3792),n(2062),n(4782),n(3288),n(2010),n(6099),n(7495),n(8781),n(7764),n(2480),n(3500),n(2953);var r=n(4692),i=n.n(r),o="top",s="bottom",a="right",c="left",l="auto",u=[o,s,a,c],f="start",d="end",p="clippingParents",h="viewport",g="popper",m="reference",v=u.reduce(function(t,e){return t.concat([e+"-"+f,e+"-"+d])},[]),y=[].concat(u,[l]).reduce(function(t,e){return t.concat([e,e+"-"+f,e+"-"+d])},[]),b="beforeRead",_="read",x="afterRead",w="beforeMain",T="main",E="afterMain",A="beforeWrite",S="write",C="afterWrite",O=[b,_,x,w,T,E,A,S,C];function k(t){return t?(t.nodeName||"").toLowerCase():null}function j(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function D(t){return t instanceof j(t).Element||t instanceof Element}function L(t){return t instanceof j(t).HTMLElement||t instanceof HTMLElement}function N(t){return"undefined"!=typeof ShadowRoot&&(t instanceof j(t).ShadowRoot||t instanceof ShadowRoot)}const P={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach(function(t){var n=e.styles[t]||{},r=e.attributes[t]||{},i=e.elements[t];L(i)&&k(i)&&(Object.assign(i.style,n),Object.keys(r).forEach(function(t){var e=r[t];!1===e?i.removeAttribute(t):i.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach(function(t){var r=e.elements[t],i=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce(function(t,e){return t[e]="",t},{});L(r)&&k(r)&&(Object.assign(r.style,o),Object.keys(i).forEach(function(t){r.removeAttribute(t)}))})}},requires:["computeStyles"]};function I(t){return t.split("-")[0]}var M=Math.max,$=Math.min,H=Math.round;function R(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function F(){return!/^((?!chrome|android).)*safari/i.test(R())}function q(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!1);var r=t.getBoundingClientRect(),i=1,o=1;e&&L(t)&&(i=t.offsetWidth>0&&H(r.width)/t.offsetWidth||1,o=t.offsetHeight>0&&H(r.height)/t.offsetHeight||1);var s=(D(t)?j(t):window).visualViewport,a=!F()&&n,c=(r.left+(a&&s?s.offsetLeft:0))/i,l=(r.top+(a&&s?s.offsetTop:0))/o,u=r.width/i,f=r.height/o;return{width:u,height:f,top:l,right:c+u,bottom:l+f,left:c,x:c,y:l}}function W(t){var e=q(t),n=t.offsetWidth,r=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:r}}function B(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&N(n)){var r=e;do{if(r&&t.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function z(t){return j(t).getComputedStyle(t)}function V(t){return["table","td","th"].indexOf(k(t))>=0}function U(t){return((D(t)?t.ownerDocument:t.document)||window.document).documentElement}function X(t){return"html"===k(t)?t:t.assignedSlot||t.parentNode||(N(t)?t.host:null)||U(t)}function Y(t){return L(t)&&"fixed"!==z(t).position?t.offsetParent:null}function G(t){for(var e=j(t),n=Y(t);n&&V(n)&&"static"===z(n).position;)n=Y(n);return n&&("html"===k(n)||"body"===k(n)&&"static"===z(n).position)?e:n||function(t){var e=/firefox/i.test(R());if(/Trident/i.test(R())&&L(t)&&"fixed"===z(t).position)return null;var n=X(t);for(N(n)&&(n=n.host);L(n)&&["html","body"].indexOf(k(n))<0;){var r=z(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||e&&"filter"===r.willChange||e&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(t)||e}function K(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function Q(t,e,n){return M(t,$(e,n))}function J(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Z(t,e){return e.reduce(function(e,n){return e[n]=t,e},{})}const tt={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,r=t.name,i=t.options,l=n.elements.arrow,f=n.modifiersData.popperOffsets,d=I(n.placement),p=K(d),h=[c,a].indexOf(d)>=0?"height":"width";if(l&&f){var g=function(t,e){return J("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Z(t,u))}(i.padding,n),m=W(l),v="y"===p?o:c,y="y"===p?s:a,b=n.rects.reference[h]+n.rects.reference[p]-f[p]-n.rects.popper[h],_=f[p]-n.rects.reference[p],x=G(l),w=x?"y"===p?x.clientHeight||0:x.clientWidth||0:0,T=b/2-_/2,E=g[v],A=w-m[h]-g[y],S=w/2-m[h]/2+T,C=Q(E,S,A),O=p;n.modifiersData[r]=((e={})[O]=C,e.centerOffset=C-S,e)}},effect:function(t){var e=t.state,n=t.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&B(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function et(t){return t.split("-")[1]}var nt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function rt(t){var e,n=t.popper,r=t.popperRect,i=t.placement,l=t.variation,u=t.offsets,f=t.position,p=t.gpuAcceleration,h=t.adaptive,g=t.roundOffsets,m=t.isFixed,v=u.x,y=void 0===v?0:v,b=u.y,_=void 0===b?0:b,x="function"==typeof g?g({x:y,y:_}):{x:y,y:_};y=x.x,_=x.y;var w=u.hasOwnProperty("x"),T=u.hasOwnProperty("y"),E=c,A=o,S=window;if(h){var C=G(n),O="clientHeight",k="clientWidth";if(C===j(n)&&"static"!==z(C=U(n)).position&&"absolute"===f&&(O="scrollHeight",k="scrollWidth"),i===o||(i===c||i===a)&&l===d)A=s,_-=(m&&C===S&&S.visualViewport?S.visualViewport.height:C[O])-r.height,_*=p?1:-1;if(i===c||(i===o||i===s)&&l===d)E=a,y-=(m&&C===S&&S.visualViewport?S.visualViewport.width:C[k])-r.width,y*=p?1:-1}var D,L=Object.assign({position:f},h&&nt),N=!0===g?function(t,e){var n=t.x,r=t.y,i=e.devicePixelRatio||1;return{x:H(n*i)/i||0,y:H(r*i)/i||0}}({x:y,y:_},j(n)):{x:y,y:_};return y=N.x,_=N.y,p?Object.assign({},L,((D={})[A]=T?"0":"",D[E]=w?"0":"",D.transform=(S.devicePixelRatio||1)<=1?"translate("+y+"px, "+_+"px)":"translate3d("+y+"px, "+_+"px, 0)",D)):Object.assign({},L,((e={})[A]=T?_+"px":"",e[E]=w?y+"px":"",e.transform="",e))}const it={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,c=void 0===a||a,l={placement:I(e.placement),variation:et(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,rt(Object.assign({},l,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:s,roundOffsets:c})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,rt(Object.assign({},l,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var ot={passive:!0};const st={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,r=t.options,i=r.scroll,o=void 0===i||i,s=r.resize,a=void 0===s||s,c=j(e.elements.popper),l=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&l.forEach(function(t){t.addEventListener("scroll",n.update,ot)}),a&&c.addEventListener("resize",n.update,ot),function(){o&&l.forEach(function(t){t.removeEventListener("scroll",n.update,ot)}),a&&c.removeEventListener("resize",n.update,ot)}},data:{}};var at={left:"right",right:"left",bottom:"top",top:"bottom"};function ct(t){return t.replace(/left|right|bottom|top/g,function(t){return at[t]})}var lt={start:"end",end:"start"};function ut(t){return t.replace(/start|end/g,function(t){return lt[t]})}function ft(t){var e=j(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function dt(t){return q(U(t)).left+ft(t).scrollLeft}function pt(t){var e=z(t),n=e.overflow,r=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function ht(t){return["html","body","#document"].indexOf(k(t))>=0?t.ownerDocument.body:L(t)&&pt(t)?t:ht(X(t))}function gt(t,e){var n;void 0===e&&(e=[]);var r=ht(t),i=r===(null==(n=t.ownerDocument)?void 0:n.body),o=j(r),s=i?[o].concat(o.visualViewport||[],pt(r)?r:[]):r,a=e.concat(s);return i?a:a.concat(gt(X(s)))}function mt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function vt(t,e,n){return e===h?mt(function(t,e){var n=j(t),r=U(t),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,c=0;if(i){o=i.width,s=i.height;var l=F();(l||!l&&"fixed"===e)&&(a=i.offsetLeft,c=i.offsetTop)}return{width:o,height:s,x:a+dt(t),y:c}}(t,n)):D(e)?function(t,e){var n=q(t,!1,"fixed"===e);return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}(e,n):mt(function(t){var e,n=U(t),r=ft(t),i=null==(e=t.ownerDocument)?void 0:e.body,o=M(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=M(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+dt(t),c=-r.scrollTop;return"rtl"===z(i||n).direction&&(a+=M(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:c}}(U(t)))}function yt(t,e,n,r){var i="clippingParents"===e?function(t){var e=gt(X(t)),n=["absolute","fixed"].indexOf(z(t).position)>=0&&L(t)?G(t):t;return D(n)?e.filter(function(t){return D(t)&&B(t,n)&&"body"!==k(t)}):[]}(t):[].concat(e),o=[].concat(i,[n]),s=o[0],a=o.reduce(function(e,n){var i=vt(t,n,r);return e.top=M(i.top,e.top),e.right=$(i.right,e.right),e.bottom=$(i.bottom,e.bottom),e.left=M(i.left,e.left),e},vt(t,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function bt(t){var e,n=t.reference,r=t.element,i=t.placement,l=i?I(i):null,u=i?et(i):null,p=n.x+n.width/2-r.width/2,h=n.y+n.height/2-r.height/2;switch(l){case o:e={x:p,y:n.y-r.height};break;case s:e={x:p,y:n.y+n.height};break;case a:e={x:n.x+n.width,y:h};break;case c:e={x:n.x-r.width,y:h};break;default:e={x:n.x,y:n.y}}var g=l?K(l):null;if(null!=g){var m="y"===g?"height":"width";switch(u){case f:e[g]=e[g]-(n[m]/2-r[m]/2);break;case d:e[g]=e[g]+(n[m]/2-r[m]/2)}}return e}function _t(t,e){void 0===e&&(e={});var n=e,r=n.placement,i=void 0===r?t.placement:r,c=n.strategy,l=void 0===c?t.strategy:c,f=n.boundary,d=void 0===f?p:f,v=n.rootBoundary,y=void 0===v?h:v,b=n.elementContext,_=void 0===b?g:b,x=n.altBoundary,w=void 0!==x&&x,T=n.padding,E=void 0===T?0:T,A=J("number"!=typeof E?E:Z(E,u)),S=_===g?m:g,C=t.rects.popper,O=t.elements[w?S:_],k=yt(D(O)?O:O.contextElement||U(t.elements.popper),d,y,l),j=q(t.elements.reference),L=bt({reference:j,element:C,strategy:"absolute",placement:i}),N=mt(Object.assign({},C,L)),P=_===g?N:j,I={top:k.top-P.top+A.top,bottom:P.bottom-k.bottom+A.bottom,left:k.left-P.left+A.left,right:P.right-k.right+A.right},M=t.modifiersData.offset;if(_===g&&M){var $=M[i];Object.keys(I).forEach(function(t){var e=[a,s].indexOf(t)>=0?1:-1,n=[o,s].indexOf(t)>=0?"y":"x";I[t]+=$[n]*e})}return I}const xt={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var i=n.mainAxis,d=void 0===i||i,p=n.altAxis,h=void 0===p||p,g=n.fallbackPlacements,m=n.padding,b=n.boundary,_=n.rootBoundary,x=n.altBoundary,w=n.flipVariations,T=void 0===w||w,E=n.allowedAutoPlacements,A=e.options.placement,S=I(A),C=g||(S===A||!T?[ct(A)]:function(t){if(I(t)===l)return[];var e=ct(t);return[ut(t),e,ut(e)]}(A)),O=[A].concat(C).reduce(function(t,n){return t.concat(I(n)===l?function(t,e){void 0===e&&(e={});var n=e,r=n.placement,i=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,l=void 0===c?y:c,f=et(r),d=f?a?v:v.filter(function(t){return et(t)===f}):u,p=d.filter(function(t){return l.indexOf(t)>=0});0===p.length&&(p=d);var h=p.reduce(function(e,n){return e[n]=_t(t,{placement:n,boundary:i,rootBoundary:o,padding:s})[I(n)],e},{});return Object.keys(h).sort(function(t,e){return h[t]-h[e]})}(e,{placement:n,boundary:b,rootBoundary:_,padding:m,flipVariations:T,allowedAutoPlacements:E}):n)},[]),k=e.rects.reference,j=e.rects.popper,D=new Map,L=!0,N=O[0],P=0;P<O.length;P++){var M=O[P],$=I(M),H=et(M)===f,R=[o,s].indexOf($)>=0,F=R?"width":"height",q=_t(e,{placement:M,boundary:b,rootBoundary:_,altBoundary:x,padding:m}),W=R?H?a:c:H?s:o;k[F]>j[F]&&(W=ct(W));var B=ct(W),z=[];if(d&&z.push(q[$]<=0),h&&z.push(q[W]<=0,q[B]<=0),z.every(function(t){return t})){N=M,L=!1;break}D.set(M,z)}if(L)for(var V=function(t){var e=O.find(function(e){var n=D.get(e);if(n)return n.slice(0,t).every(function(t){return t})});if(e)return N=e,"break"},U=T?3:1;U>0;U--){if("break"===V(U))break}e.placement!==N&&(e.modifiersData[r]._skip=!0,e.placement=N,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function wt(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function Tt(t){return[o,a,s,c].some(function(e){return t[e]>=0})}const Et={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,r=e.rects.reference,i=e.rects.popper,o=e.modifiersData.preventOverflow,s=_t(e,{elementContext:"reference"}),a=_t(e,{altBoundary:!0}),c=wt(s,r),l=wt(a,i,o),u=Tt(c),f=Tt(l);e.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:u,hasPopperEscaped:f},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}};const At={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,r=t.name,i=n.offset,s=void 0===i?[0,0]:i,l=y.reduce(function(t,n){return t[n]=function(t,e,n){var r=I(t),i=[c,o].indexOf(r)>=0?-1:1,s="function"==typeof n?n(Object.assign({},e,{placement:t})):n,l=s[0],u=s[1];return l=l||0,u=(u||0)*i,[c,a].indexOf(r)>=0?{x:u,y:l}:{x:l,y:u}}(n,e.rects,s),t},{}),u=l[e.placement],f=u.x,d=u.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=f,e.modifiersData.popperOffsets.y+=d),e.modifiersData[r]=l}};const St={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=bt({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}};const Ct={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name,i=n.mainAxis,l=void 0===i||i,u=n.altAxis,d=void 0!==u&&u,p=n.boundary,h=n.rootBoundary,g=n.altBoundary,m=n.padding,v=n.tether,y=void 0===v||v,b=n.tetherOffset,_=void 0===b?0:b,x=_t(e,{boundary:p,rootBoundary:h,padding:m,altBoundary:g}),w=I(e.placement),T=et(e.placement),E=!T,A=K(w),S="x"===A?"y":"x",C=e.modifiersData.popperOffsets,O=e.rects.reference,k=e.rects.popper,j="function"==typeof _?_(Object.assign({},e.rects,{placement:e.placement})):_,D="number"==typeof j?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),L=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,N={x:0,y:0};if(C){if(l){var P,H="y"===A?o:c,R="y"===A?s:a,F="y"===A?"height":"width",q=C[A],B=q+x[H],z=q-x[R],V=y?-k[F]/2:0,U=T===f?O[F]:k[F],X=T===f?-k[F]:-O[F],Y=e.elements.arrow,J=y&&Y?W(Y):{width:0,height:0},Z=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},tt=Z[H],nt=Z[R],rt=Q(0,O[F],J[F]),it=E?O[F]/2-V-rt-tt-D.mainAxis:U-rt-tt-D.mainAxis,ot=E?-O[F]/2+V+rt+nt+D.mainAxis:X+rt+nt+D.mainAxis,st=e.elements.arrow&&G(e.elements.arrow),at=st?"y"===A?st.clientTop||0:st.clientLeft||0:0,ct=null!=(P=null==L?void 0:L[A])?P:0,lt=q+ot-ct,ut=Q(y?$(B,q+it-ct-at):B,q,y?M(z,lt):z);C[A]=ut,N[A]=ut-q}if(d){var ft,dt="x"===A?o:c,pt="x"===A?s:a,ht=C[S],gt="y"===S?"height":"width",mt=ht+x[dt],vt=ht-x[pt],yt=-1!==[o,c].indexOf(w),bt=null!=(ft=null==L?void 0:L[S])?ft:0,xt=yt?mt:ht-O[gt]-k[gt]-bt+D.altAxis,wt=yt?ht+O[gt]+k[gt]-bt-D.altAxis:vt,Tt=y&&yt?function(t,e,n){var r=Q(t,e,n);return r>n?n:r}(xt,ht,wt):Q(y?xt:mt,ht,y?wt:vt);C[S]=Tt,N[S]=Tt-ht}e.modifiersData[r]=N}},requiresIfExists:["offset"]};function Ot(t,e,n){void 0===n&&(n=!1);var r,i,o=L(e),s=L(e)&&function(t){var e=t.getBoundingClientRect(),n=H(e.width)/t.offsetWidth||1,r=H(e.height)/t.offsetHeight||1;return 1!==n||1!==r}(e),a=U(e),c=q(t,s,n),l={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==k(e)||pt(a))&&(l=(r=e)!==j(r)&&L(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:ft(r)),L(e)?((u=q(e,!0)).x+=e.clientLeft,u.y+=e.clientTop):a&&(u.x=dt(a))),{x:c.left+l.scrollLeft-u.x,y:c.top+l.scrollTop-u.y,width:c.width,height:c.height}}function kt(t){var e=new Map,n=new Set,r=[];function i(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!n.has(t)){var r=e.get(t);r&&i(r)}}),r.push(t)}return t.forEach(function(t){e.set(t.name,t)}),t.forEach(function(t){n.has(t.name)||i(t)}),r}var jt={placement:"bottom",modifiers:[],strategy:"absolute"};function Dt(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function Lt(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,r=void 0===n?[]:n,i=e.defaultOptions,o=void 0===i?jt:i;return function(t,e,n){void 0===n&&(n=o);var i,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},jt,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},c=[],l=!1,u={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;f(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:D(t)?gt(t):t.contextElement?gt(t.contextElement):[],popper:gt(e)};var s,l,d=function(t){var e=kt(t);return O.reduce(function(t,n){return t.concat(e.filter(function(t){return t.phase===n}))},[])}((s=[].concat(r,a.options.modifiers),l=s.reduce(function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t},{}),Object.keys(l).map(function(t){return l[t]})));return a.orderedModifiers=d.filter(function(t){return t.enabled}),a.orderedModifiers.forEach(function(t){var e=t.name,n=t.options,r=void 0===n?{}:n,i=t.effect;if("function"==typeof i){var o=i({state:a,name:e,instance:u,options:r}),s=function(){};c.push(o||s)}}),u.update()},forceUpdate:function(){if(!l){var t=a.elements,e=t.reference,n=t.popper;if(Dt(e,n)){a.rects={reference:Ot(e,G(n),"fixed"===a.options.strategy),popper:W(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(t){return a.modifiersData[t.name]=Object.assign({},t.data)});for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,s=i.options,c=void 0===s?{}:s,f=i.name;"function"==typeof o&&(a=o({state:a,options:c,name:f,instance:u})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise(function(t){u.forceUpdate(),t(a)})},function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(i())})})),s}),destroy:function(){f(),l=!0}};if(!Dt(t,e))return u;function f(){c.forEach(function(t){return t()}),c=[]}return u.setOptions(n).then(function(t){!l&&n.onFirstUpdate&&n.onFirstUpdate(t)}),u}}var Nt=Lt(),Pt=Lt({defaultModifiers:[st,St,it,P,At,xt,Ct,tt,Et]}),It=Lt({defaultModifiers:[st,St,it,P]}),Mt=n(4692);const $t=new Map,Ht={set(t,e,n){$t.has(t)||$t.set(t,new Map);const r=$t.get(t);r.has(e)||0===r.size?r.set(e,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`)},get:(t,e)=>$t.has(t)&&$t.get(t).get(e)||null,remove(t,e){if(!$t.has(t))return;const n=$t.get(t);n.delete(e),0===n.size&&$t.delete(t)}},Rt="transitionend",Ft=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),t),qt=t=>null==t?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase(),Wt=t=>{t.dispatchEvent(new Event(Rt))},Bt=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),zt=t=>Bt(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(Ft(t)):null,Vt=t=>{if(!Bt(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),n=t.closest("details:not([open])");if(!n)return e;if(n!==t){const e=t.closest("summary");if(e&&e.parentNode!==n)return!1;if(null===e)return!1}return e},Ut=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),Xt=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?Xt(t.parentNode):null},Yt=()=>{},Gt=t=>{t.offsetHeight},Kt=()=>Mt&&!document.body.hasAttribute("data-bs-no-jquery")?Mt:null,Qt=[],Jt=()=>"rtl"===document.documentElement.dir,Zt=t=>{var e;e=()=>{const e=Kt();if(e){const n=t.NAME,r=e.fn[n];e.fn[n]=t.jQueryInterface,e.fn[n].Constructor=t,e.fn[n].noConflict=()=>(e.fn[n]=r,t.jQueryInterface)}},"loading"===document.readyState?(Qt.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Qt)t()}),Qt.push(e)):e()},te=(t,e=[],n=t)=>"function"==typeof t?t.call(...e):n,ee=(t,e,n=!0)=>{if(!n)return void te(t);const r=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:n}=window.getComputedStyle(t);const r=Number.parseFloat(e),i=Number.parseFloat(n);return r||i?(e=e.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(n))):0})(e)+5;let i=!1;const o=({target:n})=>{n===e&&(i=!0,e.removeEventListener(Rt,o),te(t))};e.addEventListener(Rt,o),setTimeout(()=>{i||Wt(e)},r)},ne=(t,e,n,r)=>{const i=t.length;let o=t.indexOf(e);return-1===o?!n&&r?t[i-1]:t[0]:(o+=n?1:-1,r&&(o=(o+i)%i),t[Math.max(0,Math.min(o,i-1))])},re=/[^.]*(?=\..*)\.|.*/,ie=/\..*/,oe=/::\d+$/,se={};let ae=1;const ce={mouseenter:"mouseover",mouseleave:"mouseout"},le=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function ue(t,e){return e&&`${e}::${ae++}`||t.uidEvent||ae++}function fe(t){const e=ue(t);return t.uidEvent=e,se[e]=se[e]||{},se[e]}function de(t,e,n=null){return Object.values(t).find(t=>t.callable===e&&t.delegationSelector===n)}function pe(t,e,n){const r="string"==typeof e,i=r?n:e||n;let o=ve(t);return le.has(o)||(o=t),[r,i,o]}function he(t,e,n,r,i){if("string"!=typeof e||!t)return;let[o,s,a]=pe(e,n,r);if(e in ce){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};s=t(s)}const c=fe(t),l=c[a]||(c[a]={}),u=de(l,s,o?n:null);if(u)return void(u.oneOff=u.oneOff&&i);const f=ue(s,e.replace(re,"")),d=o?function(t,e,n){return function r(i){const o=t.querySelectorAll(e);for(let{target:s}=i;s&&s!==this;s=s.parentNode)for(const a of o)if(a===s)return be(i,{delegateTarget:s}),r.oneOff&&ye.off(t,i.type,e,n),n.apply(s,[i])}}(t,n,s):function(t,e){return function n(r){return be(r,{delegateTarget:t}),n.oneOff&&ye.off(t,r.type,e),e.apply(t,[r])}}(t,s);d.delegationSelector=o?n:null,d.callable=s,d.oneOff=i,d.uidEvent=f,l[f]=d,t.addEventListener(a,d,o)}function ge(t,e,n,r,i){const o=de(e[n],r,i);o&&(t.removeEventListener(n,o,Boolean(i)),delete e[n][o.uidEvent])}function me(t,e,n,r){const i=e[n]||{};for(const[o,s]of Object.entries(i))o.includes(r)&&ge(t,e,n,s.callable,s.delegationSelector)}function ve(t){return t=t.replace(ie,""),ce[t]||t}const ye={on(t,e,n,r){he(t,e,n,r,!1)},one(t,e,n,r){he(t,e,n,r,!0)},off(t,e,n,r){if("string"!=typeof e||!t)return;const[i,o,s]=pe(e,n,r),a=s!==e,c=fe(t),l=c[s]||{},u=e.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(c))me(t,c,n,e.slice(1));for(const[n,r]of Object.entries(l)){const i=n.replace(oe,"");a&&!e.includes(i)||ge(t,c,s,r.callable,r.delegationSelector)}}else{if(!Object.keys(l).length)return;ge(t,c,s,o,i?n:null)}},trigger(t,e,n){if("string"!=typeof e||!t)return null;const r=Kt();let i=null,o=!0,s=!0,a=!1;e!==ve(e)&&r&&(i=r.Event(e,n),r(t).trigger(i),o=!i.isPropagationStopped(),s=!i.isImmediatePropagationStopped(),a=i.isDefaultPrevented());const c=be(new Event(e,{bubbles:o,cancelable:!0}),n);return a&&c.preventDefault(),s&&t.dispatchEvent(c),c.defaultPrevented&&i&&i.preventDefault(),c}};function be(t,e={}){for(const[n,r]of Object.entries(e))try{t[n]=r}catch(e){Object.defineProperty(t,n,{configurable:!0,get:()=>r})}return t}function _e(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function xe(t){return t.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const we={setDataAttribute(t,e,n){t.setAttribute(`data-bs-${xe(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${xe(e)}`)},getDataAttributes(t){if(!t)return{};const e={},n=Object.keys(t.dataset).filter(t=>t.startsWith("bs")&&!t.startsWith("bsConfig"));for(const r of n){let n=r.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),e[n]=_e(t.dataset[r])}return e},getDataAttribute:(t,e)=>_e(t.getAttribute(`data-bs-${xe(e)}`))};class Te{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const n=Bt(e)?we.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...Bt(e)?we.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,r]of Object.entries(e)){const e=t[n],i=Bt(e)?"element":qt(e);if(!new RegExp(r).test(i))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${i}" but expected type "${r}".`)}}}class Ee extends Te{constructor(t,e){super(),(t=zt(t))&&(this._element=t,this._config=this._getConfig(e),Ht.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Ht.remove(this._element,this.constructor.DATA_KEY),ye.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,n=!0){ee(t,e,n)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Ht.get(zt(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Ae=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let n=t.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),e=n&&"#"!==n?n.trim():null}return e?e.split(",").map(t=>Ft(t)).join(","):null},Se={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter(t=>t.matches(e)),parents(t,e){const n=[];let r=t.parentNode.closest(e);for(;r;)n.push(r),r=r.parentNode.closest(e);return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(n.matches(e))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>`${t}:not([tabindex^="-"])`).join(",");return this.find(e,t).filter(t=>!Ut(t)&&Vt(t))},getSelectorFromElement(t){const e=Ae(t);return e&&Se.findOne(e)?e:null},getElementFromSelector(t){const e=Ae(t);return e?Se.findOne(e):null},getMultipleElementsFromSelector(t){const e=Ae(t);return e?Se.find(e):[]}},Ce=(t,e="hide")=>{const n=`click.dismiss${t.EVENT_KEY}`,r=t.NAME;ye.on(document,n,`[data-bs-dismiss="${r}"]`,function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),Ut(this))return;const i=Se.getElementFromSelector(this)||this.closest(`.${r}`);t.getOrCreateInstance(i)[e]()})},Oe=".bs.alert",ke=`close${Oe}`,je=`closed${Oe}`;class De extends Ee{static get NAME(){return"alert"}close(){if(ye.trigger(this._element,ke).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,t)}_destroyElement(){this._element.remove(),ye.trigger(this._element,je),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=De.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}})}}Ce(De,"close"),Zt(De);const Le='[data-bs-toggle="button"]';class Ne extends Ee{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each(function(){const e=Ne.getOrCreateInstance(this);"toggle"===t&&e[t]()})}}ye.on(document,"click.bs.button.data-api",Le,t=>{t.preventDefault();const e=t.target.closest(Le);Ne.getOrCreateInstance(e).toggle()}),Zt(Ne);const Pe=".bs.swipe",Ie=`touchstart${Pe}`,Me=`touchmove${Pe}`,$e=`touchend${Pe}`,He=`pointerdown${Pe}`,Re=`pointerup${Pe}`,Fe={endCallback:null,leftCallback:null,rightCallback:null},qe={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class We extends Te{constructor(t,e){super(),this._element=t,t&&We.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Fe}static get DefaultType(){return qe}static get NAME(){return"swipe"}dispose(){ye.off(this._element,Pe)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),te(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&te(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(ye.on(this._element,He,t=>this._start(t)),ye.on(this._element,Re,t=>this._end(t)),this._element.classList.add("pointer-event")):(ye.on(this._element,Ie,t=>this._start(t)),ye.on(this._element,Me,t=>this._move(t)),ye.on(this._element,$e,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Be=".bs.carousel",ze=".data-api",Ve="ArrowLeft",Ue="ArrowRight",Xe="next",Ye="prev",Ge="left",Ke="right",Qe=`slide${Be}`,Je=`slid${Be}`,Ze=`keydown${Be}`,tn=`mouseenter${Be}`,en=`mouseleave${Be}`,nn=`dragstart${Be}`,rn=`load${Be}${ze}`,on=`click${Be}${ze}`,sn="carousel",an="active",cn=".active",ln=".carousel-item",un=cn+ln,fn={[Ve]:Ke,[Ue]:Ge},dn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},pn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class hn extends Ee{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Se.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===sn&&this.cycle()}static get Default(){return dn}static get DefaultType(){return pn}static get NAME(){return"carousel"}next(){this._slide(Xe)}nextWhenVisible(){!document.hidden&&Vt(this._element)&&this.next()}prev(){this._slide(Ye)}pause(){this._isSliding&&Wt(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?ye.one(this._element,Je,()=>this.cycle()):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void ye.one(this._element,Je,()=>this.to(t));const n=this._getItemIndex(this._getActive());if(n===t)return;const r=t>n?Xe:Ye;this._slide(r,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&ye.on(this._element,Ze,t=>this._keydown(t)),"hover"===this._config.pause&&(ye.on(this._element,tn,()=>this.pause()),ye.on(this._element,en,()=>this._maybeEnableCycle())),this._config.touch&&We.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of Se.find(".carousel-item img",this._element))ye.on(t,nn,t=>t.preventDefault());const t={leftCallback:()=>this._slide(this._directionToOrder(Ge)),rightCallback:()=>this._slide(this._directionToOrder(Ke)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new We(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=fn[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=Se.findOne(cn,this._indicatorsElement);e.classList.remove(an),e.removeAttribute("aria-current");const n=Se.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);n&&(n.classList.add(an),n.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const n=this._getActive(),r=t===Xe,i=e||ne(this._getItems(),n,r,this._config.wrap);if(i===n)return;const o=this._getItemIndex(i),s=e=>ye.trigger(this._element,e,{relatedTarget:i,direction:this._orderToDirection(t),from:this._getItemIndex(n),to:o});if(s(Qe).defaultPrevented)return;if(!n||!i)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i;const c=r?"carousel-item-start":"carousel-item-end",l=r?"carousel-item-next":"carousel-item-prev";i.classList.add(l),Gt(i),n.classList.add(c),i.classList.add(c);this._queueCallback(()=>{i.classList.remove(c,l),i.classList.add(an),n.classList.remove(an,l,c),this._isSliding=!1,s(Je)},n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Se.findOne(un,this._element)}_getItems(){return Se.find(ln,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return Jt()?t===Ge?Ye:Xe:t===Ge?Xe:Ye}_orderToDirection(t){return Jt()?t===Ye?Ge:Ke:t===Ye?Ke:Ge}static jQueryInterface(t){return this.each(function(){const e=hn.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)})}}ye.on(document,on,"[data-bs-slide], [data-bs-slide-to]",function(t){const e=Se.getElementFromSelector(this);if(!e||!e.classList.contains(sn))return;t.preventDefault();const n=hn.getOrCreateInstance(e),r=this.getAttribute("data-bs-slide-to");return r?(n.to(r),void n._maybeEnableCycle()):"next"===we.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())}),ye.on(window,rn,()=>{const t=Se.find('[data-bs-ride="carousel"]');for(const e of t)hn.getOrCreateInstance(e)}),Zt(hn);const gn=".bs.collapse",mn=`show${gn}`,vn=`shown${gn}`,yn=`hide${gn}`,bn=`hidden${gn}`,_n=`click${gn}.data-api`,xn="show",wn="collapse",Tn="collapsing",En=`:scope .${wn} .${wn}`,An='[data-bs-toggle="collapse"]',Sn={parent:null,toggle:!0},Cn={parent:"(null|element)",toggle:"boolean"};class On extends Ee{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const n=Se.find(An);for(const t of n){const e=Se.getSelectorFromElement(t),n=Se.find(e).filter(t=>t===this._element);null!==e&&n.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Sn}static get DefaultType(){return Cn}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(t=>t!==this._element).map(t=>On.getOrCreateInstance(t,{toggle:!1}))),t.length&&t[0]._isTransitioning)return;if(ye.trigger(this._element,mn).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(wn),this._element.classList.add(Tn),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Tn),this._element.classList.add(wn,xn),this._element.style[e]="",ye.trigger(this._element,vn)},this._element,!0),this._element.style[e]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(ye.trigger(this._element,yn).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,Gt(this._element),this._element.classList.add(Tn),this._element.classList.remove(wn,xn);for(const t of this._triggerArray){const e=Se.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Tn),this._element.classList.add(wn),ye.trigger(this._element,bn)},this._element,!0)}_isShown(t=this._element){return t.classList.contains(xn)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=zt(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(An);for(const e of t){const t=Se.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=Se.find(En,this._config.parent);return Se.find(t,this._config.parent).filter(t=>!e.includes(t))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const n of t)n.classList.toggle("collapsed",!e),n.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const n=On.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t]()}})}}ye.on(document,_n,An,function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of Se.getMultipleElementsFromSelector(this))On.getOrCreateInstance(t,{toggle:!1}).toggle()}),Zt(On);const kn="dropdown",jn=".bs.dropdown",Dn=".data-api",Ln="ArrowUp",Nn="ArrowDown",Pn=`hide${jn}`,In=`hidden${jn}`,Mn=`show${jn}`,$n=`shown${jn}`,Hn=`click${jn}${Dn}`,Rn=`keydown${jn}${Dn}`,Fn=`keyup${jn}${Dn}`,qn="show",Wn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Bn=`${Wn}.${qn}`,zn=".dropdown-menu",Vn=Jt()?"top-end":"top-start",Un=Jt()?"top-start":"top-end",Xn=Jt()?"bottom-end":"bottom-start",Yn=Jt()?"bottom-start":"bottom-end",Gn=Jt()?"left-start":"right-start",Kn=Jt()?"right-start":"left-start",Qn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Jn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Zn extends Ee{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=Se.next(this._element,zn)[0]||Se.prev(this._element,zn)[0]||Se.findOne(zn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Qn}static get DefaultType(){return Jn}static get NAME(){return kn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Ut(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!ye.trigger(this._element,Mn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))ye.on(t,"mouseover",Yt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(qn),this._element.classList.add(qn),ye.trigger(this._element,$n,t)}}hide(){if(Ut(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!ye.trigger(this._element,Pn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ye.off(t,"mouseover",Yt);this._popper&&this._popper.destroy(),this._menu.classList.remove(qn),this._element.classList.remove(qn),this._element.setAttribute("aria-expanded","false"),we.removeDataAttribute(this._menu,"popper"),ye.trigger(this._element,In,t),this._element.focus()}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!Bt(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${kn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){let t=this._element;"parent"===this._config.reference?t=this._parent:Bt(this._config.reference)?t=zt(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Pt(t,this._menu,e)}_isShown(){return this._menu.classList.contains(qn)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Gn;if(t.classList.contains("dropstart"))return Kn;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Un:Vn:e?Yn:Xn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(we.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...te(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const n=Se.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(t=>Vt(t));n.length&&ne(n,e,t===Nn,!n.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=Zn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=Se.find(Bn);for(const n of e){const e=Zn.getInstance(n);if(!e||!1===e._config.autoClose)continue;const r=t.composedPath(),i=r.includes(e._menu);if(r.includes(e._element)||"inside"===e._config.autoClose&&!i||"outside"===e._config.autoClose&&i)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),n="Escape"===t.key,r=[Ln,Nn].includes(t.key);if(!r&&!n)return;if(e&&!n)return;t.preventDefault();const i=this.matches(Wn)?this:Se.prev(this,Wn)[0]||Se.next(this,Wn)[0]||Se.findOne(Wn,t.delegateTarget.parentNode),o=Zn.getOrCreateInstance(i);if(r)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),i.focus())}}ye.on(document,Rn,Wn,Zn.dataApiKeydownHandler),ye.on(document,Rn,zn,Zn.dataApiKeydownHandler),ye.on(document,Hn,Zn.clearMenus),ye.on(document,Fn,Zn.clearMenus),ye.on(document,Hn,Wn,function(t){t.preventDefault(),Zn.getOrCreateInstance(this).toggle()}),Zt(Zn);const tr="backdrop",er="show",nr=`mousedown.bs.${tr}`,rr={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ir={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class or extends Te{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return rr}static get DefaultType(){return ir}static get NAME(){return tr}show(t){if(!this._config.isVisible)return void te(t);this._append();const e=this._getElement();this._config.isAnimated&&Gt(e),e.classList.add(er),this._emulateAnimation(()=>{te(t)})}hide(t){this._config.isVisible?(this._getElement().classList.remove(er),this._emulateAnimation(()=>{this.dispose(),te(t)})):te(t)}dispose(){this._isAppended&&(ye.off(this._element,nr),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=zt(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),ye.on(t,nr,()=>{te(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){ee(t,this._getElement(),this._config.isAnimated)}}const sr=".bs.focustrap",ar=`focusin${sr}`,cr=`keydown.tab${sr}`,lr="backward",ur={autofocus:!0,trapElement:null},fr={autofocus:"boolean",trapElement:"element"};class dr extends Te{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ur}static get DefaultType(){return fr}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),ye.off(document,sr),ye.on(document,ar,t=>this._handleFocusin(t)),ye.on(document,cr,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,ye.off(document,sr))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const n=Se.focusableChildren(e);0===n.length?e.focus():this._lastTabNavDirection===lr?n[n.length-1].focus():n[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?lr:"forward")}}const pr=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",hr=".sticky-top",gr="padding-right",mr="margin-right";class vr{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,gr,e=>e+t),this._setElementAttributes(pr,gr,e=>e+t),this._setElementAttributes(hr,mr,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,gr),this._resetElementAttributes(pr,gr),this._resetElementAttributes(hr,mr)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,n){const r=this.getWidth();this._applyManipulationCallback(t,t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+r)return;this._saveInitialAttribute(t,e);const i=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${n(Number.parseFloat(i))}px`)})}_saveInitialAttribute(t,e){const n=t.style.getPropertyValue(e);n&&we.setDataAttribute(t,e,n)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,t=>{const n=we.getDataAttribute(t,e);null!==n?(we.removeDataAttribute(t,e),t.style.setProperty(e,n)):t.style.removeProperty(e)})}_applyManipulationCallback(t,e){if(Bt(t))e(t);else for(const n of Se.find(t,this._element))e(n)}}const yr=".bs.modal",br=`hide${yr}`,_r=`hidePrevented${yr}`,xr=`hidden${yr}`,wr=`show${yr}`,Tr=`shown${yr}`,Er=`resize${yr}`,Ar=`click.dismiss${yr}`,Sr=`mousedown.dismiss${yr}`,Cr=`keydown.dismiss${yr}`,Or=`click${yr}.data-api`,kr="modal-open",jr="show",Dr="modal-static",Lr={backdrop:!0,focus:!0,keyboard:!0},Nr={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Pr extends Ee{constructor(t,e){super(t,e),this._dialog=Se.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new vr,this._addEventListeners()}static get Default(){return Lr}static get DefaultType(){return Nr}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||this._isTransitioning)return;ye.trigger(this._element,wr,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(kr),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){if(!this._isShown||this._isTransitioning)return;ye.trigger(this._element,br).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(jr),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){ye.off(window,yr),ye.off(this._dialog,yr),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new or({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new dr({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=Se.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),Gt(this._element),this._element.classList.add(jr);this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,ye.trigger(this._element,Tr,{relatedTarget:t})},this._dialog,this._isAnimated())}_addEventListeners(){ye.on(this._element,Cr,t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),ye.on(window,Er,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),ye.on(this._element,Sr,t=>{ye.one(this._element,Ar,e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(kr),this._resetAdjustments(),this._scrollBar.reset(),ye.trigger(this._element,xr)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(ye.trigger(this._element,_r).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Dr)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Dr),this._queueCallback(()=>{this._element.classList.remove(Dr),this._queueCallback(()=>{this._element.style.overflowY=e},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),n=e>0;if(n&&!t){const t=Jt()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!n&&t){const t=Jt()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const n=Pr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===n[t])throw new TypeError(`No method named "${t}"`);n[t](e)}})}}ye.on(document,Or,'[data-bs-toggle="modal"]',function(t){const e=Se.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),ye.one(e,wr,t=>{t.defaultPrevented||ye.one(e,xr,()=>{Vt(this)&&this.focus()})});const n=Se.findOne(".modal.show");n&&Pr.getInstance(n).hide();Pr.getOrCreateInstance(e).toggle(this)}),Ce(Pr),Zt(Pr);const Ir=".bs.offcanvas",Mr=".data-api",$r=`load${Ir}${Mr}`,Hr="show",Rr="showing",Fr="hiding",qr=".offcanvas.show",Wr=`show${Ir}`,Br=`shown${Ir}`,zr=`hide${Ir}`,Vr=`hidePrevented${Ir}`,Ur=`hidden${Ir}`,Xr=`resize${Ir}`,Yr=`click${Ir}${Mr}`,Gr=`keydown.dismiss${Ir}`,Kr={backdrop:!0,keyboard:!0,scroll:!1},Qr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Jr extends Ee{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Kr}static get DefaultType(){return Qr}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown)return;if(ye.trigger(this._element,Wr,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new vr).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Rr);this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Hr),this._element.classList.remove(Rr),ye.trigger(this._element,Br,{relatedTarget:t})},this._element,!0)}hide(){if(!this._isShown)return;if(ye.trigger(this._element,zr).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Fr),this._backdrop.hide();this._queueCallback(()=>{this._element.classList.remove(Hr,Fr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new vr).reset(),ye.trigger(this._element,Ur)},this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new or({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():ye.trigger(this._element,Vr)}:null})}_initializeFocusTrap(){return new dr({trapElement:this._element})}_addEventListeners(){ye.on(this._element,Gr,t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():ye.trigger(this._element,Vr))})}static jQueryInterface(t){return this.each(function(){const e=Jr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}})}}ye.on(document,Yr,'[data-bs-toggle="offcanvas"]',function(t){const e=Se.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),Ut(this))return;ye.one(e,Ur,()=>{Vt(this)&&this.focus()});const n=Se.findOne(qr);n&&n!==e&&Jr.getInstance(n).hide();Jr.getOrCreateInstance(e).toggle(this)}),ye.on(window,$r,()=>{for(const t of Se.find(qr))Jr.getOrCreateInstance(t).show()}),ye.on(window,Xr,()=>{for(const t of Se.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Jr.getOrCreateInstance(t).hide()}),Ce(Jr),Zt(Jr);const Zr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ti=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ei=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ni=(t,e)=>{const n=t.nodeName.toLowerCase();return e.includes(n)?!ti.has(n)||Boolean(ei.test(t.nodeValue)):e.filter(t=>t instanceof RegExp).some(t=>t.test(n))};const ri={allowList:Zr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ii={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},oi={entry:"(string|element|function|null)",selector:"(string|element)"};class si extends Te{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return ri}static get DefaultType(){return ii}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,n]of Object.entries(this._config.content))this._setContent(t,n,e);const e=t.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&e.classList.add(...n.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,n]of Object.entries(t))super._typeCheckConfig({selector:e,entry:n},oi)}_setContent(t,e,n){const r=Se.findOne(n,t);r&&((e=this._resolvePossibleFunction(e))?Bt(e)?this._putElementInTemplate(zt(e),r):this._config.html?r.innerHTML=this._maybeSanitize(e):r.textContent=e:r.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,n){if(!t.length)return t;if(n&&"function"==typeof n)return n(t);const r=(new window.DOMParser).parseFromString(t,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const t of i){const n=t.nodeName.toLowerCase();if(!Object.keys(e).includes(n)){t.remove();continue}const r=[].concat(...t.attributes),i=[].concat(e["*"]||[],e[n]||[]);for(const e of r)ni(e,i)||t.removeAttribute(e.nodeName)}return r.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return te(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const ai=new Set(["sanitize","allowList","sanitizeFn"]),ci="fade",li="show",ui=".tooltip-inner",fi=".modal",di="hide.bs.modal",pi="hover",hi="focus",gi="click",mi={AUTO:"auto",TOP:"top",RIGHT:Jt()?"left":"right",BOTTOM:"bottom",LEFT:Jt()?"right":"left"},vi={allowList:Zr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},yi={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class bi extends Ee{constructor(t,e){super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return vi}static get DefaultType(){return yi}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),ye.off(this._element.closest(fi),di,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=ye.trigger(this._element,this.constructor.eventName("show")),e=(Xt(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(n),ye.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(li),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ye.on(t,"mouseover",Yt);this._queueCallback(()=>{ye.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(ye.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(li),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))ye.off(t,"mouseover",Yt);this._activeTrigger[gi]=!1,this._activeTrigger[hi]=!1,this._activeTrigger[pi]=!1,this._isHovered=null;this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),ye.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(ci,li),e.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",n),this._isAnimated()&&e.classList.add(ci),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new si({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ui]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ci)}_isShown(){return this.tip&&this.tip.classList.contains(li)}_createPopper(t){const e=te(this._config.placement,[this,t,this._element]),n=mi[e.toUpperCase()];return Pt(this._element,t,this._getPopperConfig(n))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return te(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...te(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)ye.on(this._element,this.constructor.eventName("click"),this._config.selector,t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger[gi]=!(e._isShown()&&e._activeTrigger[gi]),e.toggle()});else if("manual"!==e){const t=e===pi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=e===pi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");ye.on(this._element,t,this._config.selector,t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?hi:pi]=!0,e._enter()}),ye.on(this._element,n,this._config.selector,t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?hi:pi]=e._element.contains(t.relatedTarget),e._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},ye.on(this._element.closest(fi),di,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=we.getDataAttributes(this._element);for(const t of Object.keys(e))ai.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:zt(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,n]of Object.entries(this._config))this.constructor.Default[e]!==n&&(t[e]=n);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=bi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}Zt(bi);const _i=".popover-header",xi=".popover-body",wi={...bi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Ti={...bi.DefaultType,content:"(null|string|element|function)"};class Ei extends bi{static get Default(){return wi}static get DefaultType(){return Ti}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_i]:this._getTitle(),[xi]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=Ei.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}Zt(Ei);const Ai=".bs.scrollspy",Si=`activate${Ai}`,Ci=`click${Ai}`,Oi=`load${Ai}.data-api`,ki="active",ji="[href]",Di=".nav-link",Li=`${Di}, .nav-item > ${Di}, .list-group-item`,Ni={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Pi={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ii extends Ee{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ni}static get DefaultType(){return Pi}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=zt(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map(t=>Number.parseFloat(t))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(ye.off(this._config.target,Ci),ye.on(this._config.target,Ci,ji,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const n=this._rootElement||window,r=e.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:r,behavior:"smooth"});n.scrollTop=r}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),n=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&t){if(n(o),!r)return}else i||t||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=Se.find(ji,this._config.target);for(const e of t){if(!e.hash||Ut(e))continue;const t=Se.findOne(decodeURI(e.hash),this._element);Vt(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(ki),this._activateParents(t),ye.trigger(this._element,Si,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))Se.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(ki);else for(const e of Se.parents(t,".nav, .list-group"))for(const t of Se.prev(e,Li))t.classList.add(ki)}_clearActiveClass(t){t.classList.remove(ki);const e=Se.find(`${ji}.${ki}`,t);for(const t of e)t.classList.remove(ki)}static jQueryInterface(t){return this.each(function(){const e=Ii.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}})}}ye.on(window,Oi,()=>{for(const t of Se.find('[data-bs-spy="scroll"]'))Ii.getOrCreateInstance(t)}),Zt(Ii);const Mi=".bs.tab",$i=`hide${Mi}`,Hi=`hidden${Mi}`,Ri=`show${Mi}`,Fi=`shown${Mi}`,qi=`click${Mi}`,Wi=`keydown${Mi}`,Bi=`load${Mi}`,zi="ArrowLeft",Vi="ArrowRight",Ui="ArrowUp",Xi="ArrowDown",Yi="Home",Gi="End",Ki="active",Qi="fade",Ji="show",Zi=".dropdown-toggle",to=`:not(${Zi})`,eo='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',no=`${`.nav-link${to}, .list-group-item${to}, [role="tab"]${to}`}, ${eo}`,ro=`.${Ki}[data-bs-toggle="tab"], .${Ki}[data-bs-toggle="pill"], .${Ki}[data-bs-toggle="list"]`;class io extends Ee{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),ye.on(this._element,Wi,t=>this._keydown(t)))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),n=e?ye.trigger(e,$i,{relatedTarget:t}):null;ye.trigger(t,Ri,{relatedTarget:e}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(Ki),this._activate(Se.getElementFromSelector(t));this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),ye.trigger(t,Fi,{relatedTarget:e})):t.classList.add(Ji)},t,t.classList.contains(Qi))}_deactivate(t,e){if(!t)return;t.classList.remove(Ki),t.blur(),this._deactivate(Se.getElementFromSelector(t));this._queueCallback(()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),ye.trigger(t,Hi,{relatedTarget:e})):t.classList.remove(Ji)},t,t.classList.contains(Qi))}_keydown(t){if(![zi,Vi,Ui,Xi,Yi,Gi].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(t=>!Ut(t));let n;if([Yi,Gi].includes(t.key))n=e[t.key===Yi?0:e.length-1];else{const r=[Vi,Xi].includes(t.key);n=ne(e,t.target,r,!0)}n&&(n.focus({preventScroll:!0}),io.getOrCreateInstance(n).show())}_getChildren(){return Se.find(no,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),n=this._getOuterElement(t);t.setAttribute("aria-selected",e),n!==t&&this._setAttributeIfNotExists(n,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=Se.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const n=this._getOuterElement(t);if(!n.classList.contains("dropdown"))return;const r=(t,r)=>{const i=Se.findOne(t,n);i&&i.classList.toggle(r,e)};r(Zi,Ki),r(".dropdown-menu",Ji),n.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,n){t.hasAttribute(e)||t.setAttribute(e,n)}_elemIsActive(t){return t.classList.contains(Ki)}_getInnerElement(t){return t.matches(no)?t:Se.findOne(no,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each(function(){const e=io.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}})}}ye.on(document,qi,eo,function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),Ut(this)||io.getOrCreateInstance(this).show()}),ye.on(window,Bi,()=>{for(const t of Se.find(ro))io.getOrCreateInstance(t)}),Zt(io);const oo=".bs.toast",so=`mouseover${oo}`,ao=`mouseout${oo}`,co=`focusin${oo}`,lo=`focusout${oo}`,uo=`hide${oo}`,fo=`hidden${oo}`,po=`show${oo}`,ho=`shown${oo}`,go="hide",mo="show",vo="showing",yo={animation:"boolean",autohide:"boolean",delay:"number"},bo={animation:!0,autohide:!0,delay:5e3};class _o extends Ee{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return bo}static get DefaultType(){return yo}static get NAME(){return"toast"}show(){if(ye.trigger(this._element,po).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(go),Gt(this._element),this._element.classList.add(mo,vo),this._queueCallback(()=>{this._element.classList.remove(vo),ye.trigger(this._element,ho),this._maybeScheduleHide()},this._element,this._config.animation)}hide(){if(!this.isShown())return;if(ye.trigger(this._element,uo).defaultPrevented)return;this._element.classList.add(vo),this._queueCallback(()=>{this._element.classList.add(go),this._element.classList.remove(vo,mo),ye.trigger(this._element,fo)},this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(mo),super.dispose()}isShown(){return this._element.classList.contains(mo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const n=t.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){ye.on(this._element,so,t=>this._onInteraction(t,!0)),ye.on(this._element,ao,t=>this._onInteraction(t,!1)),ye.on(this._element,co,t=>this._onInteraction(t,!0)),ye.on(this._element,lo,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=_o.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}})}}function xo(t){return function(t){if(Array.isArray(t))return wo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return wo(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?wo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wo(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}Ce(_o),Zt(_o),globalThis.$=globalThis.jQuery=i(),globalThis.bootstrap=e,document.addEventListener("DOMContentLoaded",function(){xo(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(t){return new bi(t)}),globalThis.flashMessage=function(t){var e=t.message,n=void 0===e?"":e,r=t.reload,i=void 0!==r&&r,o=t.type,s=void 0===o?"info":o,a="flash-message",c=sessionStorage.getItem(a);if(""===n&&c)return FOSSBilling.message(c,s),void sessionStorage.removeItem(a);n&&(sessionStorage.setItem(a,n),"boolean"==typeof i&&i?bb.reload():"string"==typeof i&&bb.redirect(i))},flashMessage({}),document.querySelectorAll("input[required], textarea[required]").forEach(function(t){var e=t.previousElementSibling;if(!t.parentElement.parentElement.classList.contains("auth")&&e&&"label"===e.tagName.toLowerCase()){var n=document.createElement("span");n.textContent=" *",n.classList.add("text-danger"),e.appendChild(n)}}),document.querySelectorAll("select.currency_selector").forEach(function(t){t.addEventListener("change",function(){API.guest.post("cart/set_currency",{currency:t.value},function(t){location.reload()},function(t){FOSSBilling.message(t)})})})})})()})();