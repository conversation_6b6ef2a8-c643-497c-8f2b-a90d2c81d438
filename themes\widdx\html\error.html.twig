{% extends "layout_default.html.twig" %}

{% block meta_title %}{{ 'Error'|trans }} {{ exception.getCode }}{% endblock %}

{% block body_class %}error-page{% endblock %}

{% block breadcrumb %}
    <li class="active breadcrumb-item" aria-current="page">{{ exception.getCode }}</li>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-12 my-5">
            <div class="d-flex flex-column py-5">
                <div class="d-flex justify-content-center">
                    {% if exception.getCode %}
                        <h1 class="display-1">{{ exception.getCode }}</h1>
                    {% else %}
                        <h1 class="display-1">404</h1>
                    {% endif %}
                </div>
                <div class="d-flex flex-column justify-content-center text-center">
                    <span>{{ 'Whoops! Unable to find the page you are looking for'|trans }}.</span>
                    <span>{{ exception.getMessage }}</span>
                    <div class="mt-3">
                        <a class="btn btn-sm btn-primary" href="{{ '/' | link}}">{{ 'Go back to home'|trans }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
