
export default {
	options: [],
	optgroups: [],

	plugins: [],
	delimiter: ',',
	splitOn: null, // regexp or string for splitting up values from a paste command
	persist: true,
	diacritics: true,
	create: null,
	createOnBlur: false,
	createFilter: null,
	highlight: true,
	openOnFocus: true,
	shouldOpen: null,
	maxOptions: 50,
	maxItems: null,
	hideSelected: null,
	duplicates: false,
	addPrecedence: false,
	selectOnTab: false,
	preload: null,
	allowEmptyOption: false,
	//closeAfterSelect: false,
	refreshThrottle: 300,


	loadThrottle: 300,
	loadingClass: 'loading',

	dataAttr: null, //'data-data',
	optgroupField: 'optgroup',
	valueField: 'value',
	labelField: 'text',
	disabledField: 'disabled',
	optgroupLabelField: 'label',
	optgroupValueField: 'value',
	lockOptgroupOrder: false,

	sortField: '$order',
	searchField: ['text'],
	searchConjunction: 'and',

	mode: null,
	wrapperClass: 'ts-wrapper',
	controlClass: 'ts-control',
	dropdownClass: 'ts-dropdown',
	dropdownContentClass: 'ts-dropdown-content',
	itemClass: 'item',
	optionClass: 'option',

	dropdownParent: null,
	controlInput: '<input type="text" autocomplete="off" size="1" />',

	copyClassesToDropdown: false,

	placeholder: null,
	hidePlaceholder: null,

	shouldLoad: function(query:string):boolean{
		return query.length > 0;
	},

	/*
	load                 : null, // function(query, callback) { ... }
	score                : null, // function(search) { ... }
	onInitialize         : null, // function() { ... }
	onChange             : null, // function(value) { ... }
	onItemAdd            : null, // function(value, $item) { ... }
	onItemRemove         : null, // function(value) { ... }
	onClear              : null, // function() { ... }
	onOptionAdd          : null, // function(value, data) { ... }
	onOptionRemove       : null, // function(value) { ... }
	onOptionClear        : null, // function() { ... }
	onOptionGroupAdd     : null, // function(id, data) { ... }
	onOptionGroupRemove  : null, // function(id) { ... }
	onOptionGroupClear   : null, // function() { ... }
	onDropdownOpen       : null, // function(dropdown) { ... }
	onDropdownClose      : null, // function(dropdown) { ... }
	onType               : null, // function(str) { ... }
	onDelete             : null, // function(values) { ... }
	*/

	render: {
		/*
		item: null,
		optgroup: null,
		optgroup_header: null,
		option: null,
		option_create: null
		*/
	}
};
