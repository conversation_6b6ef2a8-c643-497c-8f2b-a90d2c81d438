<!DOCTYPE html>
<html class="no-js" lang="en" data-bs-theme="{{ settings.theme|default('light') }}">
<head>
    <meta charset="utf-8">
    <title>{{ settings.meta_title_prefix }} {% block meta_title %}{% endblock %} {{ settings.meta_title_suffix }}</title>

    <meta property="bb:url" content="{{ constant('SYSTEM_URL') }}">
    <meta property="bb:client_area" content="{{ '/'|link }}">
    <meta name="csrf-token" content="{{ CSRFToken }}">

    <meta name="description" content="{% block meta_description %}{{ settings.meta_description }}{% endblock %}">
    <meta name="keywords" content="{{ settings.meta_keywords }}">
    <meta name="robots" content="{{ settings.meta_robots }}">
    <meta name="author" content="{{ settings.meta_author }}">
    <meta name="generator" content="FOSSBilling">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% block opengraph %}{% endblock %}

    <link rel='stylesheet' type='text/css' href="{{ 'css/font-awesome.css' | asset_url }}">
    {{ encore_entry_link_tags('huraga') }}

    <link rel="shortcut icon" href="{{ guest.system_company.favicon_url }}">

    {{ "Api/API.js" | library_url | script_tag }}
    {{ encore_entry_script_tags('huraga') }}
    {% block head %}{% endblock %}
    {% block js %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    {% block body %}{% endblock %}
    <div aria-live="polite" aria-atomic="true" class="position-relative">
        <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1070;"></div>
    </div>
    {{ include('partial_pending_messages.html.twig', ignore_missing: true) }}
</body>
</html>
