(()=>{var e={51:(e,t,n)=>{"use strict";var r=n(2484),i=n(2675),o=n(2748),s=Object,a=r("".split);e.exports=i(function(){return!s("z").propertyIsEnumerable(0)})?function(e){return"String"===o(e)?a(e,""):s(e)}:s},92:(e,t,n)=>{"use strict";var r=n(6891);e.exports=r&&!!Symbol.for&&!!Symbol.keyFor},121:(e,t,n)=>{"use strict";var r=n(2484),i=2147483647,o=/[^\0-\u007E]/,s=/[.\u3002\uFF0E\uFF61]/g,a="Overflow: input needs wider integers to process",l=RangeError,c=r(s.exec),u=Math.floor,d=String.fromCharCode,f=r("".charCodeAt),p=r([].join),h=r([].push),g=r("".replace),m=r("".split),v=r("".toLowerCase),y=function(e){return e+22+75*(e<26)},b=function(e,t,n){var r=0;for(e=n?u(e/700):e>>1,e+=u(e/t);e>455;)e=u(e/35),r+=36;return u(r+36*e/(e+38))},_=function(e){var t=[];e=function(e){for(var t=[],n=0,r=e.length;n<r;){var i=f(e,n++);if(i>=55296&&i<=56319&&n<r){var o=f(e,n++);56320==(64512&o)?h(t,((1023&i)<<10)+(1023&o)+65536):(h(t,i),n--)}else h(t,i)}return t}(e);var n,r,o=e.length,s=128,c=0,g=72;for(n=0;n<e.length;n++)(r=e[n])<128&&h(t,d(r));var m=t.length,v=m;for(m&&h(t,"-");v<o;){var _=i;for(n=0;n<e.length;n++)(r=e[n])>=s&&r<_&&(_=r);var w=v+1;if(_-s>u((i-c)/w))throw new l(a);for(c+=(_-s)*w,s=_,n=0;n<e.length;n++){if((r=e[n])<s&&++c>i)throw new l(a);if(r===s){for(var x=c,S=36;;){var O=S<=g?1:S>=g+26?26:S-g;if(x<O)break;var A=x-O,E=36-O;h(t,d(y(O+A%E))),x=u(A/E),S+=36}h(t,d(y(x))),g=b(c,w,v===m),c=0,v++}}c++,s++}return p(t,"")};e.exports=function(e){var t,n,r=[],i=m(g(v(e),s,"."),".");for(t=0;t<i.length;t++)n=i[t],h(r,c(o,n)?"xn--"+_(n):n);return p(r,".")}},133:(e,t,n)=>{"use strict";var r=n(3588),i=Function.prototype,o=i.apply,s=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(o):function(){return s.apply(o,arguments)})},321:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},341:(e,t,n)=>{"use strict";var r=n(5034),i=n(9976),o=n(7032).CONSTRUCTOR;e.exports=o||!i(function(e){r.all(e).then(void 0,function(){})})},381:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},383:(e,t,n)=>{"use strict";var r=n(8810),i=n(6007),o=n(7032).CONSTRUCTOR,s=n(5034),a=n(3163),l=n(321),c=n(5236),u=s&&s.prototype;if(r({target:"Promise",proto:!0,forced:o,real:!0},{catch:function(e){return this.then(void 0,e)}}),!i&&l(s)){var d=a("Promise").prototype.catch;u.catch!==d&&c(u,"catch",d,{unsafe:!0})}},391:(e,t,n)=>{"use strict";n(4515)("iterator")},461:(e,t,n)=>{"use strict";var r=n(3163),i=n(6038),o=n(4175),s=n(2128),a=o("species");e.exports=function(e){var t=r(e);s&&t&&!t[a]&&i(t,a,{configurable:!0,get:function(){return this}})}},464:e=>{"use strict";var t=TypeError;e.exports=function(e,n){if(e<n)throw new t("Not enough arguments");return e}},552:(e,t,n)=>{"use strict";var r=n(3291);e.exports=/web0s(?!.*chrome)/i.test(r)},562:(e,t,n)=>{"use strict";var r=n(8810),i=n(2961).map;r({target:"Array",proto:!0,forced:!n(2321)("map")},{map:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},573:(e,t,n)=>{"use strict";var r=n(3625),i=n(4102),o=n(3401),s=n(9538),a=n(5514),l=n(4175),c=TypeError,u=l("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var n,l=s(e,u);if(l){if(void 0===t&&(t="default"),n=r(l,e,t),!i(n)||o(n))return n;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},600:(e,t,n)=>{"use strict";var r=n(9004),i=n(3625),o=n(8649),s=n(4035),a=n(3709),l=n(1441),c=n(8770),u=n(4028),d=n(6221),f=n(4951),p=Array;e.exports=function(e){var t=o(e),n=l(this),h=arguments.length,g=h>1?arguments[1]:void 0,m=void 0!==g;m&&(g=r(g,h>2?arguments[2]:void 0));var v,y,b,_,w,x,S=f(t),O=0;if(!S||this===p&&a(S))for(v=c(t),y=n?new this(v):p(v);v>O;O++)x=m?g(t[O],O):t[O],u(y,O,x);else for(y=n?new this:[],w=(_=d(t,S)).next;!(b=i(w,_)).done;O++)x=m?s(_,g,[b.value,O],!0):b.value,u(y,O,x);return y.length=O,y}},659:(e,t,n)=>{"use strict";var r=n(4102),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not an object")}},671:(e,t,n)=>{"use strict";var r=n(2128),i=n(6005),o=n(5936);e.exports=r?function(e,t,n){return i.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},706:(e,t,n)=>{"use strict";var r=n(2128),i=n(2675);e.exports=r&&i(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},861:(e,t,n)=>{"use strict";var r=n(8810),i=n(2484),o=n(6526),s=RangeError,a=String.fromCharCode,l=String.fromCodePoint,c=i([].join);r({target:"String",stat:!0,arity:1,forced:!!l&&1!==l.length},{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,i=0;r>i;){if(t=+arguments[i++],o(t,1114111)!==t)throw new s(t+" is not a valid code point");n[i]=t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320)}return c(n,"")}})},863:(e,t,n)=>{"use strict";var r,i,o=n(3625),s=n(2484),a=n(7267),l=n(8303),c=n(2537),u=n(6445),d=n(3844),f=n(6369).get,p=n(991),h=n(5722),g=u("native-string-replace",String.prototype.replace),m=RegExp.prototype.exec,v=m,y=s("".charAt),b=s("".indexOf),_=s("".replace),w=s("".slice),x=(i=/b*/g,o(m,r=/a/,"a"),o(m,i,"a"),0!==r.lastIndex||0!==i.lastIndex),S=c.BROKEN_CARET,O=void 0!==/()??/.exec("")[1];(x||O||S||p||h)&&(v=function(e){var t,n,r,i,s,c,u,p=this,h=f(p),A=a(e),E=h.raw;if(E)return E.lastIndex=p.lastIndex,t=o(v,E,A),p.lastIndex=E.lastIndex,t;var C=h.groups,T=S&&p.sticky,k=o(l,p),L=p.source,j=0,P=A;if(T&&(k=_(k,"y",""),-1===b(k,"g")&&(k+="g"),P=w(A,p.lastIndex),p.lastIndex>0&&(!p.multiline||p.multiline&&"\n"!==y(A,p.lastIndex-1))&&(L="(?: "+L+")",P=" "+P,j++),n=new RegExp("^(?:"+L+")",k)),O&&(n=new RegExp("^"+L+"$(?!\\s)",k)),x&&(r=p.lastIndex),i=o(m,T?n:p,P),T?i?(i.input=w(i.input,j),i[0]=w(i[0],j),i.index=p.lastIndex,p.lastIndex+=i[0].length):p.lastIndex=0:x&&i&&(p.lastIndex=p.global?i.index+i[0].length:r),O&&i&&i.length>1&&o(g,i[0],n,function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)}),i&&C)for(i.groups=c=d(null),s=0;s<C.length;s++)c[(u=C[s])[0]]=i[u[1]];return i}),e.exports=v},878:(e,t,n)=>{"use strict";var r=n(1417),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw new o("Can't set "+i(e)+" as a prototype")}},930:(e,t,n)=>{"use strict";var r=n(8810),i=n(3163),o=n(4461),s=n(7267),a=n(6445),l=n(92),c=a("string-to-symbol-registry"),u=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!l},{for:function(e){var t=s(e);if(o(c,t))return c[t];var n=i("Symbol")(t);return c[t]=n,u[n]=t,n}})},991:(e,t,n)=>{"use strict";var r=n(2675),i=n(8052).RegExp;e.exports=r(function(){var e=i(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)})},1031:(e,t,n)=>{"use strict";var r=n(4334),i=TypeError,o=function(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw new i("Bad Promise constructor");t=e,n=r}),this.resolve=r(t),this.reject=r(n)};e.exports.f=function(e){return new o(e)}},1048:(e,t,n)=>{"use strict";var r=n(8810),i=n(2484),o=n(7391),s=n(7156),a=n(1465),l=n(2675),c=RangeError,u=String,d=Math.floor,f=i(a),p=i("".slice),h=i(1.1.toFixed),g=function(e,t,n){return 0===t?n:t%2==1?g(e,t-1,n*e):g(e*e,t/2,n)},m=function(e,t,n){for(var r=-1,i=n;++r<6;)i+=t*e[r],e[r]=i%1e7,i=d(i/1e7)},v=function(e,t){for(var n=6,r=0;--n>=0;)r+=e[n],e[n]=d(r/t),r=r%t*1e7},y=function(e){for(var t=6,n="";--t>=0;)if(""!==n||0===t||0!==e[t]){var r=u(e[t]);n=""===n?r:n+f("0",7-r.length)+r}return n};r({target:"Number",proto:!0,forced:l(function(){return"0.000"!==h(8e-5,3)||"1"!==h(.9,0)||"1.25"!==h(1.255,2)||"1000000000000000128"!==h(0xde0b6b3a7640080,0)})||!l(function(){h({})})},{toFixed:function(e){var t,n,r,i,a=s(this),l=o(e),d=[0,0,0,0,0,0],h="",b="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(a!=a)return"NaN";if(a<=-1e21||a>=1e21)return u(a);if(a<0&&(h="-",a=-a),a>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(a*g(2,69,1))-69)<0?a*g(2,-t,1):a/g(2,t,1),n*=4503599627370496,(t=52-t)>0){for(m(d,0,n),r=l;r>=7;)m(d,1e7,0),r-=7;for(m(d,g(10,r,1),0),r=t-1;r>=23;)v(d,1<<23),r-=23;v(d,1<<r),m(d,1,1),v(d,2),b=y(d)}else m(d,0,n),m(d,1<<-t,0),b=y(d)+f("0",l);return b=l>0?h+((i=b.length)<=l?"0."+f("0",l-i)+b:p(b,0,i-l)+"."+p(b,i-l)):h+b}})},1072:(e,t,n)=>{"use strict";var r=n(9004),i=n(3625),o=n(659),s=n(8379),a=n(3709),l=n(8770),c=n(7837),u=n(6221),d=n(4951),f=n(1151),p=TypeError,h=function(e,t){this.stopped=e,this.result=t},g=h.prototype;e.exports=function(e,t,n){var m,v,y,b,_,w,x,S=n&&n.that,O=!(!n||!n.AS_ENTRIES),A=!(!n||!n.IS_RECORD),E=!(!n||!n.IS_ITERATOR),C=!(!n||!n.INTERRUPTED),T=r(t,S),k=function(e){return m&&f(m,"normal"),new h(!0,e)},L=function(e){return O?(o(e),C?T(e[0],e[1],k):T(e[0],e[1])):C?T(e,k):T(e)};if(A)m=e.iterator;else if(E)m=e;else{if(!(v=d(e)))throw new p(s(e)+" is not iterable");if(a(v)){for(y=0,b=l(e);b>y;y++)if((_=L(e[y]))&&c(g,_))return _;return new h(!1)}m=u(e,v)}for(w=A?e.next:m.next;!(x=i(w,m)).done;){try{_=L(x.value)}catch(e){f(m,"throw",e)}if("object"==typeof _&&_&&c(g,_))return _}return new h(!1)}},1151:(e,t,n)=>{"use strict";var r=n(3625),i=n(659),o=n(9538);e.exports=function(e,t,n){var s,a;i(e);try{if(!(s=o(e,"return"))){if("throw"===t)throw n;return n}s=r(s,e)}catch(e){a=!0,s=e}if("throw"===t)throw n;if(a)throw s;return i(s),n}},1261:(e,t,n)=>{"use strict";var r=n(573),i=n(3401);e.exports=function(e){var t=r(e,"string");return i(t)?t:t+""}},1417:(e,t,n)=>{"use strict";var r=n(4102);e.exports=function(e){return r(e)||null===e}},1441:(e,t,n)=>{"use strict";var r=n(2484),i=n(2675),o=n(321),s=n(5719),a=n(3163),l=n(2718),c=function(){},u=a("Reflect","construct"),d=/^\s*(?:class|function)\b/,f=r(d.exec),p=!d.test(c),h=function(e){if(!o(e))return!1;try{return u(c,[],e),!0}catch(e){return!1}},g=function(e){if(!o(e))return!1;switch(s(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!f(d,l(e))}catch(e){return!0}};g.sham=!0,e.exports=!u||i(function(){var e;return h(h.call)||!h(Object)||!h(function(){e=!0})||e})?g:h},1465:(e,t,n)=>{"use strict";var r=n(7391),i=n(7267),o=n(4834),s=RangeError;e.exports=function(e){var t=i(o(this)),n="",a=r(e);if(a<0||a===1/0)throw new s("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(n+=t);return n}},1650:(e,t,n)=>{"use strict";var r=n(2484),i=n(8649),o=Math.floor,s=r("".charAt),a=r("".replace),l=r("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,u=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,n,r,d,f){var p=n+e.length,h=r.length,g=u;return void 0!==d&&(d=i(d),g=c),a(f,g,function(i,a){var c;switch(s(a,0)){case"$":return"$";case"&":return e;case"`":return l(t,0,n);case"'":return l(t,p);case"<":c=d[l(a,1,-1)];break;default:var u=+a;if(0===u)return i;if(u>h){var f=o(u/10);return 0===f?i:f<=h?void 0===r[f-1]?s(a,1):r[f-1]+s(a,1):i}c=r[u-1]}return void 0===c?"":c})}},1652:(e,t,n)=>{"use strict";var r=n(659),i=n(5514),o=TypeError;e.exports=function(e){if(r(this),"string"===e||"default"===e)e="string";else if("number"!==e)throw new o("Incorrect hint");return i(this,e)}},1661:e=>{"use strict";var t=function(){this.head=null,this.tail=null};t.prototype={add:function(e){var t={item:e,next:null},n=this.tail;n?n.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}},e.exports=t},1704:(e,t,n)=>{"use strict";var r=n(4461),i=n(9467),o=n(3071),s=n(6005);e.exports=function(e,t,n){for(var a=i(t),l=s.f,c=o.f,u=0;u<a.length;u++){var d=a[u];r(e,d)||n&&r(n,d)||l(e,d,c(t,d))}}},1715:(e,t,n)=>{"use strict";var r=n(6057),i=n(4102),o=n(4834),s=n(878);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.prototype,"__proto__","set"))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return o(n),s(r),i(n)?(t?e(n,r):n.__proto__=r,n):n}}():void 0)},1759:(e,t,n)=>{"use strict";var r=n(7928),i=n(5236),o=n(6871);r||i(Object.prototype,"toString",o,{unsafe:!0})},1904:(e,t,n)=>{"use strict";var r=n(2748),i=n(2484);e.exports=function(e){if("Function"===r(e))return i(e)}},1929:e=>{"use strict";e.exports=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(e){}}},1941:(e,t,n)=>{"use strict";var r=n(8052),i=Object.defineProperty;e.exports=function(e,t){try{i(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},1948:(e,t,n)=>{"use strict";var r=n(2748);e.exports=Array.isArray||function(e){return"Array"===r(e)}},1955:(e,t,n)=>{"use strict";var r=n(2484),i=n(7391),o=n(7267),s=n(4834),a=r("".charAt),l=r("".charCodeAt),c=r("".slice),u=function(e){return function(t,n){var r,u,d=o(s(t)),f=i(n),p=d.length;return f<0||f>=p?e?"":void 0:(r=l(d,f))<55296||r>56319||f+1===p||(u=l(d,f+1))<56320||u>57343?e?a(d,f):r:e?c(d,f,f+2):u-56320+(r-55296<<10)+65536}};e.exports={codeAt:u(!1),charAt:u(!0)}},2029:(e,t,n)=>{"use strict";var r=n(8810),i=n(8052);r({global:!0,forced:i.globalThis!==i},{globalThis:i})},2035:(e,t,n)=>{"use strict";var r=n(8810),i=n(3625),o=n(4334),s=n(1031),a=n(3443),l=n(1072);r({target:"Promise",stat:!0,forced:n(341)},{race:function(e){var t=this,n=s.f(t),r=n.reject,c=a(function(){var s=o(t.resolve);l(e,function(e){i(s,t,e).then(n.resolve,r)})});return c.error&&r(c.value),n.promise}})},2128:(e,t,n)=>{"use strict";var r=n(2675);e.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},2148:(e,t,n)=>{"use strict";var r=n(8810),i=n(3163),o=n(6007),s=n(5034),a=n(7032).CONSTRUCTOR,l=n(6794),c=i("Promise"),u=o&&!a;r({target:"Promise",stat:!0,forced:o||a},{resolve:function(e){return l(u&&this===c?s:this,e)}})},2153:(e,t,n)=>{"use strict";var r=n(4175),i=n(3844),o=n(6005).f,s=r("unscopables"),a=Array.prototype;void 0===a[s]&&o(a,s,{configurable:!0,value:i(null)}),e.exports=function(e){a[s][e]=!0}},2173:(e,t,n)=>{"use strict";var r=n(8810),i=n(1031);r({target:"Promise",stat:!0,forced:n(7032).CONSTRUCTOR},{reject:function(e){var t=i.f(this);return(0,t.reject)(e),t.promise}})},2202:(e,t,n)=>{"use strict";n(7560);var r,i=n(8810),o=n(2128),s=n(5940),a=n(8052),l=n(9004),c=n(2484),u=n(5236),d=n(6038),f=n(5755),p=n(4461),h=n(5433),g=n(600),m=n(4540),v=n(1955).codeAt,y=n(121),b=n(7267),_=n(8819),w=n(464),x=n(2690),S=n(6369),O=S.set,A=S.getterFor("URL"),E=x.URLSearchParams,C=x.getState,T=a.URL,k=a.TypeError,L=a.parseInt,j=Math.floor,P=Math.pow,I=c("".charAt),D=c(/./.exec),N=c([].join),F=c(1.1.toString),R=c([].pop),M=c([].push),$=c("".replace),q=c([].shift),H=c("".split),B=c("".slice),U=c("".toLowerCase),W=c([].unshift),z="Invalid scheme",V="Invalid host",G="Invalid port",K=/[a-z]/i,X=/[\d+-.a-z]/i,Q=/\d/,Y=/^0x/i,J=/^[0-7]+$/,Z=/^\d+$/,ee=/^[\da-f]+$/i,te=/[\0\t\n\r #%/:<>?@[\\\]^|]/,ne=/[\0\t\n\r #/:<>?@[\\\]^|]/,re=/^[\u0000-\u0020]+/,ie=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,oe=/[\t\n\r]/g,se=function(e){var t,n,r,i;if("number"==typeof e){for(t=[],n=0;n<4;n++)W(t,e%256),e=j(e/256);return N(t,".")}if("object"==typeof e){for(t="",r=function(e){for(var t=null,n=1,r=null,i=0,o=0;o<8;o++)0!==e[o]?(i>n&&(t=r,n=i),r=null,i=0):(null===r&&(r=o),++i);return i>n?r:t}(e),n=0;n<8;n++)i&&0===e[n]||(i&&(i=!1),r===n?(t+=n?":":"::",i=!0):(t+=F(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},ae={},le=h({},ae,{" ":1,'"':1,"<":1,">":1,"`":1}),ce=h({},le,{"#":1,"?":1,"{":1,"}":1}),ue=h({},ce,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),de=function(e,t){var n=v(e,0);return n>32&&n<127&&!p(t,e)?e:encodeURIComponent(e)},fe={ftp:21,file:null,http:80,https:443,ws:80,wss:443},pe=function(e,t){var n;return 2===e.length&&D(K,I(e,0))&&(":"===(n=I(e,1))||!t&&"|"===n)},he=function(e){var t;return e.length>1&&pe(B(e,0,2))&&(2===e.length||"/"===(t=I(e,2))||"\\"===t||"?"===t||"#"===t)},ge=function(e){return"."===e||"%2e"===U(e)},me=function(e){return".."===(e=U(e))||"%2e."===e||".%2e"===e||"%2e%2e"===e},ve={},ye={},be={},_e={},we={},xe={},Se={},Oe={},Ae={},Ee={},Ce={},Te={},ke={},Le={},je={},Pe={},Ie={},De={},Ne={},Fe={},Re={},Me=function(e,t,n){var r,i,o,s=b(e);if(t){if(i=this.parse(s))throw new k(i);this.searchParams=null}else{if(void 0!==n&&(r=new Me(n,!0)),i=this.parse(s,null,r))throw new k(i);(o=C(new E)).bindURL(this),this.searchParams=o}};Me.prototype={type:"URL",parse:function(e,t,n){var i,o,s,a,l=this,c=t||ve,u=0,d="",f=!1,h=!1,v=!1;for(e=b(e),t||(l.scheme="",l.username="",l.password="",l.host=null,l.port=null,l.path=[],l.query=null,l.fragment=null,l.cannotBeABaseURL=!1,e=$(e,re,""),e=$(e,ie,"$1")),e=$(e,oe,""),i=g(e);u<=i.length;){switch(o=i[u],c){case ve:if(!o||!D(K,o)){if(t)return z;c=be;continue}d+=U(o),c=ye;break;case ye:if(o&&(D(X,o)||"+"===o||"-"===o||"."===o))d+=U(o);else{if(":"!==o){if(t)return z;d="",c=be,u=0;continue}if(t&&(l.isSpecial()!==p(fe,d)||"file"===d&&(l.includesCredentials()||null!==l.port)||"file"===l.scheme&&!l.host))return;if(l.scheme=d,t)return void(l.isSpecial()&&fe[l.scheme]===l.port&&(l.port=null));d="","file"===l.scheme?c=Le:l.isSpecial()&&n&&n.scheme===l.scheme?c=_e:l.isSpecial()?c=Oe:"/"===i[u+1]?(c=we,u++):(l.cannotBeABaseURL=!0,M(l.path,""),c=Ne)}break;case be:if(!n||n.cannotBeABaseURL&&"#"!==o)return z;if(n.cannotBeABaseURL&&"#"===o){l.scheme=n.scheme,l.path=m(n.path),l.query=n.query,l.fragment="",l.cannotBeABaseURL=!0,c=Re;break}c="file"===n.scheme?Le:xe;continue;case _e:if("/"!==o||"/"!==i[u+1]){c=xe;continue}c=Ae,u++;break;case we:if("/"===o){c=Ee;break}c=De;continue;case xe:if(l.scheme=n.scheme,o===r)l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=m(n.path),l.query=n.query;else if("/"===o||"\\"===o&&l.isSpecial())c=Se;else if("?"===o)l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=m(n.path),l.query="",c=Fe;else{if("#"!==o){l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=m(n.path),l.path.length--,c=De;continue}l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,l.path=m(n.path),l.query=n.query,l.fragment="",c=Re}break;case Se:if(!l.isSpecial()||"/"!==o&&"\\"!==o){if("/"!==o){l.username=n.username,l.password=n.password,l.host=n.host,l.port=n.port,c=De;continue}c=Ee}else c=Ae;break;case Oe:if(c=Ae,"/"!==o||"/"!==I(d,u+1))continue;u++;break;case Ae:if("/"!==o&&"\\"!==o){c=Ee;continue}break;case Ee:if("@"===o){f&&(d="%40"+d),f=!0,s=g(d);for(var y=0;y<s.length;y++){var _=s[y];if(":"!==_||v){var w=de(_,ue);v?l.password+=w:l.username+=w}else v=!0}d=""}else if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&l.isSpecial()){if(f&&""===d)return"Invalid authority";u-=g(d).length+1,d="",c=Ce}else d+=o;break;case Ce:case Te:if(t&&"file"===l.scheme){c=Pe;continue}if(":"!==o||h){if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&l.isSpecial()){if(l.isSpecial()&&""===d)return V;if(t&&""===d&&(l.includesCredentials()||null!==l.port))return;if(a=l.parseHost(d))return a;if(d="",c=Ie,t)return;continue}"["===o?h=!0:"]"===o&&(h=!1),d+=o}else{if(""===d)return V;if(a=l.parseHost(d))return a;if(d="",c=ke,t===Te)return}break;case ke:if(!D(Q,o)){if(o===r||"/"===o||"?"===o||"#"===o||"\\"===o&&l.isSpecial()||t){if(""!==d){var x=L(d,10);if(x>65535)return G;l.port=l.isSpecial()&&x===fe[l.scheme]?null:x,d=""}if(t)return;c=Ie;continue}return G}d+=o;break;case Le:if(l.scheme="file","/"===o||"\\"===o)c=je;else{if(!n||"file"!==n.scheme){c=De;continue}switch(o){case r:l.host=n.host,l.path=m(n.path),l.query=n.query;break;case"?":l.host=n.host,l.path=m(n.path),l.query="",c=Fe;break;case"#":l.host=n.host,l.path=m(n.path),l.query=n.query,l.fragment="",c=Re;break;default:he(N(m(i,u),""))||(l.host=n.host,l.path=m(n.path),l.shortenPath()),c=De;continue}}break;case je:if("/"===o||"\\"===o){c=Pe;break}n&&"file"===n.scheme&&!he(N(m(i,u),""))&&(pe(n.path[0],!0)?M(l.path,n.path[0]):l.host=n.host),c=De;continue;case Pe:if(o===r||"/"===o||"\\"===o||"?"===o||"#"===o){if(!t&&pe(d))c=De;else if(""===d){if(l.host="",t)return;c=Ie}else{if(a=l.parseHost(d))return a;if("localhost"===l.host&&(l.host=""),t)return;d="",c=Ie}continue}d+=o;break;case Ie:if(l.isSpecial()){if(c=De,"/"!==o&&"\\"!==o)continue}else if(t||"?"!==o)if(t||"#"!==o){if(o!==r&&(c=De,"/"!==o))continue}else l.fragment="",c=Re;else l.query="",c=Fe;break;case De:if(o===r||"/"===o||"\\"===o&&l.isSpecial()||!t&&("?"===o||"#"===o)){if(me(d)?(l.shortenPath(),"/"===o||"\\"===o&&l.isSpecial()||M(l.path,"")):ge(d)?"/"===o||"\\"===o&&l.isSpecial()||M(l.path,""):("file"===l.scheme&&!l.path.length&&pe(d)&&(l.host&&(l.host=""),d=I(d,0)+":"),M(l.path,d)),d="","file"===l.scheme&&(o===r||"?"===o||"#"===o))for(;l.path.length>1&&""===l.path[0];)q(l.path);"?"===o?(l.query="",c=Fe):"#"===o&&(l.fragment="",c=Re)}else d+=de(o,ce);break;case Ne:"?"===o?(l.query="",c=Fe):"#"===o?(l.fragment="",c=Re):o!==r&&(l.path[0]+=de(o,ae));break;case Fe:t||"#"!==o?o!==r&&("'"===o&&l.isSpecial()?l.query+="%27":l.query+="#"===o?"%23":de(o,ae)):(l.fragment="",c=Re);break;case Re:o!==r&&(l.fragment+=de(o,le))}u++}},parseHost:function(e){var t,n,r;if("["===I(e,0)){if("]"!==I(e,e.length-1))return V;if(t=function(e){var t,n,r,i,o,s,a,l=[0,0,0,0,0,0,0,0],c=0,u=null,d=0,f=function(){return I(e,d)};if(":"===f()){if(":"!==I(e,1))return;d+=2,u=++c}for(;f();){if(8===c)return;if(":"!==f()){for(t=n=0;n<4&&D(ee,f());)t=16*t+L(f(),16),d++,n++;if("."===f()){if(0===n)return;if(d-=n,c>6)return;for(r=0;f();){if(i=null,r>0){if(!("."===f()&&r<4))return;d++}if(!D(Q,f()))return;for(;D(Q,f());){if(o=L(f(),10),null===i)i=o;else{if(0===i)return;i=10*i+o}if(i>255)return;d++}l[c]=256*l[c]+i,2!==++r&&4!==r||c++}if(4!==r)return;break}if(":"===f()){if(d++,!f())return}else if(f())return;l[c++]=t}else{if(null!==u)return;d++,u=++c}}if(null!==u)for(s=c-u,c=7;0!==c&&s>0;)a=l[c],l[c--]=l[u+s-1],l[u+--s]=a;else if(8!==c)return;return l}(B(e,1,-1)),!t)return V;this.host=t}else if(this.isSpecial()){if(e=y(e),D(te,e))return V;if(t=function(e){var t,n,r,i,o,s,a,l=H(e,".");if(l.length&&""===l[l.length-1]&&l.length--,(t=l.length)>4)return e;for(n=[],r=0;r<t;r++){if(""===(i=l[r]))return e;if(o=10,i.length>1&&"0"===I(i,0)&&(o=D(Y,i)?16:8,i=B(i,8===o?1:2)),""===i)s=0;else{if(!D(10===o?Z:8===o?J:ee,i))return e;s=L(i,o)}M(n,s)}for(r=0;r<t;r++)if(s=n[r],r===t-1){if(s>=P(256,5-t))return null}else if(s>255)return null;for(a=R(n),r=0;r<n.length;r++)a+=n[r]*P(256,3-r);return a}(e),null===t)return V;this.host=t}else{if(D(ne,e))return V;for(t="",n=g(e),r=0;r<n.length;r++)t+=de(n[r],ae);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return p(fe,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"===this.scheme&&1===t&&pe(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,i=e.host,o=e.port,s=e.path,a=e.query,l=e.fragment,c=t+":";return null!==i?(c+="//",e.includesCredentials()&&(c+=n+(r?":"+r:"")+"@"),c+=se(i),null!==o&&(c+=":"+o)):"file"===t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+N(s,"/"):"",null!==a&&(c+="?"+a),null!==l&&(c+="#"+l),c},setHref:function(e){var t=this.parse(e);if(t)throw new k(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"===e)try{return new $e(e.path[0]).origin}catch(e){return"null"}return"file"!==e&&this.isSpecial()?e+"://"+se(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(b(e)+":",ve)},getUsername:function(){return this.username},setUsername:function(e){var t=g(b(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=de(t[n],ue)}},getPassword:function(){return this.password},setPassword:function(e){var t=g(b(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=de(t[n],ue)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?se(e):se(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Ce)},getHostname:function(){var e=this.host;return null===e?"":se(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,Te)},getPort:function(){var e=this.port;return null===e?"":b(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""===(e=b(e))?this.port=null:this.parse(e,ke))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+N(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,Ie))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""===(e=b(e))?this.query=null:("?"===I(e,0)&&(e=B(e,1)),this.query="",this.parse(e,Fe)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!==(e=b(e))?("#"===I(e,0)&&(e=B(e,1)),this.fragment="",this.parse(e,Re)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var $e=function(e){var t=f(this,qe),n=w(arguments.length,1)>1?arguments[1]:void 0,r=O(t,new Me(e,!1,n));o||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},qe=$e.prototype,He=function(e,t){return{get:function(){return A(this)[e]()},set:t&&function(e){return A(this)[t](e)},configurable:!0,enumerable:!0}};if(o&&(d(qe,"href",He("serialize","setHref")),d(qe,"origin",He("getOrigin")),d(qe,"protocol",He("getProtocol","setProtocol")),d(qe,"username",He("getUsername","setUsername")),d(qe,"password",He("getPassword","setPassword")),d(qe,"host",He("getHost","setHost")),d(qe,"hostname",He("getHostname","setHostname")),d(qe,"port",He("getPort","setPort")),d(qe,"pathname",He("getPathname","setPathname")),d(qe,"search",He("getSearch","setSearch")),d(qe,"searchParams",He("getSearchParams")),d(qe,"hash",He("getHash","setHash"))),u(qe,"toJSON",function(){return A(this).serialize()},{enumerable:!0}),u(qe,"toString",function(){return A(this).serialize()},{enumerable:!0}),T){var Be=T.createObjectURL,Ue=T.revokeObjectURL;Be&&u($e,"createObjectURL",l(Be,T)),Ue&&u($e,"revokeObjectURL",l(Ue,T))}_($e,"URL"),i({global:!0,constructor:!0,forced:!s,sham:!o},{URL:$e})},2321:(e,t,n)=>{"use strict";var r=n(2675),i=n(4175),o=n(2763),s=i("species");e.exports=function(e){return o>=51||!r(function(){var t=[];return(t.constructor={})[s]=function(){return{foo:1}},1!==t[e](Boolean).foo})}},2360:(e,t,n)=>{"use strict";var r=n(2675),i=n(321),o=/#|\.prototype\./,s=function(e,t){var n=l[a(e)];return n===u||n!==c&&(i(t)?r(t):!!t)},a=s.normalize=function(e){return String(e).replace(o,".").toLowerCase()},l=s.data={},c=s.NATIVE="N",u=s.POLYFILL="P";e.exports=s},2407:(e,t,n)=>{"use strict";var r=n(2675);e.exports=!r(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2484:(e,t,n)=>{"use strict";var r=n(3588),i=Function.prototype,o=i.call,s=r&&i.bind.bind(o,o);e.exports=r?s:function(e){return function(){return o.apply(e,arguments)}}},2537:(e,t,n)=>{"use strict";var r=n(2675),i=n(8052).RegExp,o=r(function(){var e=i("a","y");return e.lastIndex=2,null!==e.exec("abcd")}),s=o||r(function(){return!i("a","y").sticky}),a=o||r(function(){var e=i("^r","gy");return e.lastIndex=2,null!==e.exec("str")});e.exports={BROKEN_CARET:a,MISSED_STICKY:s,UNSUPPORTED_Y:o}},2558:(e,t,n)=>{"use strict";var r=n(2128),i=n(4690).EXISTS,o=n(2484),s=n(6038),a=Function.prototype,l=o(a.toString),c=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,u=o(c.exec);r&&!i&&s(a,"name",{configurable:!0,get:function(){try{return u(c,l(this))[1]}catch(e){return""}}})},2614:(e,t,n)=>{"use strict";var r=n(8810),i=n(600);r({target:"Array",stat:!0,forced:!n(9976)(function(e){Array.from(e)})},{from:i})},2675:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},2690:(e,t,n)=>{"use strict";n(3348),n(861);var r=n(8810),i=n(8052),o=n(6977),s=n(3163),a=n(3625),l=n(2484),c=n(2128),u=n(5940),d=n(5236),f=n(6038),p=n(3179),h=n(8819),g=n(7270),m=n(6369),v=n(5755),y=n(321),b=n(4461),_=n(9004),w=n(5719),x=n(659),S=n(4102),O=n(7267),A=n(3844),E=n(5936),C=n(6221),T=n(4951),k=n(381),L=n(464),j=n(4175),P=n(4492),I=j("iterator"),D="URLSearchParams",N=D+"Iterator",F=m.set,R=m.getterFor(D),M=m.getterFor(N),$=o("fetch"),q=o("Request"),H=o("Headers"),B=q&&q.prototype,U=H&&H.prototype,W=i.TypeError,z=i.encodeURIComponent,V=String.fromCharCode,G=s("String","fromCodePoint"),K=parseInt,X=l("".charAt),Q=l([].join),Y=l([].push),J=l("".replace),Z=l([].shift),ee=l([].splice),te=l("".split),ne=l("".slice),re=l(/./.exec),ie=/\+/g,oe=/^[0-9a-f]+$/i,se=function(e,t){var n=ne(e,t,t+2);return re(oe,n)?K(n,16):NaN},ae=function(e){for(var t=0,n=128;n>0&&0!==(e&n);n>>=1)t++;return t},le=function(e){var t=null;switch(e.length){case 1:t=e[0];break;case 2:t=(31&e[0])<<6|63&e[1];break;case 3:t=(15&e[0])<<12|(63&e[1])<<6|63&e[2];break;case 4:t=(7&e[0])<<18|(63&e[1])<<12|(63&e[2])<<6|63&e[3]}return t>1114111?null:t},ce=function(e){for(var t=(e=J(e,ie," ")).length,n="",r=0;r<t;){var i=X(e,r);if("%"===i){if("%"===X(e,r+1)||r+3>t){n+="%",r++;continue}var o=se(e,r+1);if(o!=o){n+=i,r++;continue}r+=2;var s=ae(o);if(0===s)i=V(o);else{if(1===s||s>4){n+="�",r++;continue}for(var a=[o],l=1;l<s&&!(++r+3>t||"%"!==X(e,r));){var c=se(e,r+1);if(c!=c){r+=3;break}if(c>191||c<128)break;Y(a,c),r+=2,l++}if(a.length!==s){n+="�";continue}var u=le(a);null===u?n+="�":i=G(u)}}n+=i,r++}return n},ue=/[!'()~]|%20/g,de={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},fe=function(e){return de[e]},pe=function(e){return J(z(e),ue,fe)},he=g(function(e,t){F(this,{type:N,target:R(e).entries,index:0,kind:t})},D,function(){var e=M(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,k(void 0,!0);var r=t[n];switch(e.kind){case"keys":return k(r.key,!1);case"values":return k(r.value,!1)}return k([r.key,r.value],!1)},!0),ge=function(e){this.entries=[],this.url=null,void 0!==e&&(S(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===X(e,0)?ne(e,1):e:O(e)))};ge.prototype={type:D,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,i,o,s,l,c=this.entries,u=T(e);if(u)for(n=(t=C(e,u)).next;!(r=a(n,t)).done;){if(o=(i=C(x(r.value))).next,(s=a(o,i)).done||(l=a(o,i)).done||!a(o,i).done)throw new W("Expected sequence with length 2");Y(c,{key:O(s.value),value:O(l.value)})}else for(var d in e)b(e,d)&&Y(c,{key:d,value:O(e[d])})},parseQuery:function(e){if(e)for(var t,n,r=this.entries,i=te(e,"&"),o=0;o<i.length;)(t=i[o++]).length&&(n=te(t,"="),Y(r,{key:ce(Z(n)),value:ce(Q(n,"="))}))},serialize:function(){for(var e,t=this.entries,n=[],r=0;r<t.length;)e=t[r++],Y(n,pe(e.key)+"="+pe(e.value));return Q(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var me=function(){v(this,ve);var e=F(this,new ge(arguments.length>0?arguments[0]:void 0));c||(this.size=e.entries.length)},ve=me.prototype;if(p(ve,{append:function(e,t){var n=R(this);L(arguments.length,2),Y(n.entries,{key:O(e),value:O(t)}),c||this.length++,n.updateURL()},delete:function(e){for(var t=R(this),n=L(arguments.length,1),r=t.entries,i=O(e),o=n<2?void 0:arguments[1],s=void 0===o?o:O(o),a=0;a<r.length;){var l=r[a];if(l.key!==i||void 0!==s&&l.value!==s)a++;else if(ee(r,a,1),void 0!==s)break}c||(this.size=r.length),t.updateURL()},get:function(e){var t=R(this).entries;L(arguments.length,1);for(var n=O(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){var t=R(this).entries;L(arguments.length,1);for(var n=O(e),r=[],i=0;i<t.length;i++)t[i].key===n&&Y(r,t[i].value);return r},has:function(e){for(var t=R(this).entries,n=L(arguments.length,1),r=O(e),i=n<2?void 0:arguments[1],o=void 0===i?i:O(i),s=0;s<t.length;){var a=t[s++];if(a.key===r&&(void 0===o||a.value===o))return!0}return!1},set:function(e,t){var n=R(this);L(arguments.length,1);for(var r,i=n.entries,o=!1,s=O(e),a=O(t),l=0;l<i.length;l++)(r=i[l]).key===s&&(o?ee(i,l--,1):(o=!0,r.value=a));o||Y(i,{key:s,value:a}),c||(this.size=i.length),n.updateURL()},sort:function(){var e=R(this);P(e.entries,function(e,t){return e.key>t.key?1:-1}),e.updateURL()},forEach:function(e){for(var t,n=R(this).entries,r=_(e,arguments.length>1?arguments[1]:void 0),i=0;i<n.length;)r((t=n[i++]).value,t.key,this)},keys:function(){return new he(this,"keys")},values:function(){return new he(this,"values")},entries:function(){return new he(this,"entries")}},{enumerable:!0}),d(ve,I,ve.entries,{name:"entries"}),d(ve,"toString",function(){return R(this).serialize()},{enumerable:!0}),c&&f(ve,"size",{get:function(){return R(this).entries.length},configurable:!0,enumerable:!0}),h(me,D),r({global:!0,constructor:!0,forced:!u},{URLSearchParams:me}),!u&&y(H)){var ye=l(U.has),be=l(U.set),_e=function(e){if(S(e)){var t,n=e.body;if(w(n)===D)return t=e.headers?new H(e.headers):new H,ye(t,"content-type")||be(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),A(e,{body:E(0,O(n)),headers:E(0,t)})}return e};if(y($)&&r({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return $(e,arguments.length>1?_e(arguments[1]):{})}}),y(q)){var we=function(e){return v(this,B),new q(e,arguments.length>1?_e(arguments[1]):{})};B.constructor=we,we.prototype=B,r({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:we})}}e.exports={URLSearchParams:me,getState:R}},2718:(e,t,n)=>{"use strict";var r=n(2484),i=n(321),o=n(2921),s=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return s(e)}),e.exports=o.inspectSource},2748:(e,t,n)=>{"use strict";var r=n(2484),i=r({}.toString),o=r("".slice);e.exports=function(e){return o(i(e),8,-1)}},2763:(e,t,n)=>{"use strict";var r,i,o=n(8052),s=n(3291),a=o.process,l=o.Deno,c=a&&a.versions||l&&l.version,u=c&&c.v8;u&&(i=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&s&&(!(r=s.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/))&&(i=+r[1]),e.exports=i},2868:(e,t,n)=>{"use strict";var r=n(2484),i=0,o=Math.random(),s=r(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++i+o,36)}},2900:(e,t,n)=>{"use strict";var r=n(3291);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},2901:(e,t,n)=>{"use strict";var r=n(8052),i=n(5004),o=n(7140),s=n(3348),a=n(671),l=n(8819),c=n(4175)("iterator"),u=s.values,d=function(e,t){if(e){if(e[c]!==u)try{a(e,c,u)}catch(t){e[c]=u}if(l(e,t,!0),i[t])for(var n in s)if(e[n]!==s[n])try{a(e,n,s[n])}catch(t){e[n]=s[n]}}};for(var f in i)d(r[f]&&r[f].prototype,f);d(o,"DOMTokenList")},2921:(e,t,n)=>{"use strict";var r=n(6007),i=n(8052),o=n(1941),s="__core-js_shared__",a=e.exports=i[s]||o(s,{});(a.versions||(a.versions=[])).push({version:"3.43.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.43.0/LICENSE",source:"https://github.com/zloirock/core-js"})},2961:(e,t,n)=>{"use strict";var r=n(9004),i=n(2484),o=n(51),s=n(8649),a=n(8770),l=n(4929),c=i([].push),u=function(e){var t=1===e,n=2===e,i=3===e,u=4===e,d=6===e,f=7===e,p=5===e||d;return function(h,g,m,v){for(var y,b,_=s(h),w=o(_),x=a(w),S=r(g,m),O=0,A=v||l,E=t?A(h,x):n||f?A(h,0):void 0;x>O;O++)if((p||O in w)&&(b=S(y=w[O],O,_),e))if(t)E[O]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return O;case 2:c(E,y)}else switch(e){case 4:return!1;case 7:c(E,y)}return d?-1:i||u?u:E}};e.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},3026:(e,t,n)=>{"use strict";var r=n(7391),i=Math.min;e.exports=function(e){var t=r(e);return t>0?i(t,9007199254740991):0}},3037:(e,t,n)=>{"use strict";var r=n(2128),i=n(706),o=n(6005),s=n(659),a=n(8969),l=n(4700);t.f=r&&!i?Object.defineProperties:function(e,t){s(e);for(var n,r=a(t),i=l(t),c=i.length,u=0;c>u;)o.f(e,n=i[u++],r[n]);return e}},3049:(e,t,n)=>{"use strict";var r=n(4690).PROPER,i=n(5236),o=n(659),s=n(7267),a=n(2675),l=n(3614),c="toString",u=RegExp.prototype,d=u[c],f=a(function(){return"/a/b"!==d.call({source:"a",flags:"b"})}),p=r&&d.name!==c;(f||p)&&i(u,c,function(){var e=o(this);return"/"+s(e.source)+"/"+s(l(e))},{unsafe:!0})},3071:(e,t,n)=>{"use strict";var r=n(2128),i=n(3625),o=n(7769),s=n(5936),a=n(8969),l=n(1261),c=n(4461),u=n(4113),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=a(e),t=l(t),u)try{return d(e,t)}catch(e){}if(c(e,t))return s(!i(o.f,e,t),e[t])}},3081:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},3082:(e,t,n)=>{"use strict";var r=n(8810),i=n(1948),o=n(1441),s=n(4102),a=n(6526),l=n(8770),c=n(8969),u=n(4028),d=n(4175),f=n(2321),p=n(4540),h=f("slice"),g=d("species"),m=Array,v=Math.max;r({target:"Array",proto:!0,forced:!h},{slice:function(e,t){var n,r,d,f=c(this),h=l(f),y=a(e,h),b=a(void 0===t?h:t,h);if(i(f)&&(n=f.constructor,(o(n)&&(n===m||i(n.prototype))||s(n)&&null===(n=n[g]))&&(n=void 0),n===m||void 0===n))return p(f,y,b);for(r=new(void 0===n?m:n)(v(b-y,0)),d=0;y<b;y++,d++)y in f&&u(r,d,f[y]);return r.length=d,r}})},3112:(e,t,n)=>{"use strict";var r=n(1441),i=n(8379),o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not a constructor")}},3163:(e,t,n)=>{"use strict";var r=n(8052),i=n(321);e.exports=function(e,t){return arguments.length<2?(n=r[e],i(n)?n:void 0):r[e]&&r[e][t];var n}},3179:(e,t,n)=>{"use strict";var r=n(5236);e.exports=function(e,t,n){for(var i in t)r(e,i,t[i],n);return e}},3291:(e,t,n)=>{"use strict";var r=n(8052).navigator,i=r&&r.userAgent;e.exports=i?String(i):""},3316:(e,t,n)=>{"use strict";var r=n(6891);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3348:(e,t,n)=>{"use strict";var r=n(8969),i=n(2153),o=n(6609),s=n(6369),a=n(6005).f,l=n(8676),c=n(381),u=n(6007),d=n(2128),f="Array Iterator",p=s.set,h=s.getterFor(f);e.exports=l(Array,"Array",function(e,t){p(this,{type:f,target:r(e),index:0,kind:t})},function(){var e=h(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,c(void 0,!0);switch(e.kind){case"keys":return c(n,!1);case"values":return c(t[n],!1)}return c([n,t[n]],!1)},"values");var g=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!u&&d&&"values"!==g.name)try{a(g,"name",{value:"values"})}catch(e){}},3401:(e,t,n)=>{"use strict";var r=n(3163),i=n(321),o=n(7837),s=n(3316),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return i(t)&&o(t.prototype,a(e))}},3443:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},3544:(e,t,n)=>{"use strict";var r=n(8052),i=n(5004),o=n(7140),s=n(9311),a=n(671),l=function(e){if(e&&e.forEach!==s)try{a(e,"forEach",s)}catch(t){e.forEach=s}};for(var c in i)i[c]&&l(r[c]&&r[c].prototype);l(o)},3588:(e,t,n)=>{"use strict";var r=n(2675);e.exports=!r(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},3597:(e,t,n)=>{"use strict";var r,i,o,s=n(2675),a=n(321),l=n(4102),c=n(3844),u=n(5927),d=n(5236),f=n(4175),p=n(6007),h=f("iterator"),g=!1;[].keys&&("next"in(o=[].keys())?(i=u(u(o)))!==Object.prototype&&(r=i):g=!0),!l(r)||s(function(){var e={};return r[h].call(e)!==e})?r={}:p&&(r=c(r)),a(r[h])||d(r,h,function(){return this}),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:g}},3614:(e,t,n)=>{"use strict";var r=n(3625),i=n(4461),o=n(7837),s=n(4993),a=n(8303),l=RegExp.prototype;e.exports=s.correct?function(e){return e.flags}:function(e){return s.correct||!o(l,e)||i(e,"flags")?e.flags:r(a,e)}},3625:(e,t,n)=>{"use strict";var r=n(3588),i=Function.prototype.call;e.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},3709:(e,t,n)=>{"use strict";var r=n(4175),i=n(6609),o=r("iterator"),s=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||s[o]===e)}},3765:(e,t,n)=>{"use strict";var r=n(8810),i=n(8052),o=n(3625),s=n(2484),a=n(6007),l=n(2128),c=n(6891),u=n(2675),d=n(4461),f=n(7837),p=n(659),h=n(8969),g=n(1261),m=n(7267),v=n(5936),y=n(3844),b=n(4700),_=n(4956),w=n(6766),x=n(9073),S=n(3071),O=n(6005),A=n(3037),E=n(7769),C=n(5236),T=n(6038),k=n(6445),L=n(3779),j=n(6617),P=n(2868),I=n(4175),D=n(5672),N=n(4515),F=n(8438),R=n(8819),M=n(6369),$=n(2961).forEach,q=L("hidden"),H="Symbol",B="prototype",U=M.set,W=M.getterFor(H),z=Object[B],V=i.Symbol,G=V&&V[B],K=i.RangeError,X=i.TypeError,Q=i.QObject,Y=S.f,J=O.f,Z=w.f,ee=E.f,te=s([].push),ne=k("symbols"),re=k("op-symbols"),ie=k("wks"),oe=!Q||!Q[B]||!Q[B].findChild,se=function(e,t,n){var r=Y(z,t);r&&delete z[t],J(e,t,n),r&&e!==z&&J(z,t,r)},ae=l&&u(function(){return 7!==y(J({},"a",{get:function(){return J(this,"a",{value:7}).a}})).a})?se:J,le=function(e,t){var n=ne[e]=y(G);return U(n,{type:H,tag:e,description:t}),l||(n.description=t),n},ce=function(e,t,n){e===z&&ce(re,t,n),p(e);var r=g(t);return p(n),d(ne,r)?(n.enumerable?(d(e,q)&&e[q][r]&&(e[q][r]=!1),n=y(n,{enumerable:v(0,!1)})):(d(e,q)||J(e,q,v(1,y(null))),e[q][r]=!0),ae(e,r,n)):J(e,r,n)},ue=function(e,t){p(e);var n=h(t),r=b(n).concat(he(n));return $(r,function(t){l&&!o(de,n,t)||ce(e,t,n[t])}),e},de=function(e){var t=g(e),n=o(ee,this,t);return!(this===z&&d(ne,t)&&!d(re,t))&&(!(n||!d(this,t)||!d(ne,t)||d(this,q)&&this[q][t])||n)},fe=function(e,t){var n=h(e),r=g(t);if(n!==z||!d(ne,r)||d(re,r)){var i=Y(n,r);return!i||!d(ne,r)||d(n,q)&&n[q][r]||(i.enumerable=!0),i}},pe=function(e){var t=Z(h(e)),n=[];return $(t,function(e){d(ne,e)||d(j,e)||te(n,e)}),n},he=function(e){var t=e===z,n=Z(t?re:h(e)),r=[];return $(n,function(e){!d(ne,e)||t&&!d(z,e)||te(r,ne[e])}),r};c||(C(G=(V=function(){if(f(G,this))throw new X("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?m(arguments[0]):void 0,t=P(e),n=function(e){var r=void 0===this?i:this;r===z&&o(n,re,e),d(r,q)&&d(r[q],t)&&(r[q][t]=!1);var s=v(1,e);try{ae(r,t,s)}catch(e){if(!(e instanceof K))throw e;se(r,t,s)}};return l&&oe&&ae(z,t,{configurable:!0,set:n}),le(t,e)})[B],"toString",function(){return W(this).tag}),C(V,"withoutSetter",function(e){return le(P(e),e)}),E.f=de,O.f=ce,A.f=ue,S.f=fe,_.f=w.f=pe,x.f=he,D.f=function(e){return le(I(e),e)},l&&(T(G,"description",{configurable:!0,get:function(){return W(this).description}}),a||C(z,"propertyIsEnumerable",de,{unsafe:!0}))),r({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:V}),$(b(ie),function(e){N(e)}),r({target:H,stat:!0,forced:!c},{useSetter:function(){oe=!0},useSimple:function(){oe=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!l},{create:function(e,t){return void 0===t?y(e):ue(y(e),t)},defineProperty:ce,defineProperties:ue,getOwnPropertyDescriptor:fe}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:pe}),F(),R(V,H),j[q]=!0},3779:(e,t,n)=>{"use strict";var r=n(6445),i=n(2868),o=r("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},3844:(e,t,n)=>{"use strict";var r,i=n(659),o=n(3037),s=n(8563),a=n(6617),l=n(7073),c=n(4451),u=n(3779),d="prototype",f="script",p=u("IE_PROTO"),h=function(){},g=function(e){return"<"+f+">"+e+"</"+f+">"},m=function(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t},v=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;v="undefined"!=typeof document?document.domain&&r?m(r):(t=c("iframe"),n="java"+f+":",t.style.display="none",l.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(g("document.F=Object")),e.close(),e.F):m(r);for(var i=s.length;i--;)delete v[d][s[i]];return v()};a[p]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(h[d]=i(e),n=new h,h[d]=null,n[p]=e):n=v(),void 0===t?n:o.f(n,t)}},3886:function(e,t){var n;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,function(r,i){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,l=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},c=o.push,u=o.indexOf,d={},f=d.toString,p=d.hasOwnProperty,h=p.toString,g=h.call(Object),m={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},b=r.document,_={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in _)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?d[f.call(e)]||"object":typeof e}var S="3.7.1",O=/HTML$/i,A=function(e,t){return new A.fn.init(e,t)};function E(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function C(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}A.fn=A.prototype={jquery:S,constructor:A,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=A.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return A.each(this,e)},map:function(e){return this.pushStack(A.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(A.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(A.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:o.sort,splice:o.splice},A.extend=A.fn.extend=function(){var e,t,n,r,i,o,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||v(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)r=e[t],"__proto__"!==t&&s!==r&&(c&&r&&(A.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[t],o=i&&!Array.isArray(n)?[]:i||A.isPlainObject(n)?n:{},i=!1,s[t]=A.extend(c,o,r)):void 0!==r&&(s[t]=r));return s},A.extend({expando:"jQuery"+(S+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==f.call(e))&&(!(t=s(e))||"function"==typeof(n=p.call(t,"constructor")&&t.constructor)&&h.call(n)===g)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(E(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=A.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(E(Object(e))?A.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!O.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!==s&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,s=[];if(E(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&s.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&s.push(i);return l(s)},guid:1,support:m}),"function"==typeof Symbol&&(A.fn[Symbol.iterator]=o[Symbol.iterator]),A.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){d["[object "+t+"]"]=t.toLowerCase()});var T=o.pop,k=o.sort,L=o.splice,j="[\\x20\\t\\r\\n\\f]",P=new RegExp("^"+j+"+|((?:^|[^\\\\])(?:\\\\.)*)"+j+"+$","g");A.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var I=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function D(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}A.escapeSelector=function(e){return(e+"").replace(I,D)};var N=b,F=c;!function(){var e,t,n,i,s,l,c,d,f,h,g=F,v=A.expando,y=0,b=0,_=ee(),w=ee(),x=ee(),S=ee(),O=function(e,t){return e===t&&(s=!0),0},E="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",I="(?:\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",D="\\["+j+"*("+I+")(?:"+j+"*([*^$|!~]?=)"+j+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+I+"))|)"+j+"*\\]",R=":("+I+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+D+")*)|.*)\\)|)",M=new RegExp(j+"+","g"),$=new RegExp("^"+j+"*,"+j+"*"),q=new RegExp("^"+j+"*([>+~]|"+j+")"+j+"*"),H=new RegExp(j+"|>"),B=new RegExp(R),U=new RegExp("^"+I+"$"),W={ID:new RegExp("^#("+I+")"),CLASS:new RegExp("^\\.("+I+")"),TAG:new RegExp("^("+I+"|[*])"),ATTR:new RegExp("^"+D),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+j+"*(even|odd|(([+-]|)(\\d*)n|)"+j+"*(?:([+-]|)"+j+"*(\\d+)|))"+j+"*\\)|)","i"),bool:new RegExp("^(?:"+E+")$","i"),needsContext:new RegExp("^"+j+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+j+"*((?:-\\d)?\\d*)"+j+"*\\)|)(?=[^-]|$)","i")},z=/^(?:input|select|textarea|button)$/i,V=/^h\d$/i,G=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,K=/[+~]/,X=new RegExp("\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},Y=function(){le()},J=fe(function(e){return!0===e.disabled&&C(e,"fieldset")},{dir:"parentNode",next:"legend"});try{g.apply(o=a.call(N.childNodes),N.childNodes),o[N.childNodes.length].nodeType}catch(e){g={apply:function(e,t){F.apply(e,a.call(t))},call:function(e){F.apply(e,a.call(arguments,1))}}}function Z(e,t,n,r){var i,o,s,a,c,u,p,h=t&&t.ownerDocument,y=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==y&&9!==y&&11!==y)return n;if(!r&&(le(t),t=t||l,d)){if(11!==y&&(c=G.exec(e)))if(i=c[1]){if(9===y){if(!(s=t.getElementById(i)))return n;if(s.id===i)return g.call(n,s),n}else if(h&&(s=h.getElementById(i))&&Z.contains(t,s)&&s.id===i)return g.call(n,s),n}else{if(c[2])return g.apply(n,t.getElementsByTagName(e)),n;if((i=c[3])&&t.getElementsByClassName)return g.apply(n,t.getElementsByClassName(i)),n}if(!(S[e+" "]||f&&f.test(e))){if(p=e,h=t,1===y&&(H.test(e)||q.test(e))){for((h=K.test(e)&&ae(t.parentNode)||t)==t&&m.scope||((a=t.getAttribute("id"))?a=A.escapeSelector(a):t.setAttribute("id",a=v)),o=(u=ue(e)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+de(u[o]);p=u.join(",")}try{return g.apply(n,h.querySelectorAll(p)),n}catch(t){S(e,!0)}finally{a===v&&t.removeAttribute("id")}}}return ye(e.replace(P,"$1"),t,n,r)}function ee(){var e=[];return function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}}function te(e){return e[v]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function re(e){return function(t){return C(t,"input")&&t.type===e}}function ie(e){return function(t){return(C(t,"input")||C(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&J(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function se(e){return te(function(t){return t=+t,te(function(n,r){for(var i,o=e([],n.length,t),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))})})}function ae(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var n,r=e?e.ownerDocument||e:N;return r!=l&&9===r.nodeType&&r.documentElement?(c=(l=r).documentElement,d=!A.isXMLDoc(l),h=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&N!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",Y),m.getById=ne(function(e){return c.appendChild(e).id=A.expando,!l.getElementsByName||!l.getElementsByName(A.expando).length}),m.disconnectedMatch=ne(function(e){return h.call(e,"*")}),m.scope=ne(function(){return l.querySelectorAll(":scope")}),m.cssHas=ne(function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}}),m.getById?(t.filter.ID=function(e){var t=e.replace(X,Q);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(X,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&d){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&d)return t.getElementsByClassName(e)},f=[],ne(function(e){var t;c.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||f.push("\\["+j+"*(?:value|"+E+")"),e.querySelectorAll("[id~="+v+"-]").length||f.push("~="),e.querySelectorAll("a#"+v+"+*").length||f.push(".#.+[+~]"),e.querySelectorAll(":checked").length||f.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),c.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&f.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||f.push("\\["+j+"*name"+j+"*="+j+"*(?:''|\"\")")}),m.cssHas||f.push(":has"),f=f.length&&new RegExp(f.join("|")),O=function(e,t){if(e===t)return s=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!m.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==N&&Z.contains(N,e)?-1:t===l||t.ownerDocument==N&&Z.contains(N,t)?1:i?u.call(i,e)-u.call(i,t):0:4&n?-1:1)},l):l}for(e in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(le(e),d&&!S[t+" "]&&(!f||!f.test(t)))try{var n=h.call(e,t);if(n||m.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){S(t,!0)}return Z(t,l,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=l&&le(e),A.contains(e,t)},Z.attr=function(e,n){(e.ownerDocument||e)!=l&&le(e);var r=t.attrHandle[n.toLowerCase()],i=r&&p.call(t.attrHandle,n.toLowerCase())?r(e,n,!d):void 0;return void 0!==i?i:e.getAttribute(n)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},A.uniqueSort=function(e){var t,n=[],r=0,o=0;if(s=!m.sortStable,i=!m.sortStable&&a.call(e,0),k.call(e,O),s){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)L.call(e,n[r],1)}return i=null,e},A.fn.uniqueSort=function(){return this.pushStack(A.uniqueSort(a.apply(this)))},t=A.expr={cacheLength:50,createPseudo:te,match:W,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(X,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(X,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return W.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&B.test(n)&&(t=ue(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(X,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return C(e,t)}},CLASS:function(e){var t=_[e+" "];return t||(t=new RegExp("(^|"+j+")"+e+"("+j+"|$)"))&&_(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r){var i=Z.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(M," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,f,p,h=o!==s?"nextSibling":"previousSibling",g=t.parentNode,m=a&&t.nodeName.toLowerCase(),b=!l&&!a,_=!1;if(g){if(o){for(;h;){for(d=t;d=d[h];)if(a?C(d,m):1===d.nodeType)return!1;p=h="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?g.firstChild:g.lastChild],s&&b){for(_=(f=(c=(u=g[v]||(g[v]={}))[e]||[])[0]===y&&c[1])&&c[2],d=f&&g.childNodes[f];d=++f&&d&&d[h]||(_=f=0)||p.pop();)if(1===d.nodeType&&++_&&d===t){u[e]=[y,f,_];break}}else if(b&&(_=f=(c=(u=t[v]||(t[v]={}))[e]||[])[0]===y&&c[1]),!1===_)for(;(d=++f&&d&&d[h]||(_=f=0)||p.pop())&&(!(a?C(d,m):1===d.nodeType)||!++_||(b&&((u=d[v]||(d[v]={}))[e]=[y,_]),d!==t)););return(_-=i)===r||_%r===0&&_/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return i[v]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te(function(e,t){for(var r,o=i(e,n),s=o.length;s--;)e[r=u.call(e,o[s])]=!(t[r]=o[s])}):function(e){return i(e,0,r)}):i}},pseudos:{not:te(function(e){var t=[],n=[],r=ve(e.replace(P,"$1"));return r[v]?te(function(e,t,n,i){for(var o,s=r(e,null,i,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:te(function(e){return function(t){return Z(e,t).length>0}}),contains:te(function(e){return e=e.replace(X,Q),function(t){return(t.textContent||A.text(t)).indexOf(e)>-1}}),lang:te(function(e){return U.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(X,Q).toLowerCase(),function(t){var n;do{if(n=d?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(e){var t=r.location&&r.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===c},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return C(e,"input")&&!!e.checked||C(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return V.test(e.nodeName)},input:function(e){return z.test(e.nodeName)},button:function(e){return C(e,"input")&&"button"===e.type||C(e,"button")},text:function(e){var t;return C(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:se(function(){return[0]}),last:se(function(e,t){return[t-1]}),eq:se(function(e,t,n){return[n<0?n+t:n]}),even:se(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:se(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:se(function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e}),gt:se(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=re(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=ie(e);function ce(){}function ue(e,n){var r,i,o,s,a,l,c,u=w[e+" "];if(u)return n?0:u.slice(0);for(a=e,l=[],c=t.preFilter;a;){for(s in r&&!(i=$.exec(a))||(i&&(a=a.slice(i[0].length)||a),l.push(o=[])),r=!1,(i=q.exec(a))&&(r=i.shift(),o.push({value:r,type:i[0].replace(P," ")}),a=a.slice(r.length)),t.filter)!(i=W[s].exec(a))||c[s]&&!(i=c[s](i))||(r=i.shift(),o.push({value:r,type:s,matches:i}),a=a.slice(r.length));if(!r)break}return n?a.length:a?Z.error(e):w(e,l).slice(0)}function de(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function fe(e,t,n){var r=t.dir,i=t.next,o=i||r,s=n&&"parentNode"===o,a=b++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||s)return e(t,n,i);return!1}:function(t,n,l){var c,u,d=[y,a];if(l){for(;t=t[r];)if((1===t.nodeType||s)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||s)if(u=t[v]||(t[v]={}),i&&C(t,i))t=t[r]||t;else{if((c=u[o])&&c[0]===y&&c[1]===a)return d[2]=c[2];if(u[o]=d,d[2]=e(t,n,l))return!0}return!1}}function pe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function he(e,t,n,r,i){for(var o,s=[],a=0,l=e.length,c=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),c&&t.push(a)));return s}function ge(e,t,n,r,i,o){return r&&!r[v]&&(r=ge(r)),i&&!i[v]&&(i=ge(i,o)),te(function(o,s,a,l){var c,d,f,p,h=[],m=[],v=s.length,y=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)Z(e,t[r],n);return n}(t||"*",a.nodeType?[a]:a,[]),b=!e||!o&&t?y:he(y,h,e,a,l);if(n?n(b,p=i||(o?e:v||r)?[]:s,a,l):p=b,r)for(c=he(p,m),r(c,[],a,l),d=c.length;d--;)(f=c[d])&&(p[m[d]]=!(b[m[d]]=f));if(o){if(i||e){if(i){for(c=[],d=p.length;d--;)(f=p[d])&&c.push(b[d]=f);i(null,p=[],c,l)}for(d=p.length;d--;)(f=p[d])&&(c=i?u.call(o,f):h[d])>-1&&(o[c]=!(s[c]=f))}}else p=he(p===s?p.splice(v,p.length):p),i?i(null,s,p,l):g.apply(s,p)})}function me(e){for(var r,i,o,s=e.length,a=t.relative[e[0].type],l=a||t.relative[" "],c=a?1:0,d=fe(function(e){return e===r},l,!0),f=fe(function(e){return u.call(r,e)>-1},l,!0),p=[function(e,t,i){var o=!a&&(i||t!=n)||((r=t).nodeType?d(e,t,i):f(e,t,i));return r=null,o}];c<s;c++)if(i=t.relative[e[c].type])p=[fe(pe(p),i)];else{if((i=t.filter[e[c].type].apply(null,e[c].matches))[v]){for(o=++c;o<s&&!t.relative[e[o].type];o++);return ge(c>1&&pe(p),c>1&&de(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(P,"$1"),i,c<o&&me(e.slice(c,o)),o<s&&me(e=e.slice(o)),o<s&&de(e))}p.push(i)}return pe(p)}function ve(e,r){var i,o=[],s=[],a=x[e+" "];if(!a){for(r||(r=ue(e)),i=r.length;i--;)(a=me(r[i]))[v]?o.push(a):s.push(a);a=x(e,function(e,r){var i=r.length>0,o=e.length>0,s=function(s,a,c,u,f){var p,h,m,v=0,b="0",_=s&&[],w=[],x=n,S=s||o&&t.find.TAG("*",f),O=y+=null==x?1:Math.random()||.1,E=S.length;for(f&&(n=a==l||a||f);b!==E&&null!=(p=S[b]);b++){if(o&&p){for(h=0,a||p.ownerDocument==l||(le(p),c=!d);m=e[h++];)if(m(p,a||l,c)){g.call(u,p);break}f&&(y=O)}i&&((p=!m&&p)&&v--,s&&_.push(p))}if(v+=b,i&&b!==v){for(h=0;m=r[h++];)m(_,w,a,c);if(s){if(v>0)for(;b--;)_[b]||w[b]||(w[b]=T.call(u));w=he(w)}g.apply(u,w),f&&!s&&w.length>0&&v+r.length>1&&A.uniqueSort(u)}return f&&(y=O,n=x),_};return i?te(s):s}(s,o)),a.selector=e}return a}function ye(e,n,r,i){var o,s,a,l,c,u="function"==typeof e&&e,f=!i&&ue(e=u.selector||e);if(r=r||[],1===f.length){if((s=f[0]=f[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===n.nodeType&&d&&t.relative[s[1].type]){if(!(n=(t.find.ID(a.matches[0].replace(X,Q),n)||[])[0]))return r;u&&(n=n.parentNode),e=e.slice(s.shift().value.length)}for(o=W.needsContext.test(e)?0:s.length;o--&&(a=s[o],!t.relative[l=a.type]);)if((c=t.find[l])&&(i=c(a.matches[0].replace(X,Q),K.test(s[0].type)&&ae(n.parentNode)||n))){if(s.splice(o,1),!(e=i.length&&de(s)))return g.apply(r,i),r;break}}return(u||ve(e,f))(i,n,!d,r,!n||K.test(e)&&ae(n.parentNode)||n),r}ce.prototype=t.filters=t.pseudos,t.setFilters=new ce,m.sortStable=v.split("").sort(O).join("")===v,le(),m.sortDetached=ne(function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))}),A.find=Z,A.expr[":"]=A.expr.pseudos,A.unique=A.uniqueSort,Z.compile=ve,Z.select=ye,Z.setDocument=le,Z.tokenize=ue,Z.escape=A.escapeSelector,Z.getText=A.text,Z.isXML=A.isXMLDoc,Z.selectors=A.expr,Z.support=A.support,Z.uniqueSort=A.uniqueSort}();var R=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&A(e).is(n))break;r.push(e)}return r},M=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},$=A.expr.match.needsContext,q=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function H(e,t,n){return v(t)?A.grep(e,function(e,r){return!!t.call(e,r,e)!==n}):t.nodeType?A.grep(e,function(e){return e===t!==n}):"string"!=typeof t?A.grep(e,function(e){return u.call(t,e)>-1!==n}):A.filter(t,e,n)}A.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?A.find.matchesSelector(r,e)?[r]:[]:A.find.matches(e,A.grep(t,function(e){return 1===e.nodeType}))},A.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(A(e).filter(function(){for(t=0;t<r;t++)if(A.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)A.find(e,i[t],n);return r>1?A.uniqueSort(n):n},filter:function(e){return this.pushStack(H(this,e||[],!1))},not:function(e){return this.pushStack(H(this,e||[],!0))},is:function(e){return!!H(this,"string"==typeof e&&$.test(e)?A(e):e||[],!1).length}});var B,U=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(A.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||B,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:U.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof A?t[0]:t,A.merge(this,A.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),q.test(r[1])&&A.isPlainObject(t))for(r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(A):A.makeArray(e,this)}).prototype=A.fn,B=A(b);var W=/^(?:parents|prev(?:Until|All))/,z={children:!0,contents:!0,next:!0,prev:!0};function V(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}A.fn.extend({has:function(e){var t=A(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(A.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],s="string"!=typeof e&&A(e);if(!$.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&A.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?A.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(A(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(A.uniqueSort(A.merge(this.get(),A(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),A.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return R(e,"parentNode")},parentsUntil:function(e,t,n){return R(e,"parentNode",n)},next:function(e){return V(e,"nextSibling")},prev:function(e){return V(e,"previousSibling")},nextAll:function(e){return R(e,"nextSibling")},prevAll:function(e){return R(e,"previousSibling")},nextUntil:function(e,t,n){return R(e,"nextSibling",n)},prevUntil:function(e,t,n){return R(e,"previousSibling",n)},siblings:function(e){return M((e.parentNode||{}).firstChild,e)},children:function(e){return M(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(C(e,"template")&&(e=e.content||e),A.merge([],e.childNodes))}},function(e,t){A.fn[e]=function(n,r){var i=A.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=A.filter(r,i)),this.length>1&&(z[e]||A.uniqueSort(i),W.test(e)&&i.reverse()),this.pushStack(i)}});var G=/[^\x20\t\r\n\f]+/g;function K(e){return e}function X(e){throw e}function Q(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}A.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return A.each(e.match(G)||[],function(e,n){t[n]=!0}),t}(e):A.extend({},e);var t,n,r,i,o=[],s=[],a=-1,l=function(){for(i=i||e.once,r=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){A.each(n,function(n,r){v(r)?e.unique&&c.has(r)||o.push(r):r&&r.length&&"string"!==x(r)&&t(r)})}(arguments),n&&!t&&l()),this},remove:function(){return A.each(arguments,function(e,t){for(var n;(n=A.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--}),this},has:function(e){return e?A.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},A.extend({Deferred:function(e){var t=[["notify","progress",A.Callbacks("memory"),A.Callbacks("memory"),2],["resolve","done",A.Callbacks("once memory"),A.Callbacks("once memory"),0,"resolved"],["reject","fail",A.Callbacks("once memory"),A.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return A.Deferred(function(n){A.each(t,function(t,r){var i=v(e[r[4]])&&e[r[4]];o[r[1]](function(){var e=i&&i.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)})}),e=null}).promise()},then:function(e,n,i){var o=0;function s(e,t,n,i){return function(){var a=this,l=arguments,c=function(){var r,c;if(!(e<o)){if((r=n.apply(a,l))===t.promise())throw new TypeError("Thenable self-resolution");c=r&&("object"==typeof r||"function"==typeof r)&&r.then,v(c)?i?c.call(r,s(o,t,K,i),s(o,t,X,i)):(o++,c.call(r,s(o,t,K,i),s(o,t,X,i),s(o,t,K,t.notifyWith))):(n!==K&&(a=void 0,l=[r]),(i||t.resolveWith)(a,l))}},u=i?c:function(){try{c()}catch(r){A.Deferred.exceptionHook&&A.Deferred.exceptionHook(r,u.error),e+1>=o&&(n!==X&&(a=void 0,l=[r]),t.rejectWith(a,l))}};e?u():(A.Deferred.getErrorHook?u.error=A.Deferred.getErrorHook():A.Deferred.getStackHook&&(u.error=A.Deferred.getStackHook()),r.setTimeout(u))}}return A.Deferred(function(r){t[0][3].add(s(0,r,v(i)?i:K,r.notifyWith)),t[1][3].add(s(0,r,v(e)?e:K)),t[2][3].add(s(0,r,v(n)?n:X))}).promise()},promise:function(e){return null!=e?A.extend(e,i):i}},o={};return A.each(t,function(e,r){var s=r[2],a=r[5];i[r[1]]=s.add,a&&s.add(function(){n=a},t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),s.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=s.fireWith}),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=a.call(arguments),o=A.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?a.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Q(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||v(i[n]&&i[n].then)))return o.then();for(;n--;)Q(i[n],s(n),o.reject);return o.promise()}});var Y=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;A.Deferred.exceptionHook=function(e,t){r.console&&r.console.warn&&e&&Y.test(e.name)&&r.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},A.readyException=function(e){r.setTimeout(function(){throw e})};var J=A.Deferred();function Z(){b.removeEventListener("DOMContentLoaded",Z),r.removeEventListener("load",Z),A.ready()}A.fn.ready=function(e){return J.then(e).catch(function(e){A.readyException(e)}),this},A.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--A.readyWait:A.isReady)||(A.isReady=!0,!0!==e&&--A.readyWait>0||J.resolveWith(b,[A]))}}),A.ready.then=J.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(A.ready):(b.addEventListener("DOMContentLoaded",Z),r.addEventListener("load",Z));var ee=function(e,t,n,r,i,o,s){var a=0,l=e.length,c=null==n;if("object"===x(n))for(a in i=!0,n)ee(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,v(r)||(s=!0),c&&(s?(t.call(e,r),t=null):(c=t,t=function(e,t,n){return c.call(A(e),n)})),t))for(;a<l;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:c?t.call(e):l?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function re(e,t){return t.toUpperCase()}function ie(e){return e.replace(te,"ms-").replace(ne,re)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function se(){this.expando=A.expando+se.uid++}se.uid=1,se.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[ie(t)]=n;else for(r in t)i[ie(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][ie(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(ie):(t=ie(t))in r?[t]:t.match(G)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||A.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!A.isEmptyObject(t)}};var ae=new se,le=new se,ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ue=/[A-Z]/g;function de(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ue,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ce.test(e)?JSON.parse(e):e)}(n)}catch(e){}le.set(e,t,n)}else n=void 0;return n}A.extend({hasData:function(e){return le.hasData(e)||ae.hasData(e)},data:function(e,t,n){return le.access(e,t,n)},removeData:function(e,t){le.remove(e,t)},_data:function(e,t,n){return ae.access(e,t,n)},_removeData:function(e,t){ae.remove(e,t)}}),A.fn.extend({data:function(e,t){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(i=le.get(o),1===o.nodeType&&!ae.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=ie(r.slice(5)),de(o,r,i[r]));ae.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each(function(){le.set(this,e)}):ee(this,function(t){var n;if(o&&void 0===t)return void 0!==(n=le.get(o,e))||void 0!==(n=de(o,e))?n:void 0;this.each(function(){le.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){le.remove(this,e)})}}),A.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ae.get(e,t),n&&(!r||Array.isArray(n)?r=ae.access(e,t,A.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=A.queue(e,t),r=n.length,i=n.shift(),o=A._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){A.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ae.get(e,n)||ae.access(e,n,{empty:A.Callbacks("once memory").add(function(){ae.remove(e,[t+"queue",n])})})}}),A.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?A.queue(this[0],e):void 0===t?this:this.each(function(){var n=A.queue(this,e,t);A._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&A.dequeue(this,e)})},dequeue:function(e){return this.each(function(){A.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=A.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=ae.get(o[s],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var fe=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,pe=new RegExp("^(?:([+-])=|)("+fe+")([a-z%]*)$","i"),he=["Top","Right","Bottom","Left"],ge=b.documentElement,me=function(e){return A.contains(e.ownerDocument,e)},ve={composed:!0};ge.getRootNode&&(me=function(e){return A.contains(e.ownerDocument,e)||e.getRootNode(ve)===e.ownerDocument});var ye=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&me(e)&&"none"===A.css(e,"display")};function be(e,t,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return A.css(e,t,"")},l=a(),c=n&&n[3]||(A.cssNumber[t]?"":"px"),u=e.nodeType&&(A.cssNumber[t]||"px"!==c&&+l)&&pe.exec(A.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)A.style(e,t,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,A.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var _e={};function we(e){var t,n=e.ownerDocument,r=e.nodeName,i=_e[r];return i||(t=n.body.appendChild(n.createElement(r)),i=A.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),_e[r]=i,i)}function xe(e,t){for(var n,r,i=[],o=0,s=e.length;o<s;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=ae.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ye(r)&&(i[o]=we(r))):"none"!==n&&(i[o]="none",ae.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}A.fn.extend({show:function(){return xe(this,!0)},hide:function(){return xe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ye(this)?A(this).show():A(this).hide()})}});var Se,Oe,Ae=/^(?:checkbox|radio)$/i,Ee=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ce=/^$|^module$|\/(?:java|ecma)script/i;Se=b.createDocumentFragment().appendChild(b.createElement("div")),(Oe=b.createElement("input")).setAttribute("type","radio"),Oe.setAttribute("checked","checked"),Oe.setAttribute("name","t"),Se.appendChild(Oe),m.checkClone=Se.cloneNode(!0).cloneNode(!0).lastChild.checked,Se.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!Se.cloneNode(!0).lastChild.defaultValue,Se.innerHTML="<option></option>",m.option=!!Se.lastChild;var Te={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ke(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&C(e,t)?A.merge([e],n):n}function Le(e,t){for(var n=0,r=e.length;n<r;n++)ae.set(e[n],"globalEval",!t||ae.get(t[n],"globalEval"))}Te.tbody=Te.tfoot=Te.colgroup=Te.caption=Te.thead,Te.th=Te.td,m.option||(Te.optgroup=Te.option=[1,"<select multiple='multiple'>","</select>"]);var je=/<|&#?\w+;/;function Pe(e,t,n,r,i){for(var o,s,a,l,c,u,d=t.createDocumentFragment(),f=[],p=0,h=e.length;p<h;p++)if((o=e[p])||0===o)if("object"===x(o))A.merge(f,o.nodeType?[o]:o);else if(je.test(o)){for(s=s||d.appendChild(t.createElement("div")),a=(Ee.exec(o)||["",""])[1].toLowerCase(),l=Te[a]||Te._default,s.innerHTML=l[1]+A.htmlPrefilter(o)+l[2],u=l[0];u--;)s=s.lastChild;A.merge(f,s.childNodes),(s=d.firstChild).textContent=""}else f.push(t.createTextNode(o));for(d.textContent="",p=0;o=f[p++];)if(r&&A.inArray(o,r)>-1)i&&i.push(o);else if(c=me(o),s=ke(d.appendChild(o),"script"),c&&Le(s),n)for(u=0;o=s[u++];)Ce.test(o.type||"")&&n.push(o);return d}var Ie=/^([^.]*)(?:\.(.+)|)/;function De(){return!0}function Ne(){return!1}function Fe(e,t,n,r,i,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(r=r||n,n=void 0),t)Fe(e,a,n,r,t[a],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Ne;else if(!i)return e;return 1===o&&(s=i,i=function(e){return A().off(e),s.apply(this,arguments)},i.guid=s.guid||(s.guid=A.guid++)),e.each(function(){A.event.add(this,t,i,r,n)})}function Re(e,t,n){n?(ae.set(e,t,!1),A.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ae.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(A.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=a.call(arguments),ae.set(this,t,r),this[t](),n=ae.get(this,t),ae.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ae.set(this,t,A.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=De)}})):void 0===ae.get(e,t)&&A.event.add(e,t,De)}A.event={global:{},add:function(e,t,n,r,i){var o,s,a,l,c,u,d,f,p,h,g,m=ae.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&A.find.matchesSelector(ge,i),n.guid||(n.guid=A.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(t){return void 0!==A&&A.event.triggered!==t.type?A.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(G)||[""]).length;c--;)p=g=(a=Ie.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),p&&(d=A.event.special[p]||{},p=(i?d.delegateType:d.bindType)||p,d=A.event.special[p]||{},u=A.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&A.expr.match.needsContext.test(i),namespace:h.join(".")},o),(f=l[p])||((f=l[p]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(e,r,h,s)||e.addEventListener&&e.addEventListener(p,s)),d.add&&(d.add.call(e,u),u.handler.guid||(u.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,u):f.push(u),A.event.global[p]=!0)},remove:function(e,t,n,r,i){var o,s,a,l,c,u,d,f,p,h,g,m=ae.hasData(e)&&ae.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(G)||[""]).length;c--;)if(p=g=(a=Ie.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),p){for(d=A.event.special[p]||{},f=l[p=(r?d.delegateType:d.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)u=f[o],!i&&g!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(f.splice(o,1),u.selector&&f.delegateCount--,d.remove&&d.remove.call(e,u));s&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||A.removeEvent(e,p,m.handle),delete l[p])}else for(p in l)A.event.remove(e,p+t[c],n,r,!0);A.isEmptyObject(l)&&ae.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,s,a=new Array(arguments.length),l=A.event.fix(e),c=(ae.get(this,"events")||Object.create(null))[l.type]||[],u=A.event.special[l.type]||{};for(a[0]=l,t=1;t<arguments.length;t++)a[t]=arguments[t];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=A.event.handlers.call(this,l,c),t=0;(i=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(r=((A.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,r,i,o,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[i=(r=t[n]).selector+" "]&&(s[i]=r.needsContext?A(i,this).index(c)>-1:A.find(i,this,null,[c]).length),s[i]&&o.push(r);o.length&&a.push({elem:c,handlers:o})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(e,t){Object.defineProperty(A.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[A.expando]?e:new A.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Ae.test(t.type)&&t.click&&C(t,"input")&&Re(t,"click",!0),!1},trigger:function(e){var t=this||e;return Ae.test(t.type)&&t.click&&C(t,"input")&&Re(t,"click"),!0},_default:function(e){var t=e.target;return Ae.test(t.type)&&t.click&&C(t,"input")&&ae.get(t,"click")||C(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},A.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},A.Event=function(e,t){if(!(this instanceof A.Event))return new A.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?De:Ne,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&A.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[A.expando]=!0},A.Event.prototype={constructor:A.Event,isDefaultPrevented:Ne,isPropagationStopped:Ne,isImmediatePropagationStopped:Ne,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=De,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=De,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=De,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},A.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},A.event.addProp),A.each({focus:"focusin",blur:"focusout"},function(e,t){function n(e){if(b.documentMode){var n=ae.get(this,"handle"),r=A.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else A.event.simulate(t,e.target,A.event.fix(e))}A.event.special[e]={setup:function(){var r;if(Re(this,e,!0),!b.documentMode)return!1;(r=ae.get(this,t))||this.addEventListener(t,n),ae.set(this,t,(r||0)+1)},trigger:function(){return Re(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=ae.get(this,t)-1)?ae.set(this,t,e):(this.removeEventListener(t,n),ae.remove(this,t))},_default:function(t){return ae.get(t.target,e)},delegateType:t},A.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ae.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t)-1;o?ae.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ae.remove(i,t))}}}),A.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){A.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||A.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}}),A.fn.extend({on:function(e,t,n,r){return Fe(this,e,t,n,r)},one:function(e,t,n,r){return Fe(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,A(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Ne),this.each(function(){A.event.remove(this,e,n,t)})}});var Me=/<script|<style|<link/i,$e=/checked\s*(?:[^=]|=\s*.checked.)/i,qe=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function He(e,t){return C(e,"table")&&C(11!==t.nodeType?t:t.firstChild,"tr")&&A(e).children("tbody")[0]||e}function Be(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Ue(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function We(e,t){var n,r,i,o,s,a;if(1===t.nodeType){if(ae.hasData(e)&&(a=ae.get(e).events))for(i in ae.remove(t,"handle events"),a)for(n=0,r=a[i].length;n<r;n++)A.event.add(t,i,a[i][n]);le.hasData(e)&&(o=le.access(e),s=A.extend({},o),le.set(t,s))}}function ze(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Ae.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Ve(e,t,n,r){t=l(t);var i,o,s,a,c,u,d=0,f=e.length,p=f-1,h=t[0],g=v(h);if(g||f>1&&"string"==typeof h&&!m.checkClone&&$e.test(h))return e.each(function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),Ve(o,t,n,r)});if(f&&(o=(i=Pe(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=A.map(ke(i,"script"),Be)).length;d<f;d++)c=i,d!==p&&(c=A.clone(c,!0,!0),a&&A.merge(s,ke(c,"script"))),n.call(e[d],c,d);if(a)for(u=s[s.length-1].ownerDocument,A.map(s,Ue),d=0;d<a;d++)c=s[d],Ce.test(c.type||"")&&!ae.access(c,"globalEval")&&A.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?A._evalUrl&&!c.noModule&&A._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):w(c.textContent.replace(qe,""),c,u))}return e}function Ge(e,t,n){for(var r,i=t?A.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||A.cleanData(ke(r)),r.parentNode&&(n&&me(r)&&Le(ke(r,"script")),r.parentNode.removeChild(r));return e}A.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,s,a=e.cloneNode(!0),l=me(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||A.isXMLDoc(e)))for(s=ke(a),r=0,i=(o=ke(e)).length;r<i;r++)ze(o[r],s[r]);if(t)if(n)for(o=o||ke(e),s=s||ke(a),r=0,i=o.length;r<i;r++)We(o[r],s[r]);else We(e,a);return(s=ke(a,"script")).length>0&&Le(s,!l&&ke(e,"script")),a},cleanData:function(e){for(var t,n,r,i=A.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[ae.expando]){if(t.events)for(r in t.events)i[r]?A.event.remove(n,r):A.removeEvent(n,r,t.handle);n[ae.expando]=void 0}n[le.expando]&&(n[le.expando]=void 0)}}}),A.fn.extend({detach:function(e){return Ge(this,e,!0)},remove:function(e){return Ge(this,e)},text:function(e){return ee(this,function(e){return void 0===e?A.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ve(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||He(this,e).appendChild(e)})},prepend:function(){return Ve(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=He(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Ve(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ve(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(A.cleanData(ke(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return A.clone(this,e,t)})},html:function(e){return ee(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Me.test(e)&&!Te[(Ee.exec(e)||["",""])[1].toLowerCase()]){e=A.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(A.cleanData(ke(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return Ve(this,arguments,function(t){var n=this.parentNode;A.inArray(this,e)<0&&(A.cleanData(ke(this)),n&&n.replaceChild(t,this))},e)}}),A.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){A.fn[e]=function(e){for(var n,r=[],i=A(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),A(i[s])[t](n),c.apply(r,n.get());return this.pushStack(r)}});var Ke=new RegExp("^("+fe+")(?!px)[a-z%]+$","i"),Xe=/^--/,Qe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=r),t.getComputedStyle(e)},Ye=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Je=new RegExp(he.join("|"),"i");function Ze(e,t,n){var r,i,o,s,a=Xe.test(t),l=e.style;return(n=n||Qe(e))&&(s=n.getPropertyValue(t)||n[t],a&&s&&(s=s.replace(P,"$1")||void 0),""!==s||me(e)||(s=A.style(e,t)),!m.pixelBoxStyles()&&Ke.test(s)&&Je.test(t)&&(r=l.width,i=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=n.width,l.width=r,l.minWidth=i,l.maxWidth=o)),void 0!==s?s+"":s}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ge.appendChild(c).appendChild(u);var e=r.getComputedStyle(u);n="1%"!==e.top,l=12===t(e.marginLeft),u.style.right="60%",s=36===t(e.right),i=36===t(e.width),u.style.position="absolute",o=12===t(u.offsetWidth/3),ge.removeChild(c),u=null}}function t(e){return Math.round(parseFloat(e))}var n,i,o,s,a,l,c=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,A.extend(m,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,i;return null==a&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",ge.appendChild(e).appendChild(t).appendChild(n),i=r.getComputedStyle(t),a=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,ge.removeChild(e)),a}}))}();var tt=["Webkit","Moz","ms"],nt=b.createElement("div").style,rt={};function it(e){var t=A.cssProps[e]||rt[e];return t||(e in nt?e:rt[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,st={position:"absolute",visibility:"hidden",display:"block"},at={letterSpacing:"0",fontWeight:"400"};function lt(e,t,n){var r=pe.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ct(e,t,n,r,i,o){var s="width"===t?1:0,a=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(c+=A.css(e,n+he[s],!0,i)),r?("content"===n&&(l-=A.css(e,"padding"+he[s],!0,i)),"margin"!==n&&(l-=A.css(e,"border"+he[s]+"Width",!0,i))):(l+=A.css(e,"padding"+he[s],!0,i),"padding"!==n?l+=A.css(e,"border"+he[s]+"Width",!0,i):a+=A.css(e,"border"+he[s]+"Width",!0,i));return!r&&o>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l+c}function ut(e,t,n){var r=Qe(e),i=(!m.boxSizingReliable()||n)&&"border-box"===A.css(e,"boxSizing",!1,r),o=i,s=Ze(e,t,r),a="offset"+t[0].toUpperCase()+t.slice(1);if(Ke.test(s)){if(!n)return s;s="auto"}return(!m.boxSizingReliable()&&i||!m.reliableTrDimensions()&&C(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===A.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===A.css(e,"boxSizing",!1,r),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+ct(e,t,n||(i?"border":"content"),o,r,s)+"px"}function dt(e,t,n,r,i){return new dt.prototype.init(e,t,n,r,i)}A.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ze(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,s,a=ie(t),l=Xe.test(t),c=e.style;if(l||(t=it(a)),s=A.cssHooks[t]||A.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:c[t];"string"===(o=typeof n)&&(i=pe.exec(n))&&i[1]&&(n=be(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(A.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var i,o,s,a=ie(t);return Xe.test(t)||(t=it(a)),(s=A.cssHooks[t]||A.cssHooks[a])&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=Ze(e,t,r)),"normal"===i&&t in at&&(i=at[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),A.each(["height","width"],function(e,t){A.cssHooks[t]={get:function(e,n,r){if(n)return!ot.test(A.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ut(e,t,r):Ye(e,st,function(){return ut(e,t,r)})},set:function(e,n,r){var i,o=Qe(e),s=!m.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===A.css(e,"boxSizing",!1,o),l=r?ct(e,t,r,a,o):0;return a&&s&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-ct(e,t,"border",!1,o)-.5)),l&&(i=pe.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=A.css(e,t)),lt(0,n,l)}}}),A.cssHooks.marginLeft=et(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ze(e,"marginLeft"))||e.getBoundingClientRect().left-Ye(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),A.each({margin:"",padding:"",border:"Width"},function(e,t){A.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+he[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(A.cssHooks[e+t].set=lt)}),A.fn.extend({css:function(e,t){return ee(this,function(e,t,n){var r,i,o={},s=0;if(Array.isArray(t)){for(r=Qe(e),i=t.length;s<i;s++)o[t[s]]=A.css(e,t[s],!1,r);return o}return void 0!==n?A.style(e,t,n):A.css(e,t)},e,t,arguments.length>1)}}),A.Tween=dt,dt.prototype={constructor:dt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||A.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(A.cssNumber[n]?"":"px")},cur:function(){var e=dt.propHooks[this.prop];return e&&e.get?e.get(this):dt.propHooks._default.get(this)},run:function(e){var t,n=dt.propHooks[this.prop];return this.options.duration?this.pos=t=A.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):dt.propHooks._default.set(this),this}},dt.prototype.init.prototype=dt.prototype,dt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=A.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){A.fx.step[e.prop]?A.fx.step[e.prop](e):1!==e.elem.nodeType||!A.cssHooks[e.prop]&&null==e.elem.style[it(e.prop)]?e.elem[e.prop]=e.now:A.style(e.elem,e.prop,e.now+e.unit)}}},dt.propHooks.scrollTop=dt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},A.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},A.fx=dt.prototype.init,A.fx.step={};var ft,pt,ht=/^(?:toggle|show|hide)$/,gt=/queueHooks$/;function mt(){pt&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(mt):r.setTimeout(mt,A.fx.interval),A.fx.tick())}function vt(){return r.setTimeout(function(){ft=void 0}),ft=Date.now()}function yt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=he[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function bt(e,t,n){for(var r,i=(_t.tweeners[t]||[]).concat(_t.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function _t(e,t,n){var r,i,o=0,s=_t.prefilters.length,a=A.Deferred().always(function(){delete l.elem}),l=function(){if(i)return!1;for(var t=ft||vt(),n=Math.max(0,c.startTime+c.duration-t),r=1-(n/c.duration||0),o=0,s=c.tweens.length;o<s;o++)c.tweens[o].run(r);return a.notifyWith(e,[c,r,n]),r<1&&s?n:(s||a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c]),!1)},c=a.promise({elem:e,props:A.extend({},t),opts:A.extend(!0,{specialEasing:{},easing:A.easing._default},n),originalProperties:t,originalOptions:n,startTime:ft||vt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=A.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(r),r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return t?(a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c,t])):a.rejectWith(e,[c,t]),this}}),u=c.props;for(!function(e,t){var n,r,i,o,s;for(n in e)if(i=t[r=ie(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(s=A.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,c.opts.specialEasing);o<s;o++)if(r=_t.prefilters[o].call(c,e,u,c.opts))return v(r.stop)&&(A._queueHooks(c.elem,c.opts.queue).stop=r.stop.bind(r)),r;return A.map(u,bt,c),v(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),A.fx.timer(A.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c}A.Animation=A.extend(_t,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return be(n.elem,e,pe.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(G);for(var n,r=0,i=e.length;r<i;r++)n=e[r],_t.tweeners[n]=_t.tweeners[n]||[],_t.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,s,a,l,c,u,d="width"in t||"height"in t,f=this,p={},h=e.style,g=e.nodeType&&ye(e),m=ae.get(e,"fxshow");for(r in n.queue||(null==(s=A._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always(function(){f.always(function(){s.unqueued--,A.queue(e,"fx").length||s.empty.fire()})})),t)if(i=t[r],ht.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}p[r]=m&&m[r]||A.style(e,r)}if((l=!A.isEmptyObject(t))||!A.isEmptyObject(p))for(r in d&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(c=m&&m.display)&&(c=ae.get(e,"display")),"none"===(u=A.css(e,"display"))&&(c?u=c:(xe([e],!0),c=e.style.display||c,u=A.css(e,"display"),xe([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===A.css(e,"float")&&(l||(f.done(function(){h.display=c}),null==c&&(u=h.display,c="none"===u?"":u)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",f.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=ae.access(e,"fxshow",{display:c}),o&&(m.hidden=!g),g&&xe([e],!0),f.done(function(){for(r in g||xe([e]),ae.remove(e,"fxshow"),p)A.style(e,r,p[r])})),l=bt(g?m[r]:0,r,f),r in m||(m[r]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?_t.prefilters.unshift(e):_t.prefilters.push(e)}}),A.speed=function(e,t,n){var r=e&&"object"==typeof e?A.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return A.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in A.fx.speeds?r.duration=A.fx.speeds[r.duration]:r.duration=A.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&A.dequeue(this,r.queue)},r},A.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ye).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=A.isEmptyObject(e),o=A.speed(t,n,r),s=function(){var t=_t(this,A.extend({},e),o);(i||ae.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,i=null!=e&&e+"queueHooks",o=A.timers,s=ae.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&gt.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||A.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=ae.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=A.timers,s=r?r.length:0;for(n.finish=!0,A.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish})}}),A.each(["toggle","show","hide"],function(e,t){var n=A.fn[t];A.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(yt(t,!0),e,r,i)}}),A.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){A.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),A.timers=[],A.fx.tick=function(){var e,t=0,n=A.timers;for(ft=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||A.fx.stop(),ft=void 0},A.fx.timer=function(e){A.timers.push(e),A.fx.start()},A.fx.interval=13,A.fx.start=function(){pt||(pt=!0,mt())},A.fx.stop=function(){pt=null},A.fx.speeds={slow:600,fast:200,_default:400},A.fn.delay=function(e,t){return e=A.fx&&A.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var i=r.setTimeout(t,e);n.stop=function(){r.clearTimeout(i)}})},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",m.checkOn=""!==e.value,m.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",m.radioValue="t"===e.value}();var wt,xt=A.expr.attrHandle;A.fn.extend({attr:function(e,t){return ee(this,A.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){A.removeAttr(this,e)})}}),A.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?A.prop(e,t,n):(1===o&&A.isXMLDoc(e)||(i=A.attrHooks[t.toLowerCase()]||(A.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void A.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=A.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&C(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(G);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?A.removeAttr(e,n):e.setAttribute(n,n),n}},A.each(A.expr.match.bool.source.match(/\w+/g),function(e,t){var n=xt[t]||A.find.attr;xt[t]=function(e,t,r){var i,o,s=t.toLowerCase();return r||(o=xt[s],xt[s]=i,i=null!=n(e,t,r)?s:null,xt[s]=o),i}});var St=/^(?:input|select|textarea|button)$/i,Ot=/^(?:a|area)$/i;function At(e){return(e.match(G)||[]).join(" ")}function Et(e){return e.getAttribute&&e.getAttribute("class")||""}function Ct(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(G)||[]}A.fn.extend({prop:function(e,t){return ee(this,A.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[A.propFix[e]||e]})}}),A.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&A.isXMLDoc(e)||(t=A.propFix[t]||t,i=A.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=A.find.attr(e,"tabindex");return t?parseInt(t,10):St.test(e.nodeName)||Ot.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(A.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),A.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){A.propFix[this.toLowerCase()]=this}),A.fn.extend({addClass:function(e){var t,n,r,i,o,s;return v(e)?this.each(function(t){A(this).addClass(e.call(this,t,Et(this)))}):(t=Ct(e)).length?this.each(function(){if(r=Et(this),n=1===this.nodeType&&" "+At(r)+" "){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");s=At(n),r!==s&&this.setAttribute("class",s)}}):this},removeClass:function(e){var t,n,r,i,o,s;return v(e)?this.each(function(t){A(this).removeClass(e.call(this,t,Et(this)))}):arguments.length?(t=Ct(e)).length?this.each(function(){if(r=Et(this),n=1===this.nodeType&&" "+At(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");s=At(n),r!==s&&this.setAttribute("class",s)}}):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,s=typeof e,a="string"===s||Array.isArray(e);return v(e)?this.each(function(n){A(this).toggleClass(e.call(this,n,Et(this),t),t)}):"boolean"==typeof t&&a?t?this.addClass(e):this.removeClass(e):(n=Ct(e),this.each(function(){if(a)for(o=A(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==s||((r=Et(this))&&ae.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ae.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+At(Et(n))+" ").indexOf(t)>-1)return!0;return!1}});var Tt=/\r/g;A.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=v(e),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,A(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=A.map(i,function(e){return null==e?"":e+""})),(t=A.valHooks[this.type]||A.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))})):i?(t=A.valHooks[i.type]||A.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(Tt,""):null==n?"":n:void 0}}),A.extend({valHooks:{option:{get:function(e){var t=A.find.attr(e,"value");return null!=t?t:At(A.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?o+1:i.length;for(r=o<0?l:s?o:0;r<l;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!C(n.parentNode,"optgroup"))){if(t=A(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=A.makeArray(t),s=i.length;s--;)((r=i[s]).selected=A.inArray(A.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),A.each(["radio","checkbox"],function(){A.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=A.inArray(A(e).val(),t)>-1}},m.checkOn||(A.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var kt=r.location,Lt={guid:Date.now()},jt=/\?/;A.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new r.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||A.error("Invalid XML: "+(n?A.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var Pt=/^(?:focusinfocus|focusoutblur)$/,It=function(e){e.stopPropagation()};A.extend(A.event,{trigger:function(e,t,n,i){var o,s,a,l,c,u,d,f,h=[n||b],g=p.call(e,"type")?e.type:e,m=p.call(e,"namespace")?e.namespace.split("."):[];if(s=f=a=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!Pt.test(g+A.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(e=e[A.expando]?e:new A.Event(g,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:A.makeArray(t,[e]),d=A.event.special[g]||{},i||!d.trigger||!1!==d.trigger.apply(n,t))){if(!i&&!d.noBubble&&!y(n)){for(l=d.delegateType||g,Pt.test(l+g)||(s=s.parentNode);s;s=s.parentNode)h.push(s),a=s;a===(n.ownerDocument||b)&&h.push(a.defaultView||a.parentWindow||r)}for(o=0;(s=h[o++])&&!e.isPropagationStopped();)f=s,e.type=o>1?l:d.bindType||g,(u=(ae.get(s,"events")||Object.create(null))[e.type]&&ae.get(s,"handle"))&&u.apply(s,t),(u=c&&s[c])&&u.apply&&oe(s)&&(e.result=u.apply(s,t),!1===e.result&&e.preventDefault());return e.type=g,i||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(h.pop(),t)||!oe(n)||c&&v(n[g])&&!y(n)&&((a=n[c])&&(n[c]=null),A.event.triggered=g,e.isPropagationStopped()&&f.addEventListener(g,It),n[g](),e.isPropagationStopped()&&f.removeEventListener(g,It),A.event.triggered=void 0,a&&(n[c]=a)),e.result}},simulate:function(e,t,n){var r=A.extend(new A.Event,n,{type:e,isSimulated:!0});A.event.trigger(r,null,t)}}),A.fn.extend({trigger:function(e,t){return this.each(function(){A.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return A.event.trigger(e,t,n,!0)}});var Dt=/\[\]$/,Nt=/\r?\n/g,Ft=/^(?:submit|button|image|reset|file)$/i,Rt=/^(?:input|select|textarea|keygen)/i;function Mt(e,t,n,r){var i;if(Array.isArray(t))A.each(t,function(t,i){n||Dt.test(e)?r(e,i):Mt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)});else if(n||"object"!==x(t))r(e,t);else for(i in t)Mt(e+"["+i+"]",t[i],n,r)}A.param=function(e,t){var n,r=[],i=function(e,t){var n=v(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!A.isPlainObject(e))A.each(e,function(){i(this.name,this.value)});else for(n in e)Mt(n,e[n],t,i);return r.join("&")},A.fn.extend({serialize:function(){return A.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=A.prop(this,"elements");return e?A.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!A(this).is(":disabled")&&Rt.test(this.nodeName)&&!Ft.test(e)&&(this.checked||!Ae.test(e))}).map(function(e,t){var n=A(this).val();return null==n?null:Array.isArray(n)?A.map(n,function(e){return{name:t.name,value:e.replace(Nt,"\r\n")}}):{name:t.name,value:n.replace(Nt,"\r\n")}}).get()}});var $t=/%20/g,qt=/#.*$/,Ht=/([?&])_=[^&]*/,Bt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ut=/^(?:GET|HEAD)$/,Wt=/^\/\//,zt={},Vt={},Gt="*/".concat("*"),Kt=b.createElement("a");function Xt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(G)||[];if(v(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Qt(e,t,n,r){var i={},o=e===Vt;function s(a){var l;return i[a]=!0,A.each(e[a]||[],function(e,a){var c=a(t,n,r);return"string"!=typeof c||o||i[c]?o?!(l=c):void 0:(t.dataTypes.unshift(c),s(c),!1)}),l}return s(t.dataTypes[0])||!i["*"]&&s("*")}function Yt(e,t){var n,r,i=A.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&A.extend(!0,e,r),e}Kt.href=kt.href,A.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:kt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(kt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Gt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":A.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Yt(Yt(e,A.ajaxSettings),t):Yt(A.ajaxSettings,e)},ajaxPrefilter:Xt(zt),ajaxTransport:Xt(Vt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,s,a,l,c,u,d,f,p=A.ajaxSetup({},t),h=p.context||p,g=p.context&&(h.nodeType||h.jquery)?A(h):A.event,m=A.Deferred(),v=A.Callbacks("once memory"),y=p.statusCode||{},_={},w={},x="canceled",S={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s)for(s={};t=Bt.exec(o);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(e,t){return null==c&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==c&&(p.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)S.always(e[S.status]);else for(t in e)y[t]=[y[t],e[t]];return this},abort:function(e){var t=e||x;return n&&n.abort(t),O(0,t),this}};if(m.promise(S),p.url=((e||p.url||kt.href)+"").replace(Wt,kt.protocol+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(G)||[""],null==p.crossDomain){l=b.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=Kt.protocol+"//"+Kt.host!=l.protocol+"//"+l.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=A.param(p.data,p.traditional)),Qt(zt,p,t,S),c)return S;for(d in(u=A.event&&p.global)&&0===A.active++&&A.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Ut.test(p.type),i=p.url.replace(qt,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace($t,"+")):(f=p.url.slice(i.length),p.data&&(p.processData||"string"==typeof p.data)&&(i+=(jt.test(i)?"&":"?")+p.data,delete p.data),!1===p.cache&&(i=i.replace(Ht,"$1"),f=(jt.test(i)?"&":"?")+"_="+Lt.guid+++f),p.url=i+f),p.ifModified&&(A.lastModified[i]&&S.setRequestHeader("If-Modified-Since",A.lastModified[i]),A.etag[i]&&S.setRequestHeader("If-None-Match",A.etag[i])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&S.setRequestHeader("Content-Type",p.contentType),S.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Gt+"; q=0.01":""):p.accepts["*"]),p.headers)S.setRequestHeader(d,p.headers[d]);if(p.beforeSend&&(!1===p.beforeSend.call(h,S,p)||c))return S.abort();if(x="abort",v.add(p.complete),S.done(p.success),S.fail(p.error),n=Qt(Vt,p,t,S)){if(S.readyState=1,u&&g.trigger("ajaxSend",[S,p]),c)return S;p.async&&p.timeout>0&&(a=r.setTimeout(function(){S.abort("timeout")},p.timeout));try{c=!1,n.send(_,O)}catch(e){if(c)throw e;O(-1,e)}}else O(-1,"No Transport");function O(e,t,s,l){var d,f,b,_,w,x=t;c||(c=!0,a&&r.clearTimeout(a),n=void 0,o=l||"",S.readyState=e>0?4:0,d=e>=200&&e<300||304===e,s&&(_=function(e,t,n){for(var r,i,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(p,S,s)),!d&&A.inArray("script",p.dataTypes)>-1&&A.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),_=function(e,t,n,r){var i,o,s,a,l,c={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(i in c)if((a=i.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[i]:!0!==c[i]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(p,_,S,d),d?(p.ifModified&&((w=S.getResponseHeader("Last-Modified"))&&(A.lastModified[i]=w),(w=S.getResponseHeader("etag"))&&(A.etag[i]=w)),204===e||"HEAD"===p.type?x="nocontent":304===e?x="notmodified":(x=_.state,f=_.data,d=!(b=_.error))):(b=x,!e&&x||(x="error",e<0&&(e=0))),S.status=e,S.statusText=(t||x)+"",d?m.resolveWith(h,[f,x,S]):m.rejectWith(h,[S,x,b]),S.statusCode(y),y=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[S,p,d?f:b]),v.fireWith(h,[S,x]),u&&(g.trigger("ajaxComplete",[S,p]),--A.active||A.event.trigger("ajaxStop")))}return S},getJSON:function(e,t,n){return A.get(e,t,n,"json")},getScript:function(e,t){return A.get(e,void 0,t,"script")}}),A.each(["get","post"],function(e,t){A[t]=function(e,n,r,i){return v(n)&&(i=i||r,r=n,n=void 0),A.ajax(A.extend({url:e,type:t,dataType:i,data:n,success:r},A.isPlainObject(e)&&e))}}),A.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),A._evalUrl=function(e,t,n){return A.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){A.globalEval(e,t,n)}})},A.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=A(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return v(e)?this.each(function(t){A(this).wrapInner(e.call(this,t))}):this.each(function(){var t=A(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=v(e);return this.each(function(n){A(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){A(this).replaceWith(this.childNodes)}),this}}),A.expr.pseudos.hidden=function(e){return!A.expr.pseudos.visible(e)},A.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},A.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(e){}};var Jt={0:200,1223:204},Zt=A.ajaxSettings.xhr();m.cors=!!Zt&&"withCredentials"in Zt,m.ajax=Zt=!!Zt,A.ajaxTransport(function(e){var t,n;if(m.cors||Zt&&!e.crossDomain)return{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);t=function(e){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Jt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=a.ontimeout=t("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&r.setTimeout(function(){t&&n()})},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}}),A.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),A.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return A.globalEval(e),e}}}),A.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),A.ajaxTransport("script",function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=A("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}});var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;A.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||A.expando+"_"+Lt.guid++;return this[e]=!0,e}}),A.ajaxPrefilter("json jsonp",function(e,t,n){var i,o,s,a=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(nn,"$1"+i):!1!==e.jsonp&&(e.url+=(jt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||A.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){s=arguments},n.always(function(){void 0===o?A(r).removeProp(i):r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,tn.push(i)),s&&v(o)&&o(s[0]),s=o=void 0}),"script"}),m.createHTMLDocument=((en=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),A.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),o=!n&&[],(i=q.exec(e))?[t.createElement(i[1])]:(i=Pe([e],t,o),o&&o.length&&A(o).remove(),A.merge([],i.childNodes)));var r,i,o},A.fn.load=function(e,t,n){var r,i,o,s=this,a=e.indexOf(" ");return a>-1&&(r=At(e.slice(a)),e=e.slice(0,a)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),s.length>0&&A.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(r?A("<div>").append(A.parseHTML(e)).find(r):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},A.expr.pseudos.animated=function(e){return A.grep(A.timers,function(t){return e===t.elem}).length},A.offset={setOffset:function(e,t,n){var r,i,o,s,a,l,c=A.css(e,"position"),u=A(e),d={};"static"===c&&(e.style.position="relative"),a=u.offset(),o=A.css(e,"top"),l=A.css(e,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(s=(r=u.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(l)||0),v(t)&&(t=t.call(e,n,A.extend({},a))),null!=t.top&&(d.top=t.top-a.top+s),null!=t.left&&(d.left=t.left-a.left+i),"using"in t?t.using.call(e,d):u.css(d)}},A.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){A.offset.setOffset(this,e,t)});var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===A.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===A.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=A(e).offset()).top+=A.css(e,"borderTopWidth",!0),i.left+=A.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-A.css(r,"marginTop",!0),left:t.left-i.left-A.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===A.css(e,"position");)e=e.offsetParent;return e||ge})}}),A.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;A.fn[e]=function(r){return ee(this,function(e,r,i){var o;if(y(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i},e,r,arguments.length)}}),A.each(["top","left"],function(e,t){A.cssHooks[t]=et(m.pixelPosition,function(e,n){if(n)return n=Ze(e,t),Ke.test(n)?A(e).position()[t]+"px":n})}),A.each({Height:"height",Width:"width"},function(e,t){A.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,r){A.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return ee(this,function(t,n,i){var o;return y(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?A.css(t,n,a):A.style(t,n,i,a)},t,s?i:void 0,s)}})}),A.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){A.fn[t]=function(e){return this.on(t,e)}}),A.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),A.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){A.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}});var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;A.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return r=a.call(arguments,2),i=function(){return e.apply(t||this,r.concat(a.call(arguments)))},i.guid=e.guid=e.guid||A.guid++,i},A.holdReady=function(e){e?A.readyWait++:A.ready(!0)},A.isArray=Array.isArray,A.parseJSON=JSON.parse,A.nodeName=C,A.isFunction=v,A.isWindow=y,A.camelCase=ie,A.type=x,A.now=Date.now,A.isNumeric=function(e){var t=A.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},A.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return A}.apply(t,[]))||(e.exports=n);var on=r.jQuery,sn=r.$;return A.noConflict=function(e){return r.$===A&&(r.$=sn),e&&r.jQuery===A&&(r.jQuery=on),A},void 0===i&&(r.jQuery=r.$=A),A})},3985:(e,t,n)=>{"use strict";var r=n(659),i=n(3112),o=n(9601),s=n(4175)("species");e.exports=function(e,t){var n,a=r(e).constructor;return void 0===a||o(n=r(a)[s])?t:i(n)}},4028:(e,t,n)=>{"use strict";var r=n(2128),i=n(6005),o=n(5936);e.exports=function(e,t,n){r?i.f(e,t,o(0,n)):e[t]=n}},4035:(e,t,n)=>{"use strict";var r=n(659),i=n(1151);e.exports=function(e,t,n,o){try{return o?t(r(n)[0],n[1]):t(n)}catch(t){i(e,"throw",t)}}},4049:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},4102:(e,t,n)=>{"use strict";var r=n(321);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},4113:(e,t,n)=>{"use strict";var r=n(2128),i=n(2675),o=n(4451);e.exports=!r&&!i(function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a})},4175:(e,t,n)=>{"use strict";var r=n(8052),i=n(6445),o=n(4461),s=n(2868),a=n(6891),l=n(3316),c=r.Symbol,u=i("wks"),d=l?c.for||c:c&&c.withoutSetter||s;e.exports=function(e){return o(u,e)||(u[e]=a&&o(c,e)?c[e]:d("Symbol."+e)),u[e]}},4267:(e,t,n)=>{"use strict";var r=n(8810),i=n(863);r({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},4334:(e,t,n)=>{"use strict";var r=n(321),i=n(8379),o=TypeError;e.exports=function(e){if(r(e))return e;throw new o(i(e)+" is not a function")}},4451:(e,t,n)=>{"use strict";var r=n(8052),i=n(4102),o=r.document,s=i(o)&&i(o.createElement);e.exports=function(e){return s?o.createElement(e):{}}},4461:(e,t,n)=>{"use strict";var r=n(2484),i=n(8649),o=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return o(i(e),t)}},4492:(e,t,n)=>{"use strict";var r=n(4540),i=Math.floor,o=function(e,t){var n=e.length;if(n<8)for(var s,a,l=1;l<n;){for(a=l,s=e[l];a&&t(e[a-1],s)>0;)e[a]=e[--a];a!==l++&&(e[a]=s)}else for(var c=i(n/2),u=o(r(e,0,c),t),d=o(r(e,c),t),f=u.length,p=d.length,h=0,g=0;h<f||g<p;)e[h+g]=h<f&&g<p?t(u[h],d[g])<=0?u[h++]:d[g++]:h<f?u[h++]:d[g++];return e};e.exports=o},4515:(e,t,n)=>{"use strict";var r=n(9747),i=n(4461),o=n(5672),s=n(6005).f;e.exports=function(e){var t=r.Symbol||(r.Symbol={});i(t,e)||s(t,e,{value:o.f(e)})}},4540:(e,t,n)=>{"use strict";var r=n(2484);e.exports=r([].slice)},4690:(e,t,n)=>{"use strict";var r=n(2128),i=n(4461),o=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,a=i(o,"name"),l=a&&"something"===function(){}.name,c=a&&(!r||r&&s(o,"name").configurable);e.exports={EXISTS:a,PROPER:l,CONFIGURABLE:c}},4700:(e,t,n)=>{"use strict";var r=n(4792),i=n(8563);e.exports=Object.keys||function(e){return r(e,i)}},4792:(e,t,n)=>{"use strict";var r=n(2484),i=n(4461),o=n(8969),s=n(6749).indexOf,a=n(6617),l=r([].push);e.exports=function(e,t){var n,r=o(e),c=0,u=[];for(n in r)!i(a,n)&&i(r,n)&&l(u,n);for(;t.length>c;)i(r,n=t[c++])&&(~s(u,n)||l(u,n));return u}},4802:(e,t,n)=>{"use strict";var r=n(8810),i=n(2128),o=n(9467),s=n(8969),a=n(3071),l=n(4028);r({target:"Object",stat:!0,sham:!i},{getOwnPropertyDescriptors:function(e){for(var t,n,r=s(e),i=a.f,c=o(r),u={},d=0;c.length>d;)void 0!==(n=i(r,t=c[d++]))&&l(u,t,n);return u}})},4834:(e,t,n)=>{"use strict";var r=n(9601),i=TypeError;e.exports=function(e){if(r(e))throw new i("Can't call method on "+e);return e}},4912:e=>{"use strict";e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4929:(e,t,n)=>{"use strict";var r=n(8845);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},4951:(e,t,n)=>{"use strict";var r=n(5719),i=n(9538),o=n(9601),s=n(6609),a=n(4175)("iterator");e.exports=function(e){if(!o(e))return i(e,a)||i(e,"@@iterator")||s[r(e)]}},4956:(e,t,n)=>{"use strict";var r=n(4792),i=n(8563).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},4958:(e,t,n)=>{"use strict";var r=n(8810),i=n(2675),o=n(1948),s=n(4102),a=n(8649),l=n(8770),c=n(3081),u=n(4028),d=n(4929),f=n(2321),p=n(4175),h=n(2763),g=p("isConcatSpreadable"),m=h>=51||!i(function(){var e=[];return e[g]=!1,e.concat()[0]!==e}),v=function(e){if(!s(e))return!1;var t=e[g];return void 0!==t?!!t:o(e)};r({target:"Array",proto:!0,arity:1,forced:!m||!f("concat")},{concat:function(e){var t,n,r,i,o,s=a(this),f=d(s,0),p=0;for(t=-1,r=arguments.length;t<r;t++)if(v(o=-1===t?s:arguments[t]))for(i=l(o),c(p+i),n=0;n<i;n++,p++)n in o&&u(f,p,o[n]);else c(p+1),u(f,p++,o);return f.length=p,f}})},4993:(e,t,n)=>{"use strict";var r=n(8052),i=n(2675),o=r.RegExp,s=!i(function(){var e=!0;try{o(".","d")}catch(t){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",i=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},s={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var a in e&&(s.hasIndices="d"),s)i(a,s[a]);return Object.getOwnPropertyDescriptor(o.prototype,"flags").get.call(t)!==r||n!==r});e.exports={correct:s}},5001:(e,t,n)=>{"use strict";var r=n(2484),i=n(1948),o=n(321),s=n(2748),a=n(7267),l=r([].push);e.exports=function(e){if(o(e))return e;if(i(e)){for(var t=e.length,n=[],r=0;r<t;r++){var c=e[r];"string"==typeof c?l(n,c):"number"!=typeof c&&"Number"!==s(c)&&"String"!==s(c)||l(n,a(c))}var u=n.length,d=!0;return function(e,t){if(d)return d=!1,t;if(i(this))return t;for(var r=0;r<u;r++)if(n[r]===e)return t}}}},5004:e=>{"use strict";e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},5030:(e,t,n)=>{"use strict";n(8810)({target:"Array",stat:!0},{isArray:n(1948)})},5034:(e,t,n)=>{"use strict";var r=n(8052);e.exports=r.Promise},5124:(e,t,n)=>{"use strict";n(2202)},5143:(e,t,n)=>{"use strict";var r=n(8810),i=n(3625),o=n(4334),s=n(1031),a=n(3443),l=n(1072);r({target:"Promise",stat:!0,forced:n(341)},{all:function(e){var t=this,n=s.f(t),r=n.resolve,c=n.reject,u=a(function(){var n=o(t.resolve),s=[],a=0,u=1;l(e,function(e){var o=a++,l=!1;u++,i(n,t,e).then(function(e){l||(l=!0,s[o]=e,--u||r(s))},c)}),--u||r(s)});return u.error&&c(u.value),n.promise}})},5150:(e,t,n)=>{"use strict";var r=n(2484),i=n(4834),o=n(7267),s=n(4912),a=r("".replace),l=RegExp("^["+s+"]+"),c=RegExp("(^|[^"+s+"])["+s+"]+$"),u=function(e){return function(t){var n=o(i(t));return 1&e&&(n=a(n,l,"")),2&e&&(n=a(n,c,"$1")),n}};e.exports={start:u(1),end:u(2),trim:u(3)}},5236:(e,t,n)=>{"use strict";var r=n(321),i=n(6005),o=n(9455),s=n(1941);e.exports=function(e,t,n,a){a||(a={});var l=a.enumerable,c=void 0!==a.name?a.name:t;if(r(n)&&o(n,c,a),a.global)l?e[t]=n:s(t,n);else{try{a.unsafe?e[t]&&(l=!0):delete e[t]}catch(e){}l?e[t]=n:i.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},5249:(e,t,n)=>{"use strict";var r=n(8810),i=n(9311);r({target:"Array",proto:!0,forced:[].forEach!==i},{forEach:i})},5424:(e,t,n)=>{"use strict";var r=n(8810),i=n(6007),o=n(2128),s=n(8052),a=n(9747),l=n(2484),c=n(2360),u=n(4461),d=n(9859),f=n(7837),p=n(3401),h=n(573),g=n(2675),m=n(4956).f,v=n(3071).f,y=n(6005).f,b=n(7156),_=n(5150).trim,w="Number",x=s[w],S=a[w],O=x.prototype,A=s.TypeError,E=l("".slice),C=l("".charCodeAt),T=function(e){var t,n,r,i,o,s,a,l,c=h(e,"number");if(p(c))throw new A("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=_(c),43===(t=C(c,0))||45===t){if(88===(n=C(c,2))||120===n)return NaN}else if(48===t){switch(C(c,1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+c}for(s=(o=E(c,2)).length,a=0;a<s;a++)if((l=C(o,a))<48||l>i)return NaN;return parseInt(o,r)}return+c},k=c(w,!x(" 0o1")||!x("0b1")||x("+0x1")),L=function(e){var t,n=arguments.length<1?0:x(function(e){var t=h(e,"number");return"bigint"==typeof t?t:T(t)}(e));return f(O,t=this)&&g(function(){b(t)})?d(Object(n),this,L):n};L.prototype=O,k&&!i&&(O.constructor=L),r({global:!0,constructor:!0,wrap:!0,forced:k},{Number:L});var j=function(e,t){for(var n,r=o?m(t):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),i=0;r.length>i;i++)u(t,n=r[i])&&!u(e,n)&&y(e,n,v(t,n))};i&&S&&j(a[w],S),(k||i)&&j(a[w],x)},5433:(e,t,n)=>{"use strict";var r=n(2128),i=n(2484),o=n(3625),s=n(2675),a=n(4700),l=n(9073),c=n(7769),u=n(8649),d=n(51),f=Object.assign,p=Object.defineProperty,h=i([].concat);e.exports=!f||s(function(){if(r&&1!==f({b:1},f(p({},"a",{enumerable:!0,get:function(){p(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol("assign detection"),i="abcdefghijklmnopqrst";return e[n]=7,i.split("").forEach(function(e){t[e]=e}),7!==f({},e)[n]||a(f({},t)).join("")!==i})?function(e,t){for(var n=u(e),i=arguments.length,s=1,f=l.f,p=c.f;i>s;)for(var g,m=d(arguments[s++]),v=f?h(a(m),f(m)):a(m),y=v.length,b=0;y>b;)g=v[b++],r&&!o(p,m,g)||(n[g]=m[g]);return n}:f},5514:(e,t,n)=>{"use strict";var r=n(3625),i=n(321),o=n(4102),s=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&i(n=e.toString)&&!o(a=r(n,e)))return a;if(i(n=e.valueOf)&&!o(a=r(n,e)))return a;if("string"!==t&&i(n=e.toString)&&!o(a=r(n,e)))return a;throw new s("Can't convert object to primitive value")}},5549:(e,t,n)=>{"use strict";var r=n(8810),i=n(2961).find,o=n(2153),s="find",a=!0;s in[]&&Array(1)[s](function(){a=!1}),r({target:"Array",proto:!0,forced:a},{find:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),o(s)},5672:(e,t,n)=>{"use strict";var r=n(4175);t.f=r},5719:(e,t,n)=>{"use strict";var r=n(7928),i=n(321),o=n(2748),s=n(4175)("toStringTag"),a=Object,l="Arguments"===o(function(){return arguments}());e.exports=r?o:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=a(e),s))?n:l?o(t):"Object"===(r=o(t))&&i(t.callee)?"Arguments":r}},5722:(e,t,n)=>{"use strict";var r=n(2675),i=n(8052).RegExp;e.exports=r(function(){var e=i("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})},5755:(e,t,n)=>{"use strict";var r=n(7837),i=TypeError;e.exports=function(e,t){if(r(t,e))return e;throw new i("Incorrect invocation")}},5927:(e,t,n)=>{"use strict";var r=n(4461),i=n(321),o=n(8649),s=n(3779),a=n(2407),l=s("IE_PROTO"),c=Object,u=c.prototype;e.exports=a?c.getPrototypeOf:function(e){var t=o(e);if(r(t,l))return t[l];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof c?u:null}},5936:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},5940:(e,t,n)=>{"use strict";var r=n(2675),i=n(4175),o=n(2128),s=n(6007),a=i("iterator");e.exports=!r(function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),r="";return e.pathname="c%20d",t.forEach(function(e,n){t.delete("b"),r+=n+e}),n.delete("a",2),n.delete("b",void 0),s&&(!e.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b"))||!t.size&&(s||!o)||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[a]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==r||"x"!==new URL("https://x",void 0).host})},6005:(e,t,n)=>{"use strict";var r=n(2128),i=n(4113),o=n(706),s=n(659),a=n(1261),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",f="configurable",p="writable";t.f=r?o?function(e,t,n){if(s(e),t=a(t),s(n),"function"==typeof e&&"prototype"===t&&"value"in n&&p in n&&!n[p]){var r=u(e,t);r&&r[p]&&(e[t]=n.value,n={configurable:f in n?n[f]:r[f],enumerable:d in n?n[d]:r[d],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(s(e),t=a(t),s(n),i)try{return c(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new l("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},6007:e=>{"use strict";e.exports=!1},6038:(e,t,n)=>{"use strict";var r=n(9455),i=n(6005);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),i.f(e,t,n)}},6057:(e,t,n)=>{"use strict";var r=n(2484),i=n(4334);e.exports=function(e,t,n){try{return r(i(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(e){}}},6125:(e,t,n)=>{"use strict";var r=n(6627);e.exports="NODE"===r},6221:(e,t,n)=>{"use strict";var r=n(3625),i=n(4334),o=n(659),s=n(8379),a=n(4951),l=TypeError;e.exports=function(e,t){var n=arguments.length<2?a(e):t;if(i(n))return o(r(n,e));throw new l(s(e)+" is not iterable")}},6315:(e,t,n)=>{"use strict";var r=n(8810),i=n(2128),o=n(8052),s=n(2484),a=n(4461),l=n(321),c=n(7837),u=n(7267),d=n(6038),f=n(1704),p=o.Symbol,h=p&&p.prototype;if(i&&l(p)&&(!("description"in h)||void 0!==p().description)){var g={},m=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:u(arguments[0]),t=c(h,this)?new p(e):void 0===e?p():p(e);return""===e&&(g[t]=!0),t};f(m,p),m.prototype=h,h.constructor=m;var v="Symbol(description detection)"===String(p("description detection")),y=s(h.valueOf),b=s(h.toString),_=/^Symbol\((.*)\)[^)]+$/,w=s("".replace),x=s("".slice);d(h,"description",{configurable:!0,get:function(){var e=y(this);if(a(g,e))return"";var t=b(e),n=v?x(t,7,-1):w(t,_,"$1");return""===n?void 0:n}}),r({global:!0,constructor:!0,forced:!0},{Symbol:m})}},6335:(e,t,n)=>{"use strict";var r=n(8810),i=n(8948);r({global:!0,forced:parseFloat!==i},{parseFloat:i})},6369:(e,t,n)=>{"use strict";var r,i,o,s=n(8434),a=n(8052),l=n(4102),c=n(671),u=n(4461),d=n(2921),f=n(3779),p=n(6617),h="Object already initialized",g=a.TypeError,m=a.WeakMap;if(s||d.state){var v=d.state||(d.state=new m);v.get=v.get,v.has=v.has,v.set=v.set,r=function(e,t){if(v.has(e))throw new g(h);return t.facade=e,v.set(e,t),t},i=function(e){return v.get(e)||{}},o=function(e){return v.has(e)}}else{var y=f("state");p[y]=!0,r=function(e,t){if(u(e,y))throw new g(h);return t.facade=e,c(e,y,t),t},i=function(e){return u(e,y)?e[y]:{}},o=function(e){return u(e,y)}}e.exports={set:r,get:i,has:o,enforce:function(e){return o(e)?i(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!l(t)||(n=i(t)).type!==e)throw new g("Incompatible receiver, "+e+" required");return n}}}},6445:(e,t,n)=>{"use strict";var r=n(2921);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},6461:(e,t,n)=>{"use strict";var r=n(8810),i=n(2128),o=n(6005).f;r({target:"Object",stat:!0,forced:Object.defineProperty!==o,sham:!i},{defineProperty:o})},6526:(e,t,n)=>{"use strict";var r=n(7391),i=Math.max,o=Math.min;e.exports=function(e,t){var n=r(e);return n<0?i(n+t,0):o(n,t)}},6558:(e,t,n)=>{"use strict";n(8808),n(5143),n(383),n(2035),n(2173),n(2148)},6559:(e,t,n)=>{"use strict";var r,i,o,s,a,l=n(8052),c=n(6977),u=n(9004),d=n(6781).set,f=n(1661),p=n(2900),h=n(7517),g=n(552),m=n(6125),v=l.MutationObserver||l.WebKitMutationObserver,y=l.document,b=l.process,_=l.Promise,w=c("queueMicrotask");if(!w){var x=new f,S=function(){var e,t;for(m&&(e=b.domain)&&e.exit();t=x.get();)try{t()}catch(e){throw x.head&&r(),e}e&&e.enter()};p||m||g||!v||!y?!h&&_&&_.resolve?((s=_.resolve(void 0)).constructor=_,a=u(s.then,s),r=function(){a(S)}):m?r=function(){b.nextTick(S)}:(d=u(d,l),r=function(){d(S)}):(i=!0,o=y.createTextNode(""),new v(S).observe(o,{characterData:!0}),r=function(){o.data=i=!i}),w=function(e){x.head||r(),x.add(e)}}e.exports=w},6609:e=>{"use strict";e.exports={}},6617:e=>{"use strict";e.exports={}},6627:(e,t,n)=>{"use strict";var r=n(8052),i=n(3291),o=n(2748),s=function(e){return i.slice(0,e.length)===e};e.exports=s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===o(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"},6749:(e,t,n)=>{"use strict";var r=n(8969),i=n(6526),o=n(8770),s=function(e){return function(t,n,s){var a=r(t),l=o(a);if(0===l)return!e&&-1;var c,u=i(s,l);if(e&&n!=n){for(;l>u;)if((c=a[u++])!=c)return!0}else for(;l>u;u++)if((e||u in a)&&a[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:s(!0),indexOf:s(!1)}},6766:(e,t,n)=>{"use strict";var r=n(2748),i=n(8969),o=n(4956).f,s=n(4540),a="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return a&&"Window"===r(e)?function(e){try{return o(e)}catch(e){return s(a)}}(e):o(i(e))}},6781:(e,t,n)=>{"use strict";var r,i,o,s,a=n(8052),l=n(133),c=n(9004),u=n(321),d=n(4461),f=n(2675),p=n(7073),h=n(4540),g=n(4451),m=n(464),v=n(2900),y=n(6125),b=a.setImmediate,_=a.clearImmediate,w=a.process,x=a.Dispatch,S=a.Function,O=a.MessageChannel,A=a.String,E=0,C={},T="onreadystatechange";f(function(){r=a.location});var k=function(e){if(d(C,e)){var t=C[e];delete C[e],t()}},L=function(e){return function(){k(e)}},j=function(e){k(e.data)},P=function(e){a.postMessage(A(e),r.protocol+"//"+r.host)};b&&_||(b=function(e){m(arguments.length,1);var t=u(e)?e:S(e),n=h(arguments,1);return C[++E]=function(){l(t,void 0,n)},i(E),E},_=function(e){delete C[e]},y?i=function(e){w.nextTick(L(e))}:x&&x.now?i=function(e){x.now(L(e))}:O&&!v?(s=(o=new O).port2,o.port1.onmessage=j,i=c(s.postMessage,s)):a.addEventListener&&u(a.postMessage)&&!a.importScripts&&r&&"file:"!==r.protocol&&!f(P)?(i=P,a.addEventListener("message",j,!1)):i=T in g("script")?function(e){p.appendChild(g("script"))[T]=function(){p.removeChild(this),k(e)}}:function(e){setTimeout(L(e),0)}),e.exports={set:b,clear:_}},6794:(e,t,n)=>{"use strict";var r=n(659),i=n(4102),o=n(1031);e.exports=function(e,t){if(r(e),i(t)&&t.constructor===e)return t;var n=o.f(e);return(0,n.resolve)(t),n.promise}},6805:(e,t,n)=>{"use strict";var r=n(8810),i=n(2128),o=n(3037).f;r({target:"Object",stat:!0,forced:Object.defineProperties!==o,sham:!i},{defineProperties:o})},6871:(e,t,n)=>{"use strict";var r=n(7928),i=n(5719);e.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},6891:(e,t,n)=>{"use strict";var r=n(2763),i=n(2675),o=n(8052).String;e.exports=!!Object.getOwnPropertySymbols&&!i(function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41})},6977:(e,t,n)=>{"use strict";var r=n(8052),i=n(2128),o=Object.getOwnPropertyDescriptor;e.exports=function(e){if(!i)return r[e];var t=o(r,e);return t&&t.value}},6985:(e,t,n)=>{"use strict";var r=n(8810),i=n(6891),o=n(2675),s=n(9073),a=n(8649);r({target:"Object",stat:!0,forced:!i||o(function(){s.f(1)})},{getOwnPropertySymbols:function(e){var t=s.f;return t?t(a(e)):[]}})},7032:(e,t,n)=>{"use strict";var r=n(8052),i=n(5034),o=n(321),s=n(2360),a=n(2718),l=n(4175),c=n(6627),u=n(6007),d=n(2763),f=i&&i.prototype,p=l("species"),h=!1,g=o(r.PromiseRejectionEvent),m=s("Promise",function(){var e=a(i),t=e!==String(i);if(!t&&66===d)return!0;if(u&&(!f.catch||!f.finally))return!0;if(!d||d<51||!/native code/.test(e)){var n=new i(function(e){e(1)}),r=function(e){e(function(){},function(){})};if((n.constructor={})[p]=r,!(h=n.then(function(){})instanceof r))return!0}return!(t||"BROWSER"!==c&&"DENO"!==c||g)});e.exports={CONSTRUCTOR:m,REJECTION_EVENT:g,SUBCLASSING:h}},7073:(e,t,n)=>{"use strict";var r=n(3163);e.exports=r("document","documentElement")},7140:(e,t,n)=>{"use strict";var r=n(4451)("span").classList,i=r&&r.constructor&&r.constructor.prototype;e.exports=i===Object.prototype?void 0:i},7156:(e,t,n)=>{"use strict";var r=n(2484);e.exports=r(1.1.valueOf)},7200:(e,t,n)=>{"use strict";var r=n(4461),i=n(5236),o=n(1652),s=n(4175)("toPrimitive"),a=Date.prototype;r(a,s)||i(a,s,o)},7267:(e,t,n)=>{"use strict";var r=n(5719),i=String;e.exports=function(e){if("Symbol"===r(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},7270:(e,t,n)=>{"use strict";var r=n(3597).IteratorPrototype,i=n(3844),o=n(5936),s=n(8819),a=n(6609),l=function(){return this};e.exports=function(e,t,n,c){var u=t+" Iterator";return e.prototype=i(r,{next:o(+!c,n)}),s(e,u,!1,!0),a[u]=l,e}},7372:(e,t,n)=>{"use strict";var r=n(8810),i=n(2961).filter;r({target:"Array",proto:!0,forced:!n(2321)("filter")},{filter:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},7391:(e,t,n)=>{"use strict";var r=n(4049);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},7476:(e,t,n)=>{"use strict";var r=n(8810),i=n(8649),o=n(4700);r({target:"Object",stat:!0,forced:n(2675)(function(){o(1)})},{keys:function(e){return o(i(e))}})},7517:(e,t,n)=>{"use strict";var r=n(3291);e.exports=/ipad|iphone|ipod/i.test(r)&&"undefined"!=typeof Pebble},7560:(e,t,n)=>{"use strict";var r=n(1955).charAt,i=n(7267),o=n(6369),s=n(8676),a=n(381),l="String Iterator",c=o.set,u=o.getterFor(l);s(String,"String",function(e){c(this,{type:l,string:i(e),index:0})},function(){var e,t=u(this),n=t.string,i=t.index;return i>=n.length?a(void 0,!0):(e=r(n,i),t.index+=e.length,a(e,!1))})},7575:(e,t,n)=>{"use strict";n(3765),n(930),n(9200),n(7754),n(6985)},7628:(e,t,n)=>{"use strict";n(2690)},7754:(e,t,n)=>{"use strict";var r=n(8810),i=n(3163),o=n(133),s=n(3625),a=n(2484),l=n(2675),c=n(321),u=n(3401),d=n(4540),f=n(5001),p=n(6891),h=String,g=i("JSON","stringify"),m=a(/./.exec),v=a("".charAt),y=a("".charCodeAt),b=a("".replace),_=a(1.1.toString),w=/[\uD800-\uDFFF]/g,x=/^[\uD800-\uDBFF]$/,S=/^[\uDC00-\uDFFF]$/,O=!p||l(function(){var e=i("Symbol")("stringify detection");return"[null]"!==g([e])||"{}"!==g({a:e})||"{}"!==g(Object(e))}),A=l(function(){return'"\\udf06\\ud834"'!==g("\udf06\ud834")||'"\\udead"'!==g("\udead")}),E=function(e,t){var n=d(arguments),r=f(t);if(c(r)||void 0!==e&&!u(e))return n[1]=function(e,t){if(c(r)&&(t=s(r,this,h(e),t)),!u(t))return t},o(g,null,n)},C=function(e,t,n){var r=v(n,t-1),i=v(n,t+1);return m(x,e)&&!m(S,i)||m(S,e)&&!m(x,r)?"\\u"+_(y(e,0),16):e};g&&r({target:"JSON",stat:!0,arity:3,forced:O||A},{stringify:function(e,t,n){var r=d(arguments),i=o(O?E:g,null,r);return A&&"string"==typeof i?b(i,w,C):i}})},7769:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);t.f=i?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},7808:(e,t,n)=>{"use strict";n(4267);var r=n(3625),i=n(5236),o=n(863),s=n(2675),a=n(4175),l=n(671),c=a("species"),u=RegExp.prototype;e.exports=function(e,t,n,d){var f=a(e),p=!s(function(){var t={};return t[f]=function(){return 7},7!==""[e](t)}),h=p&&!s(function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[c]=function(){return n},n.flags="",n[f]=/./[f]),n.exec=function(){return t=!0,null},n[f](""),!t});if(!p||!h||n){var g=/./[f],m=t(f,""[e],function(e,t,n,i,s){var a=t.exec;return a===o||a===u.exec?p&&!s?{done:!0,value:r(g,t,n,i)}:{done:!0,value:r(e,n,t,i)}:{done:!1}});i(String.prototype,e,m[0]),i(u,f,m[1])}d&&l(u[f],"sham",!0)}},7837:(e,t,n)=>{"use strict";var r=n(2484);e.exports=r({}.isPrototypeOf)},7928:(e,t,n)=>{"use strict";var r={};r[n(4175)("toStringTag")]="z",e.exports="[object z]"===String(r)},8052:function(e,t,n){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},8135:(e,t,n)=>{"use strict";var r=n(8810),i=n(2675),o=n(8649),s=n(573);r({target:"Date",proto:!0,arity:1,forced:i(function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})})},{toJSON:function(e){var t=o(this),n=s(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},8303:(e,t,n)=>{"use strict";var r=n(659);e.exports=function(){var e=r(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},8379:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},8434:(e,t,n)=>{"use strict";var r=n(8052),i=n(321),o=r.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},8438:(e,t,n)=>{"use strict";var r=n(3625),i=n(3163),o=n(4175),s=n(5236);e.exports=function(){var e=i("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,a=o("toPrimitive");t&&!t[a]&&s(t,a,function(e){return r(n,this)},{arity:1})}},8563:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8649:(e,t,n)=>{"use strict";var r=n(4834),i=Object;e.exports=function(e){return i(r(e))}},8676:(e,t,n)=>{"use strict";var r=n(8810),i=n(3625),o=n(6007),s=n(4690),a=n(321),l=n(7270),c=n(5927),u=n(1715),d=n(8819),f=n(671),p=n(5236),h=n(4175),g=n(6609),m=n(3597),v=s.PROPER,y=s.CONFIGURABLE,b=m.IteratorPrototype,_=m.BUGGY_SAFARI_ITERATORS,w=h("iterator"),x="keys",S="values",O="entries",A=function(){return this};e.exports=function(e,t,n,s,h,m,E){l(n,t,s);var C,T,k,L=function(e){if(e===h&&N)return N;if(!_&&e&&e in I)return I[e];switch(e){case x:case S:case O:return function(){return new n(this,e)}}return function(){return new n(this)}},j=t+" Iterator",P=!1,I=e.prototype,D=I[w]||I["@@iterator"]||h&&I[h],N=!_&&D||L(h),F="Array"===t&&I.entries||D;if(F&&(C=c(F.call(new e)))!==Object.prototype&&C.next&&(o||c(C)===b||(u?u(C,b):a(C[w])||p(C,w,A)),d(C,j,!0,!0),o&&(g[j]=A)),v&&h===S&&D&&D.name!==S&&(!o&&y?f(I,"name",S):(P=!0,N=function(){return i(D,this)})),h)if(T={values:L(S),keys:m?N:L(x),entries:L(O)},E)for(k in T)(_||P||!(k in I))&&p(I,k,T[k]);else r({target:t,proto:!0,forced:_||P},T);return o&&!E||I[w]===N||p(I,w,N,{name:h}),g[t]=N,T}},8754:(e,t,n)=>{"use strict";var r=n(2675);e.exports=function(e,t){var n=[][e];return!!n&&r(function(){n.call(null,t||function(){return 1},1)})}},8770:(e,t,n)=>{"use strict";var r=n(3026);e.exports=function(e){return r(e.length)}},8785:(e,t,n)=>{"use strict";var r=n(1955).charAt;e.exports=function(e,t,n){return t+(n?r(e,t).length:1)}},8801:(e,t,n)=>{"use strict";var r=n(2128),i=n(2675),o=n(2484),s=n(5927),a=n(4700),l=n(8969),c=o(n(7769).f),u=o([].push),d=r&&i(function(){var e=Object.create(null);return e[2]=2,!c(e,2)}),f=function(e){return function(t){for(var n,i=l(t),o=a(i),f=d&&null===s(i),p=o.length,h=0,g=[];p>h;)n=o[h++],r&&!(f?n in i:c(i,n))||u(g,e?[n,i[n]]:i[n]);return g}};e.exports={entries:f(!0),values:f(!1)}},8808:(e,t,n)=>{"use strict";var r,i,o,s,a=n(8810),l=n(6007),c=n(6125),u=n(8052),d=n(9747),f=n(3625),p=n(5236),h=n(1715),g=n(8819),m=n(461),v=n(4334),y=n(321),b=n(4102),_=n(5755),w=n(3985),x=n(6781).set,S=n(6559),O=n(1929),A=n(3443),E=n(1661),C=n(6369),T=n(5034),k=n(7032),L=n(1031),j="Promise",P=k.CONSTRUCTOR,I=k.REJECTION_EVENT,D=k.SUBCLASSING,N=C.getterFor(j),F=C.set,R=T&&T.prototype,M=T,$=R,q=u.TypeError,H=u.document,B=u.process,U=L.f,W=U,z=!!(H&&H.createEvent&&u.dispatchEvent),V="unhandledrejection",G=function(e){var t;return!(!b(e)||!y(t=e.then))&&t},K=function(e,t){var n,r,i,o=t.value,s=1===t.state,a=s?e.ok:e.fail,l=e.resolve,c=e.reject,u=e.domain;try{a?(s||(2===t.rejection&&Z(t),t.rejection=1),!0===a?n=o:(u&&u.enter(),n=a(o),u&&(u.exit(),i=!0)),n===e.promise?c(new q("Promise-chain cycle")):(r=G(n))?f(r,n,l,c):l(n)):c(o)}catch(e){u&&!i&&u.exit(),c(e)}},X=function(e,t){e.notified||(e.notified=!0,S(function(){for(var n,r=e.reactions;n=r.get();)K(n,e);e.notified=!1,t&&!e.rejection&&Y(e)}))},Q=function(e,t,n){var r,i;z?((r=H.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),u.dispatchEvent(r)):r={promise:t,reason:n},!I&&(i=u["on"+e])?i(r):e===V&&O("Unhandled promise rejection",n)},Y=function(e){f(x,u,function(){var t,n=e.facade,r=e.value;if(J(e)&&(t=A(function(){c?B.emit("unhandledRejection",r,n):Q(V,n,r)}),e.rejection=c||J(e)?2:1,t.error))throw t.value})},J=function(e){return 1!==e.rejection&&!e.parent},Z=function(e){f(x,u,function(){var t=e.facade;c?B.emit("rejectionHandled",t):Q("rejectionhandled",t,e.value)})},ee=function(e,t,n){return function(r){e(t,r,n)}},te=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,X(e,!0))},ne=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw new q("Promise can't be resolved itself");var r=G(t);r?S(function(){var n={done:!1};try{f(r,t,ee(ne,n,e),ee(te,n,e))}catch(t){te(n,t,e)}}):(e.value=t,e.state=1,X(e,!1))}catch(t){te({done:!1},t,e)}}};if(P&&($=(M=function(e){_(this,$),v(e),f(r,this);var t=N(this);try{e(ee(ne,t),ee(te,t))}catch(e){te(t,e)}}).prototype,(r=function(e){F(this,{type:j,done:!1,notified:!1,parent:!1,reactions:new E,rejection:!1,state:0,value:null})}).prototype=p($,"then",function(e,t){var n=N(this),r=U(w(this,M));return n.parent=!0,r.ok=!y(e)||e,r.fail=y(t)&&t,r.domain=c?B.domain:void 0,0===n.state?n.reactions.add(r):S(function(){K(r,n)}),r.promise}),i=function(){var e=new r,t=N(e);this.promise=e,this.resolve=ee(ne,t),this.reject=ee(te,t)},L.f=U=function(e){return e===M||e===o?new i(e):W(e)},!l&&y(T)&&R!==Object.prototype)){s=R.then,D||p(R,"then",function(e,t){var n=this;return new M(function(e,t){f(s,n,e,t)}).then(e,t)},{unsafe:!0});try{delete R.constructor}catch(e){}h&&h(R,$)}a({global:!0,constructor:!0,wrap:!0,forced:P},{Promise:M}),o=d.Promise,g(M,j,!1,!0),m(j)},8810:(e,t,n)=>{"use strict";var r=n(8052),i=n(3071).f,o=n(671),s=n(5236),a=n(1941),l=n(1704),c=n(2360);e.exports=function(e,t){var n,u,d,f,p,h=e.target,g=e.global,m=e.stat;if(n=g?r:m?r[h]||a(h,{}):r[h]&&r[h].prototype)for(u in t){if(f=t[u],d=e.dontCallGetSet?(p=i(n,u))&&p.value:n[u],!c(g?u:h+(m?".":"#")+u,e.forced)&&void 0!==d){if(typeof f==typeof d)continue;l(f,d)}(e.sham||d&&d.sham)&&o(f,"sham",!0),s(n,u,f,e)}}},8819:(e,t,n)=>{"use strict";var r=n(6005).f,i=n(4461),o=n(4175)("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!i(e,o)&&r(e,o,{configurable:!0,value:t})}},8832:(e,t,n)=>{"use strict";var r=n(4515),i=n(8438);r("toPrimitive"),i()},8845:(e,t,n)=>{"use strict";var r=n(1948),i=n(1441),o=n(4102),s=n(4175)("species"),a=Array;e.exports=function(e){var t;return r(e)&&(t=e.constructor,(i(t)&&(t===a||r(t.prototype))||o(t)&&null===(t=t[s]))&&(t=void 0)),void 0===t?a:t}},8948:(e,t,n)=>{"use strict";var r=n(8052),i=n(2675),o=n(2484),s=n(7267),a=n(5150).trim,l=n(4912),c=o("".charAt),u=r.parseFloat,d=r.Symbol,f=d&&d.iterator,p=1/u(l+"-0")!=-1/0||f&&!i(function(){u(Object(f))});e.exports=p?function(e){var t=a(s(e)),n=u(t);return 0===n&&"-"===c(t,0)?-0:n}:u},8969:(e,t,n)=>{"use strict";var r=n(51),i=n(4834);e.exports=function(e){return r(i(e))}},9004:(e,t,n)=>{"use strict";var r=n(1904),i=n(4334),o=n(3588),s=r(r.bind);e.exports=function(e,t){return i(e),void 0===t?e:o?s(e,t):function(){return e.apply(t,arguments)}}},9028:(e,t,n)=>{"use strict";var r=n(133),i=n(3625),o=n(2484),s=n(7808),a=n(2675),l=n(659),c=n(321),u=n(4102),d=n(7391),f=n(3026),p=n(7267),h=n(4834),g=n(8785),m=n(9538),v=n(1650),y=n(3614),b=n(9198),_=n(4175)("replace"),w=Math.max,x=Math.min,S=o([].concat),O=o([].push),A=o("".indexOf),E=o("".slice),C=function(e){return void 0===e?e:String(e)},T="$0"==="a".replace(/./,"$0"),k=!!/./[_]&&""===/./[_]("a","$0");s("replace",function(e,t,n){var o=k?"$":"$0";return[function(e,n){var r=h(this),o=u(e)?m(e,_):void 0;return o?i(o,e,r,n):i(t,p(r),e,n)},function(e,i){var s=l(this),a=p(e);if("string"==typeof i&&-1===A(i,o)&&-1===A(i,"$<")){var u=n(t,s,a,i);if(u.done)return u.value}var h=c(i);h||(i=p(i));var m,_=p(y(s)),T=-1!==A(_,"g");T&&(m=-1!==A(_,"u"),s.lastIndex=0);for(var k,L=[];null!==(k=b(s,a))&&(O(L,k),T);){""===p(k[0])&&(s.lastIndex=g(a,f(s.lastIndex),m))}for(var j="",P=0,I=0;I<L.length;I++){for(var D,N=p((k=L[I])[0]),F=w(x(d(k.index),a.length),0),R=[],M=1;M<k.length;M++)O(R,C(k[M]));var $=k.groups;if(h){var q=S([N],R,F,a);void 0!==$&&O(q,$),D=p(r(i,void 0,q))}else D=v(N,a,F,R,$,i);F>=P&&(j+=E(a,P,F)+D,P=F+N.length)}return j+E(a,P)}]},!!a(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})||!T||k)},9073:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},9111:(e,t,n)=>{"use strict";var r=n(8810),i=n(2675),o=n(8969),s=n(3071).f,a=n(2128);r({target:"Object",stat:!0,forced:!a||i(function(){s(1)}),sham:!a},{getOwnPropertyDescriptor:function(e,t){return s(o(e),t)}})},9198:(e,t,n)=>{"use strict";var r=n(3625),i=n(659),o=n(321),s=n(2748),a=n(863),l=TypeError;e.exports=function(e,t){var n=e.exec;if(o(n)){var c=r(n,e,t);return null!==c&&i(c),c}if("RegExp"===s(e))return r(a,e,t);throw new l("RegExp#exec called on incompatible receiver")}},9200:(e,t,n)=>{"use strict";var r=n(8810),i=n(4461),o=n(3401),s=n(8379),a=n(6445),l=n(92),c=a("symbol-to-string-registry");r({target:"Symbol",stat:!0,forced:!l},{keyFor:function(e){if(!o(e))throw new TypeError(s(e)+" is not a symbol");if(i(c,e))return c[e]}})},9311:(e,t,n)=>{"use strict";var r=n(2961).forEach,i=n(8754)("forEach");e.exports=i?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},9455:(e,t,n)=>{"use strict";var r=n(2484),i=n(2675),o=n(321),s=n(4461),a=n(2128),l=n(4690).CONFIGURABLE,c=n(2718),u=n(6369),d=u.enforce,f=u.get,p=String,h=Object.defineProperty,g=r("".slice),m=r("".replace),v=r([].join),y=a&&!i(function(){return 8!==h(function(){},"length",{value:8}).length}),b=String(String).split("String"),_=e.exports=function(e,t,n){"Symbol("===g(p(t),0,7)&&(t="["+m(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!s(e,"name")||l&&e.name!==t)&&(a?h(e,"name",{value:t,configurable:!0}):e.name=t),y&&n&&s(n,"arity")&&e.length!==n.arity&&h(e,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=d(e);return s(r,"source")||(r.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=_(function(){return o(this)&&f(this).source||c(this)},"toString")},9467:(e,t,n)=>{"use strict";var r=n(3163),i=n(2484),o=n(4956),s=n(9073),a=n(659),l=i([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=o.f(a(e)),n=s.f;return n?l(t,n(e)):t}},9538:(e,t,n)=>{"use strict";var r=n(4334),i=n(9601);e.exports=function(e,t){var n=e[t];return i(n)?void 0:r(n)}},9601:e=>{"use strict";e.exports=function(e){return null==e}},9740:(e,t,n)=>{"use strict";var r=n(8810),i=n(3625);r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return i(URL.prototype.toString,this)}})},9747:(e,t,n)=>{"use strict";var r=n(8052);e.exports=r},9852:(e,t,n)=>{"use strict";n(2029)},9859:(e,t,n)=>{"use strict";var r=n(321),i=n(4102),o=n(1715);e.exports=function(e,t,n){var s,a;return o&&r(s=t.constructor)&&s!==n&&i(a=s.prototype)&&a!==n.prototype&&o(e,a),e}},9872:(e,t,n)=>{"use strict";var r=n(8810),i=n(1904),o=n(6749).indexOf,s=n(8754),a=i([].indexOf),l=!!a&&1/a([1],1,-0)<0;r({target:"Array",proto:!0,forced:l||!s("indexOf")},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return l?a(this,e,t)||0:o(this,e,t)}})},9892:(e,t,n)=>{"use strict";var r=n(2484),i=n(5236),o=Date.prototype,s="Invalid Date",a="toString",l=r(o[a]),c=r(o.getTime);String(new Date(NaN))!==s&&i(o,a,function(){var e=c(this);return e==e?l(this):s})},9902:(e,t,n)=>{"use strict";var r=n(8810),i=n(8801).entries;r({target:"Object",stat:!0},{entries:function(e){return i(e)}})},9976:(e,t,n)=>{"use strict";var r=n(4175)("iterator"),i=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){i=!0}};s[r]=function(){return this},Array.from(s,function(){throw 2})}catch(e){}e.exports=function(e,t){try{if(!t&&!i)return!1}catch(e){return!1}var n=!1;try{var o={};o[r]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(e){}return n}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{afterMain:()=>O,afterRead:()=>w,afterWrite:()=>C,applyStyles:()=>D,arrow:()=>ee,auto:()=>c,basePlacements:()=>u,beforeMain:()=>x,beforeRead:()=>b,beforeWrite:()=>A,bottom:()=>s,clippingParents:()=>p,computeStyles:()=>ie,createPopper:()=>De,createPopperBase:()=>Ie,createPopperLite:()=>Ne,detectOverflow:()=>_e,end:()=>f,eventListeners:()=>se,flip:()=>we,hide:()=>Oe,left:()=>l,main:()=>S,modifierPhases:()=>T,offset:()=>Ae,placements:()=>y,popper:()=>g,popperGenerator:()=>Pe,popperOffsets:()=>Ee,preventOverflow:()=>Ce,read:()=>_,reference:()=>m,right:()=>a,start:()=>d,top:()=>o,variationPlacements:()=>v,viewport:()=>h,write:()=>E});var t={};n.r(t),n.d(t,{Alert:()=>jt,Button:()=>It,Carousel:()=>hn,Collapse:()=>Tn,Dropdown:()=>Zn,Modal:()=>Dr,Offcanvas:()=>Jr,Popover:()=>Oi,ScrollSpy:()=>Ni,Tab:()=>io,Toast:()=>_o,Tooltip:()=>bi});n(7575),n(6315),n(391),n(5249),n(2614),n(5030),n(3348),n(562),n(3082),n(9892),n(2558),n(1759),n(4267),n(3049),n(7560),n(9852),n(3544),n(2901);var r=n(3886),i=n.n(r),o="top",s="bottom",a="right",l="left",c="auto",u=[o,s,a,l],d="start",f="end",p="clippingParents",h="viewport",g="popper",m="reference",v=u.reduce(function(e,t){return e.concat([t+"-"+d,t+"-"+f])},[]),y=[].concat(u,[c]).reduce(function(e,t){return e.concat([t,t+"-"+d,t+"-"+f])},[]),b="beforeRead",_="read",w="afterRead",x="beforeMain",S="main",O="afterMain",A="beforeWrite",E="write",C="afterWrite",T=[b,_,w,x,S,O,A,E,C];function k(e){return e?(e.nodeName||"").toLowerCase():null}function L(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function j(e){return e instanceof L(e).Element||e instanceof Element}function P(e){return e instanceof L(e).HTMLElement||e instanceof HTMLElement}function I(e){return"undefined"!=typeof ShadowRoot&&(e instanceof L(e).ShadowRoot||e instanceof ShadowRoot)}const D={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];P(i)&&k(i)&&(Object.assign(i.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});P(r)&&k(r)&&(Object.assign(r.style,o),Object.keys(i).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]};function N(e){return e.split("-")[0]}var F=Math.max,R=Math.min,M=Math.round;function $(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function q(){return!/^((?!chrome|android).)*safari/i.test($())}function H(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&P(e)&&(i=e.offsetWidth>0&&M(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&M(r.height)/e.offsetHeight||1);var s=(j(e)?L(e):window).visualViewport,a=!q()&&n,l=(r.left+(a&&s?s.offsetLeft:0))/i,c=(r.top+(a&&s?s.offsetTop:0))/o,u=r.width/i,d=r.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function B(e){var t=H(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function U(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&I(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function W(e){return L(e).getComputedStyle(e)}function z(e){return["table","td","th"].indexOf(k(e))>=0}function V(e){return((j(e)?e.ownerDocument:e.document)||window.document).documentElement}function G(e){return"html"===k(e)?e:e.assignedSlot||e.parentNode||(I(e)?e.host:null)||V(e)}function K(e){return P(e)&&"fixed"!==W(e).position?e.offsetParent:null}function X(e){for(var t=L(e),n=K(e);n&&z(n)&&"static"===W(n).position;)n=K(n);return n&&("html"===k(n)||"body"===k(n)&&"static"===W(n).position)?t:n||function(e){var t=/firefox/i.test($());if(/Trident/i.test($())&&P(e)&&"fixed"===W(e).position)return null;var n=G(e);for(I(n)&&(n=n.host);P(n)&&["html","body"].indexOf(k(n))<0;){var r=W(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Q(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Y(e,t,n){return F(e,R(t,n))}function J(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Z(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}const ee={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,i=e.options,c=n.elements.arrow,d=n.modifiersData.popperOffsets,f=N(n.placement),p=Q(f),h=[l,a].indexOf(f)>=0?"height":"width";if(c&&d){var g=function(e,t){return J("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Z(e,u))}(i.padding,n),m=B(c),v="y"===p?o:l,y="y"===p?s:a,b=n.rects.reference[h]+n.rects.reference[p]-d[p]-n.rects.popper[h],_=d[p]-n.rects.reference[p],w=X(c),x=w?"y"===p?w.clientHeight||0:w.clientWidth||0:0,S=b/2-_/2,O=g[v],A=x-m[h]-g[y],E=x/2-m[h]/2+S,C=Y(O,E,A),T=p;n.modifiersData[r]=((t={})[T]=C,t.centerOffset=C-E,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&U(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function te(e){return e.split("-")[1]}var ne={top:"auto",right:"auto",bottom:"auto",left:"auto"};function re(e){var t,n=e.popper,r=e.popperRect,i=e.placement,c=e.variation,u=e.offsets,d=e.position,p=e.gpuAcceleration,h=e.adaptive,g=e.roundOffsets,m=e.isFixed,v=u.x,y=void 0===v?0:v,b=u.y,_=void 0===b?0:b,w="function"==typeof g?g({x:y,y:_}):{x:y,y:_};y=w.x,_=w.y;var x=u.hasOwnProperty("x"),S=u.hasOwnProperty("y"),O=l,A=o,E=window;if(h){var C=X(n),T="clientHeight",k="clientWidth";if(C===L(n)&&"static"!==W(C=V(n)).position&&"absolute"===d&&(T="scrollHeight",k="scrollWidth"),i===o||(i===l||i===a)&&c===f)A=s,_-=(m&&C===E&&E.visualViewport?E.visualViewport.height:C[T])-r.height,_*=p?1:-1;if(i===l||(i===o||i===s)&&c===f)O=a,y-=(m&&C===E&&E.visualViewport?E.visualViewport.width:C[k])-r.width,y*=p?1:-1}var j,P=Object.assign({position:d},h&&ne),I=!0===g?function(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:M(n*i)/i||0,y:M(r*i)/i||0}}({x:y,y:_},L(n)):{x:y,y:_};return y=I.x,_=I.y,p?Object.assign({},P,((j={})[A]=S?"0":"",j[O]=x?"0":"",j.transform=(E.devicePixelRatio||1)<=1?"translate("+y+"px, "+_+"px)":"translate3d("+y+"px, "+_+"px, 0)",j)):Object.assign({},P,((t={})[A]=S?_+"px":"",t[O]=x?y+"px":"",t.transform="",t))}const ie={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:N(t.placement),variation:te(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,re(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,re(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var oe={passive:!0};const se={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=void 0===i||i,s=r.resize,a=void 0===s||s,l=L(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(e){e.addEventListener("scroll",n.update,oe)}),a&&l.addEventListener("resize",n.update,oe),function(){o&&c.forEach(function(e){e.removeEventListener("scroll",n.update,oe)}),a&&l.removeEventListener("resize",n.update,oe)}},data:{}};var ae={left:"right",right:"left",bottom:"top",top:"bottom"};function le(e){return e.replace(/left|right|bottom|top/g,function(e){return ae[e]})}var ce={start:"end",end:"start"};function ue(e){return e.replace(/start|end/g,function(e){return ce[e]})}function de(e){var t=L(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function fe(e){return H(V(e)).left+de(e).scrollLeft}function pe(e){var t=W(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function he(e){return["html","body","#document"].indexOf(k(e))>=0?e.ownerDocument.body:P(e)&&pe(e)?e:he(G(e))}function ge(e,t){var n;void 0===t&&(t=[]);var r=he(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),o=L(r),s=i?[o].concat(o.visualViewport||[],pe(r)?r:[]):r,a=t.concat(s);return i?a:a.concat(ge(G(s)))}function me(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ve(e,t,n){return t===h?me(function(e,t){var n=L(e),r=V(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;var c=q();(c||!c&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a+fe(e),y:l}}(e,n)):j(t)?function(e,t){var n=H(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):me(function(e){var t,n=V(e),r=de(e),i=null==(t=e.ownerDocument)?void 0:t.body,o=F(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=F(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+fe(e),l=-r.scrollTop;return"rtl"===W(i||n).direction&&(a+=F(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(V(e)))}function ye(e,t,n,r){var i="clippingParents"===t?function(e){var t=ge(G(e)),n=["absolute","fixed"].indexOf(W(e).position)>=0&&P(e)?X(e):e;return j(n)?t.filter(function(e){return j(e)&&U(e,n)&&"body"!==k(e)}):[]}(e):[].concat(t),o=[].concat(i,[n]),s=o[0],a=o.reduce(function(t,n){var i=ve(e,n,r);return t.top=F(i.top,t.top),t.right=R(i.right,t.right),t.bottom=R(i.bottom,t.bottom),t.left=F(i.left,t.left),t},ve(e,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function be(e){var t,n=e.reference,r=e.element,i=e.placement,c=i?N(i):null,u=i?te(i):null,p=n.x+n.width/2-r.width/2,h=n.y+n.height/2-r.height/2;switch(c){case o:t={x:p,y:n.y-r.height};break;case s:t={x:p,y:n.y+n.height};break;case a:t={x:n.x+n.width,y:h};break;case l:t={x:n.x-r.width,y:h};break;default:t={x:n.x,y:n.y}}var g=c?Q(c):null;if(null!=g){var m="y"===g?"height":"width";switch(u){case d:t[g]=t[g]-(n[m]/2-r[m]/2);break;case f:t[g]=t[g]+(n[m]/2-r[m]/2)}}return t}function _e(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=void 0===r?e.placement:r,l=n.strategy,c=void 0===l?e.strategy:l,d=n.boundary,f=void 0===d?p:d,v=n.rootBoundary,y=void 0===v?h:v,b=n.elementContext,_=void 0===b?g:b,w=n.altBoundary,x=void 0!==w&&w,S=n.padding,O=void 0===S?0:S,A=J("number"!=typeof O?O:Z(O,u)),E=_===g?m:g,C=e.rects.popper,T=e.elements[x?E:_],k=ye(j(T)?T:T.contextElement||V(e.elements.popper),f,y,c),L=H(e.elements.reference),P=be({reference:L,element:C,strategy:"absolute",placement:i}),I=me(Object.assign({},C,P)),D=_===g?I:L,N={top:k.top-D.top+A.top,bottom:D.bottom-k.bottom+A.bottom,left:k.left-D.left+A.left,right:D.right-k.right+A.right},F=e.modifiersData.offset;if(_===g&&F){var R=F[i];Object.keys(N).forEach(function(e){var t=[a,s].indexOf(e)>=0?1:-1,n=[o,s].indexOf(e)>=0?"y":"x";N[e]+=R[n]*t})}return N}const we={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,f=void 0===i||i,p=n.altAxis,h=void 0===p||p,g=n.fallbackPlacements,m=n.padding,b=n.boundary,_=n.rootBoundary,w=n.altBoundary,x=n.flipVariations,S=void 0===x||x,O=n.allowedAutoPlacements,A=t.options.placement,E=N(A),C=g||(E===A||!S?[le(A)]:function(e){if(N(e)===c)return[];var t=le(e);return[ue(e),t,ue(t)]}(A)),T=[A].concat(C).reduce(function(e,n){return e.concat(N(n)===c?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?y:l,d=te(r),f=d?a?v:v.filter(function(e){return te(e)===d}):u,p=f.filter(function(e){return c.indexOf(e)>=0});0===p.length&&(p=f);var h=p.reduce(function(t,n){return t[n]=_e(e,{placement:n,boundary:i,rootBoundary:o,padding:s})[N(n)],t},{});return Object.keys(h).sort(function(e,t){return h[e]-h[t]})}(t,{placement:n,boundary:b,rootBoundary:_,padding:m,flipVariations:S,allowedAutoPlacements:O}):n)},[]),k=t.rects.reference,L=t.rects.popper,j=new Map,P=!0,I=T[0],D=0;D<T.length;D++){var F=T[D],R=N(F),M=te(F)===d,$=[o,s].indexOf(R)>=0,q=$?"width":"height",H=_e(t,{placement:F,boundary:b,rootBoundary:_,altBoundary:w,padding:m}),B=$?M?a:l:M?s:o;k[q]>L[q]&&(B=le(B));var U=le(B),W=[];if(f&&W.push(H[R]<=0),h&&W.push(H[B]<=0,H[U]<=0),W.every(function(e){return e})){I=F,P=!1;break}j.set(F,W)}if(P)for(var z=function(e){var t=T.find(function(t){var n=j.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return I=t,"break"},V=S?3:1;V>0;V--){if("break"===z(V))break}t.placement!==I&&(t.modifiersData[r]._skip=!0,t.placement=I,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xe(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Se(e){return[o,a,s,l].some(function(t){return e[t]>=0})}const Oe={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,s=_e(t,{elementContext:"reference"}),a=_e(t,{altBoundary:!0}),l=xe(s,r),c=xe(a,i,o),u=Se(l),d=Se(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}};const Ae={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.offset,s=void 0===i?[0,0]:i,c=y.reduce(function(e,n){return e[n]=function(e,t,n){var r=N(e),i=[l,o].indexOf(r)>=0?-1:1,s="function"==typeof n?n(Object.assign({},t,{placement:e})):n,c=s[0],u=s[1];return c=c||0,u=(u||0)*i,[l,a].indexOf(r)>=0?{x:u,y:c}:{x:c,y:u}}(n,t.rects,s),e},{}),u=c[t.placement],d=u.x,f=u.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=d,t.modifiersData.popperOffsets.y+=f),t.modifiersData[r]=c}};const Ee={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=be({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const Ce={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,c=void 0===i||i,u=n.altAxis,f=void 0!==u&&u,p=n.boundary,h=n.rootBoundary,g=n.altBoundary,m=n.padding,v=n.tether,y=void 0===v||v,b=n.tetherOffset,_=void 0===b?0:b,w=_e(t,{boundary:p,rootBoundary:h,padding:m,altBoundary:g}),x=N(t.placement),S=te(t.placement),O=!S,A=Q(x),E="x"===A?"y":"x",C=t.modifiersData.popperOffsets,T=t.rects.reference,k=t.rects.popper,L="function"==typeof _?_(Object.assign({},t.rects,{placement:t.placement})):_,j="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),P=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,I={x:0,y:0};if(C){if(c){var D,M="y"===A?o:l,$="y"===A?s:a,q="y"===A?"height":"width",H=C[A],U=H+w[M],W=H-w[$],z=y?-k[q]/2:0,V=S===d?T[q]:k[q],G=S===d?-k[q]:-T[q],K=t.elements.arrow,J=y&&K?B(K):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ee=Z[M],ne=Z[$],re=Y(0,T[q],J[q]),ie=O?T[q]/2-z-re-ee-j.mainAxis:V-re-ee-j.mainAxis,oe=O?-T[q]/2+z+re+ne+j.mainAxis:G+re+ne+j.mainAxis,se=t.elements.arrow&&X(t.elements.arrow),ae=se?"y"===A?se.clientTop||0:se.clientLeft||0:0,le=null!=(D=null==P?void 0:P[A])?D:0,ce=H+oe-le,ue=Y(y?R(U,H+ie-le-ae):U,H,y?F(W,ce):W);C[A]=ue,I[A]=ue-H}if(f){var de,fe="x"===A?o:l,pe="x"===A?s:a,he=C[E],ge="y"===E?"height":"width",me=he+w[fe],ve=he-w[pe],ye=-1!==[o,l].indexOf(x),be=null!=(de=null==P?void 0:P[E])?de:0,we=ye?me:he-T[ge]-k[ge]-be+j.altAxis,xe=ye?he+T[ge]+k[ge]-be-j.altAxis:ve,Se=y&&ye?function(e,t,n){var r=Y(e,t,n);return r>n?n:r}(we,he,xe):Y(y?we:me,he,y?xe:ve);C[E]=Se,I[E]=Se-he}t.modifiersData[r]=I}},requiresIfExists:["offset"]};function Te(e,t,n){void 0===n&&(n=!1);var r,i,o=P(t),s=P(t)&&function(e){var t=e.getBoundingClientRect(),n=M(t.width)/e.offsetWidth||1,r=M(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=V(t),l=H(e,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==k(t)||pe(a))&&(c=(r=t)!==L(r)&&P(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:de(r)),P(t)?((u=H(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):a&&(u.x=fe(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function ke(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}}),r.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||i(e)}),r}var Le={placement:"bottom",modifiers:[],strategy:"absolute"};function je(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function Pe(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,o=void 0===i?Le:i;return function(e,t,n){void 0===n&&(n=o);var i,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Le,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;d(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:j(e)?ge(e):e.contextElement?ge(e.contextElement):[],popper:ge(t)};var s,c,f=function(e){var t=ke(e);return T.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}((s=[].concat(r,a.options.modifiers),c=s.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{}),Object.keys(c).map(function(e){return c[e]})));return a.orderedModifiers=f.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var o=i({state:a,name:t,instance:u,options:r}),s=function(){};l.push(o||s)}}),u.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(je(t,n)){a.rects={reference:Te(t,X(n),"fixed"===a.options.strategy),popper:B(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,s=i.options,l=void 0===s?{}:s,d=i.name;"function"==typeof o&&(a=o({state:a,options:l,name:d,instance:u})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise(function(e){u.forceUpdate(),e(a)})},function(){return s||(s=new Promise(function(e){Promise.resolve().then(function(){s=void 0,e(i())})})),s}),destroy:function(){d(),c=!0}};if(!je(e,t))return u;function d(){l.forEach(function(e){return e()}),l=[]}return u.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),u}}var Ie=Pe(),De=Pe({defaultModifiers:[se,Ee,ie,D,Ae,we,Ce,ee,Oe]}),Ne=Pe({defaultModifiers:[se,Ee,ie,D]}),Fe=n(3886);const Re=new Map,Me={set(e,t,n){Re.has(e)||Re.set(e,new Map);const r=Re.get(e);r.has(t)||0===r.size?r.set(t,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`)},get:(e,t)=>Re.has(e)&&Re.get(e).get(t)||null,remove(e,t){if(!Re.has(e))return;const n=Re.get(e);n.delete(t),0===n.size&&Re.delete(e)}},$e="transitionend",qe=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),He=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),Be=e=>{e.dispatchEvent(new Event($e))},Ue=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),We=e=>Ue(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(qe(e)):null,ze=e=>{if(!Ue(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},Ve=e=>!e||e.nodeType!==Node.ELEMENT_NODE||(!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))),Ge=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Ge(e.parentNode):null},Ke=()=>{},Xe=e=>{e.offsetHeight},Qe=()=>Fe&&!document.body.hasAttribute("data-bs-no-jquery")?Fe:null,Ye=[],Je=()=>"rtl"===document.documentElement.dir,Ze=e=>{var t;t=()=>{const t=Qe();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}},"loading"===document.readyState?(Ye.length||document.addEventListener("DOMContentLoaded",()=>{for(const e of Ye)e()}),Ye.push(t)):t()},et=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,tt=(e,t,n=!0)=>{if(!n)return void et(e);const r=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return r||i?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5;let i=!1;const o=({target:n})=>{n===t&&(i=!0,t.removeEventListener($e,o),et(e))};t.addEventListener($e,o),setTimeout(()=>{i||Be(t)},r)},nt=(e,t,n,r)=>{const i=e.length;let o=e.indexOf(t);return-1===o?!n&&r?e[i-1]:e[0]:(o+=n?1:-1,r&&(o=(o+i)%i),e[Math.max(0,Math.min(o,i-1))])},rt=/[^.]*(?=\..*)\.|.*/,it=/\..*/,ot=/::\d+$/,st={};let at=1;const lt={mouseenter:"mouseover",mouseleave:"mouseout"},ct=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function ut(e,t){return t&&`${t}::${at++}`||e.uidEvent||at++}function dt(e){const t=ut(e);return e.uidEvent=t,st[t]=st[t]||{},st[t]}function ft(e,t,n=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===n)}function pt(e,t,n){const r="string"==typeof t,i=r?n:t||n;let o=vt(e);return ct.has(o)||(o=e),[r,i,o]}function ht(e,t,n,r,i){if("string"!=typeof t||!e)return;let[o,s,a]=pt(t,n,r);if(t in lt){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};s=e(s)}const l=dt(e),c=l[a]||(l[a]={}),u=ft(c,s,o?n:null);if(u)return void(u.oneOff=u.oneOff&&i);const d=ut(s,t.replace(rt,"")),f=o?function(e,t,n){return function r(i){const o=e.querySelectorAll(t);for(let{target:s}=i;s&&s!==this;s=s.parentNode)for(const a of o)if(a===s)return bt(i,{delegateTarget:s}),r.oneOff&&yt.off(e,i.type,t,n),n.apply(s,[i])}}(e,n,s):function(e,t){return function n(r){return bt(r,{delegateTarget:e}),n.oneOff&&yt.off(e,r.type,t),t.apply(e,[r])}}(e,s);f.delegationSelector=o?n:null,f.callable=s,f.oneOff=i,f.uidEvent=d,c[d]=f,e.addEventListener(a,f,o)}function gt(e,t,n,r,i){const o=ft(t[n],r,i);o&&(e.removeEventListener(n,o,Boolean(i)),delete t[n][o.uidEvent])}function mt(e,t,n,r){const i=t[n]||{};for(const[o,s]of Object.entries(i))o.includes(r)&&gt(e,t,n,s.callable,s.delegationSelector)}function vt(e){return e=e.replace(it,""),lt[e]||e}const yt={on(e,t,n,r){ht(e,t,n,r,!1)},one(e,t,n,r){ht(e,t,n,r,!0)},off(e,t,n,r){if("string"!=typeof t||!e)return;const[i,o,s]=pt(t,n,r),a=s!==t,l=dt(e),c=l[s]||{},u=t.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(l))mt(e,l,n,t.slice(1));for(const[n,r]of Object.entries(c)){const i=n.replace(ot,"");a&&!t.includes(i)||gt(e,l,s,r.callable,r.delegationSelector)}}else{if(!Object.keys(c).length)return;gt(e,l,s,o,i?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const r=Qe();let i=null,o=!0,s=!0,a=!1;t!==vt(t)&&r&&(i=r.Event(t,n),r(e).trigger(i),o=!i.isPropagationStopped(),s=!i.isImmediatePropagationStopped(),a=i.isDefaultPrevented());const l=bt(new Event(t,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),s&&e.dispatchEvent(l),l.defaultPrevented&&i&&i.preventDefault(),l}};function bt(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>r})}return e}function _t(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function wt(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const xt={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${wt(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${wt(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"));for(const r of n){let n=r.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),t[n]=_t(e.dataset[r])}return t},getDataAttribute:(e,t)=>_t(e.getAttribute(`data-bs-${wt(t)}`))};class St{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=Ue(t)?xt.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...Ue(t)?xt.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[n,r]of Object.entries(t)){const t=e[n],i=Ue(t)?"element":He(t);if(!new RegExp(r).test(i))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${i}" but expected type "${r}".`)}}}class Ot extends St{constructor(e,t){super(),(e=We(e))&&(this._element=e,this._config=this._getConfig(t),Me.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Me.remove(this._element,this.constructor.DATA_KEY),yt.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){tt(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return Me.get(We(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const At=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map(e=>qe(e)).join(","):null},Et={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!Ve(e)&&ze(e))},getSelectorFromElement(e){const t=At(e);return t&&Et.findOne(t)?t:null},getElementFromSelector(e){const t=At(e);return t?Et.findOne(t):null},getMultipleElementsFromSelector(e){const t=At(e);return t?Et.find(t):[]}},Ct=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;yt.on(document,n,`[data-bs-dismiss="${r}"]`,function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),Ve(this))return;const i=Et.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(i)[t]()})},Tt=".bs.alert",kt=`close${Tt}`,Lt=`closed${Tt}`;class jt extends Ot{static get NAME(){return"alert"}close(){if(yt.trigger(this._element,kt).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),yt.trigger(this._element,Lt),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=jt.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}Ct(jt,"close"),Ze(jt);const Pt='[data-bs-toggle="button"]';class It extends Ot{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=It.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}yt.on(document,"click.bs.button.data-api",Pt,e=>{e.preventDefault();const t=e.target.closest(Pt);It.getOrCreateInstance(t).toggle()}),Ze(It);const Dt=".bs.swipe",Nt=`touchstart${Dt}`,Ft=`touchmove${Dt}`,Rt=`touchend${Dt}`,Mt=`pointerdown${Dt}`,$t=`pointerup${Dt}`,qt={endCallback:null,leftCallback:null,rightCallback:null},Ht={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Bt extends St{constructor(e,t){super(),this._element=e,e&&Bt.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return qt}static get DefaultType(){return Ht}static get NAME(){return"swipe"}dispose(){yt.off(this._element,Dt)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),et(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&et(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(yt.on(this._element,Mt,e=>this._start(e)),yt.on(this._element,$t,e=>this._end(e)),this._element.classList.add("pointer-event")):(yt.on(this._element,Nt,e=>this._start(e)),yt.on(this._element,Ft,e=>this._move(e)),yt.on(this._element,Rt,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Ut=".bs.carousel",Wt=".data-api",zt="ArrowLeft",Vt="ArrowRight",Gt="next",Kt="prev",Xt="left",Qt="right",Yt=`slide${Ut}`,Jt=`slid${Ut}`,Zt=`keydown${Ut}`,en=`mouseenter${Ut}`,tn=`mouseleave${Ut}`,nn=`dragstart${Ut}`,rn=`load${Ut}${Wt}`,on=`click${Ut}${Wt}`,sn="carousel",an="active",ln=".active",cn=".carousel-item",un=ln+cn,dn={[zt]:Qt,[Vt]:Xt},fn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},pn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class hn extends Ot{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Et.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===sn&&this.cycle()}static get Default(){return fn}static get DefaultType(){return pn}static get NAME(){return"carousel"}next(){this._slide(Gt)}nextWhenVisible(){!document.hidden&&ze(this._element)&&this.next()}prev(){this._slide(Kt)}pause(){this._isSliding&&Be(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?yt.one(this._element,Jt,()=>this.cycle()):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void yt.one(this._element,Jt,()=>this.to(e));const n=this._getItemIndex(this._getActive());if(n===e)return;const r=e>n?Gt:Kt;this._slide(r,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&yt.on(this._element,Zt,e=>this._keydown(e)),"hover"===this._config.pause&&(yt.on(this._element,en,()=>this.pause()),yt.on(this._element,tn,()=>this._maybeEnableCycle())),this._config.touch&&Bt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const e of Et.find(".carousel-item img",this._element))yt.on(e,nn,e=>e.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Xt)),rightCallback:()=>this._slide(this._directionToOrder(Qt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new Bt(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=dn[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=Et.findOne(ln,this._indicatorsElement);t.classList.remove(an),t.removeAttribute("aria-current");const n=Et.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(an),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),r=e===Gt,i=t||nt(this._getItems(),n,r,this._config.wrap);if(i===n)return;const o=this._getItemIndex(i),s=t=>yt.trigger(this._element,t,{relatedTarget:i,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:o});if(s(Yt).defaultPrevented)return;if(!n||!i)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i;const l=r?"carousel-item-start":"carousel-item-end",c=r?"carousel-item-next":"carousel-item-prev";i.classList.add(c),Xe(i),n.classList.add(l),i.classList.add(l);this._queueCallback(()=>{i.classList.remove(l,c),i.classList.add(an),n.classList.remove(an,c,l),this._isSliding=!1,s(Jt)},n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Et.findOne(un,this._element)}_getItems(){return Et.find(cn,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return Je()?e===Xt?Kt:Gt:e===Xt?Gt:Kt}_orderToDirection(e){return Je()?e===Kt?Xt:Qt:e===Kt?Qt:Xt}static jQueryInterface(e){return this.each(function(){const t=hn.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}yt.on(document,on,"[data-bs-slide], [data-bs-slide-to]",function(e){const t=Et.getElementFromSelector(this);if(!t||!t.classList.contains(sn))return;e.preventDefault();const n=hn.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");return r?(n.to(r),void n._maybeEnableCycle()):"next"===xt.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())}),yt.on(window,rn,()=>{const e=Et.find('[data-bs-ride="carousel"]');for(const t of e)hn.getOrCreateInstance(t)}),Ze(hn);const gn=".bs.collapse",mn=`show${gn}`,vn=`shown${gn}`,yn=`hide${gn}`,bn=`hidden${gn}`,_n=`click${gn}.data-api`,wn="show",xn="collapse",Sn="collapsing",On=`:scope .${xn} .${xn}`,An='[data-bs-toggle="collapse"]',En={parent:null,toggle:!0},Cn={parent:"(null|element)",toggle:"boolean"};class Tn extends Ot{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=Et.find(An);for(const e of n){const t=Et.getSelectorFromElement(e),n=Et.find(t).filter(e=>e===this._element);null!==t&&n.length&&this._triggerArray.push(e)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return En}static get DefaultType(){return Cn}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>Tn.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning)return;if(yt.trigger(this._element,mn).defaultPrevented)return;for(const t of e)t.hide();const t=this._getDimension();this._element.classList.remove(xn),this._element.classList.add(Sn),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Sn),this._element.classList.add(xn,wn),this._element.style[t]="",yt.trigger(this._element,vn)},this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(yt.trigger(this._element,yn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,Xe(this._element),this._element.classList.add(Sn),this._element.classList.remove(xn,wn);for(const e of this._triggerArray){const t=Et.getElementFromSelector(e);t&&!this._isShown(t)&&this._addAriaAndCollapsedClass([e],!1)}this._isTransitioning=!0;this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(Sn),this._element.classList.add(xn),yt.trigger(this._element,bn)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(wn)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=We(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(An);for(const t of e){const e=Et.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=Et.find(On,this._config.parent);return Et.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){const n=Tn.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e]()}})}}yt.on(document,_n,An,function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const e of Et.getMultipleElementsFromSelector(this))Tn.getOrCreateInstance(e,{toggle:!1}).toggle()}),Ze(Tn);const kn="dropdown",Ln=".bs.dropdown",jn=".data-api",Pn="ArrowUp",In="ArrowDown",Dn=`hide${Ln}`,Nn=`hidden${Ln}`,Fn=`show${Ln}`,Rn=`shown${Ln}`,Mn=`click${Ln}${jn}`,$n=`keydown${Ln}${jn}`,qn=`keyup${Ln}${jn}`,Hn="show",Bn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Un=`${Bn}.${Hn}`,Wn=".dropdown-menu",zn=Je()?"top-end":"top-start",Vn=Je()?"top-start":"top-end",Gn=Je()?"bottom-end":"bottom-start",Kn=Je()?"bottom-start":"bottom-end",Xn=Je()?"left-start":"right-start",Qn=Je()?"right-start":"left-start",Yn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Jn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Zn extends Ot{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=Et.next(this._element,Wn)[0]||Et.prev(this._element,Wn)[0]||Et.findOne(Wn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Yn}static get DefaultType(){return Jn}static get NAME(){return kn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Ve(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!yt.trigger(this._element,Fn,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))yt.on(e,"mouseover",Ke);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Hn),this._element.classList.add(Hn),yt.trigger(this._element,Rn,e)}}hide(){if(Ve(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!yt.trigger(this._element,Dn,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.off(e,"mouseover",Ke);this._popper&&this._popper.destroy(),this._menu.classList.remove(Hn),this._element.classList.remove(Hn),this._element.setAttribute("aria-expanded","false"),xt.removeDataAttribute(this._menu,"popper"),yt.trigger(this._element,Nn,e),this._element.focus()}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!Ue(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${kn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){let e=this._element;"parent"===this._config.reference?e=this._parent:Ue(this._config.reference)?e=We(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=De(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Hn)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return Xn;if(e.classList.contains("dropstart"))return Qn;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?Vn:zn:t?Kn:Gn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(xt.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...et(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=Et.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>ze(e));n.length&&nt(n,t,e===In,!n.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=Zn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=Et.find(Un);for(const n of t){const t=Zn.getInstance(n);if(!t||!1===t._config.autoClose)continue;const r=e.composedPath(),i=r.includes(t._menu);if(r.includes(t._element)||"inside"===t._config.autoClose&&!i||"outside"===t._config.autoClose&&i)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const o={relatedTarget:t._element};"click"===e.type&&(o.clickEvent=e),t._completeHide(o)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,r=[Pn,In].includes(e.key);if(!r&&!n)return;if(t&&!n)return;e.preventDefault();const i=this.matches(Bn)?this:Et.prev(this,Bn)[0]||Et.next(this,Bn)[0]||Et.findOne(Bn,e.delegateTarget.parentNode),o=Zn.getOrCreateInstance(i);if(r)return e.stopPropagation(),o.show(),void o._selectMenuItem(e);o._isShown()&&(e.stopPropagation(),o.hide(),i.focus())}}yt.on(document,$n,Bn,Zn.dataApiKeydownHandler),yt.on(document,$n,Wn,Zn.dataApiKeydownHandler),yt.on(document,Mn,Zn.clearMenus),yt.on(document,qn,Zn.clearMenus),yt.on(document,Mn,Bn,function(e){e.preventDefault(),Zn.getOrCreateInstance(this).toggle()}),Ze(Zn);const er="backdrop",tr="show",nr=`mousedown.bs.${er}`,rr={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ir={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class or extends St{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return rr}static get DefaultType(){return ir}static get NAME(){return er}show(e){if(!this._config.isVisible)return void et(e);this._append();const t=this._getElement();this._config.isAnimated&&Xe(t),t.classList.add(tr),this._emulateAnimation(()=>{et(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(tr),this._emulateAnimation(()=>{this.dispose(),et(e)})):et(e)}dispose(){this._isAppended&&(yt.off(this._element,nr),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=We(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),yt.on(e,nr,()=>{et(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){tt(e,this._getElement(),this._config.isAnimated)}}const sr=".bs.focustrap",ar=`focusin${sr}`,lr=`keydown.tab${sr}`,cr="backward",ur={autofocus:!0,trapElement:null},dr={autofocus:"boolean",trapElement:"element"};class fr extends St{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ur}static get DefaultType(){return dr}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),yt.off(document,sr),yt.on(document,ar,e=>this._handleFocusin(e)),yt.on(document,lr,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,yt.off(document,sr))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=Et.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===cr?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?cr:"forward")}}const pr=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",hr=".sticky-top",gr="padding-right",mr="margin-right";class vr{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,gr,t=>t+e),this._setElementAttributes(pr,gr,t=>t+e),this._setElementAttributes(hr,mr,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,gr),this._resetElementAttributes(pr,gr),this._resetElementAttributes(hr,mr)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const r=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+r)return;this._saveInitialAttribute(e,t);const i=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(i))}px`)})}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&xt.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{const n=xt.getDataAttribute(e,t);null!==n?(xt.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(Ue(e))t(e);else for(const n of Et.find(e,this._element))t(n)}}const yr=".bs.modal",br=`hide${yr}`,_r=`hidePrevented${yr}`,wr=`hidden${yr}`,xr=`show${yr}`,Sr=`shown${yr}`,Or=`resize${yr}`,Ar=`click.dismiss${yr}`,Er=`mousedown.dismiss${yr}`,Cr=`keydown.dismiss${yr}`,Tr=`click${yr}.data-api`,kr="modal-open",Lr="show",jr="modal-static",Pr={backdrop:!0,focus:!0,keyboard:!0},Ir={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Dr extends Ot{constructor(e,t){super(e,t),this._dialog=Et.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new vr,this._addEventListeners()}static get Default(){return Pr}static get DefaultType(){return Ir}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||this._isTransitioning)return;yt.trigger(this._element,xr,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(kr),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){if(!this._isShown||this._isTransitioning)return;yt.trigger(this._element,br).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Lr),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){yt.off(window,yr),yt.off(this._dialog,yr),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new or({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new fr({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=Et.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),Xe(this._element),this._element.classList.add(Lr);this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,yt.trigger(this._element,Sr,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){yt.on(this._element,Cr,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),yt.on(window,Or,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),yt.on(this._element,Er,e=>{yt.one(this._element,Ar,t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(kr),this._resetAdjustments(),this._scrollBar.reset(),yt.trigger(this._element,wr)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(yt.trigger(this._element,_r).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(jr)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(jr),this._queueCallback(()=>{this._element.classList.remove(jr),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=Je()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=Je()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){const n=Dr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}})}}yt.on(document,Tr,'[data-bs-toggle="modal"]',function(e){const t=Et.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),yt.one(t,xr,e=>{e.defaultPrevented||yt.one(t,wr,()=>{ze(this)&&this.focus()})});const n=Et.findOne(".modal.show");n&&Dr.getInstance(n).hide();Dr.getOrCreateInstance(t).toggle(this)}),Ct(Dr),Ze(Dr);const Nr=".bs.offcanvas",Fr=".data-api",Rr=`load${Nr}${Fr}`,Mr="show",$r="showing",qr="hiding",Hr=".offcanvas.show",Br=`show${Nr}`,Ur=`shown${Nr}`,Wr=`hide${Nr}`,zr=`hidePrevented${Nr}`,Vr=`hidden${Nr}`,Gr=`resize${Nr}`,Kr=`click${Nr}${Fr}`,Xr=`keydown.dismiss${Nr}`,Qr={backdrop:!0,keyboard:!0,scroll:!1},Yr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Jr extends Ot{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Qr}static get DefaultType(){return Yr}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown)return;if(yt.trigger(this._element,Br,{relatedTarget:e}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new vr).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add($r);this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Mr),this._element.classList.remove($r),yt.trigger(this._element,Ur,{relatedTarget:e})},this._element,!0)}hide(){if(!this._isShown)return;if(yt.trigger(this._element,Wr).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(qr),this._backdrop.hide();this._queueCallback(()=>{this._element.classList.remove(Mr,qr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new vr).reset(),yt.trigger(this._element,Vr)},this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new or({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():yt.trigger(this._element,zr)}:null})}_initializeFocusTrap(){return new fr({trapElement:this._element})}_addEventListeners(){yt.on(this._element,Xr,e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():yt.trigger(this._element,zr))})}static jQueryInterface(e){return this.each(function(){const t=Jr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}yt.on(document,Kr,'[data-bs-toggle="offcanvas"]',function(e){const t=Et.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),Ve(this))return;yt.one(t,Vr,()=>{ze(this)&&this.focus()});const n=Et.findOne(Hr);n&&n!==t&&Jr.getInstance(n).hide();Jr.getOrCreateInstance(t).toggle(this)}),yt.on(window,Rr,()=>{for(const e of Et.find(Hr))Jr.getOrCreateInstance(e).show()}),yt.on(window,Gr,()=>{for(const e of Et.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&Jr.getOrCreateInstance(e).hide()}),Ct(Jr),Ze(Jr);const Zr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ei=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ti=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ni=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?!ei.has(n)||Boolean(ti.test(e.nodeValue)):t.filter(e=>e instanceof RegExp).some(e=>e.test(n))};const ri={allowList:Zr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ii={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},oi={entry:"(string|element|function|null)",selector:"(string|element)"};class si extends St{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return ri}static get DefaultType(){return ii}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},oi)}_setContent(e,t,n){const r=Et.findOne(n,e);r&&((t=this._resolvePossibleFunction(t))?Ue(t)?this._putElementInTemplate(We(t),r):this._config.html?r.innerHTML=this._maybeSanitize(t):r.textContent=t:r.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const e of i){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const r=[].concat(...e.attributes),i=[].concat(t["*"]||[],t[n]||[]);for(const t of r)ni(t,i)||e.removeAttribute(t.nodeName)}return r.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return et(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const ai=new Set(["sanitize","allowList","sanitizeFn"]),li="fade",ci="show",ui=".tooltip-inner",di=".modal",fi="hide.bs.modal",pi="hover",hi="focus",gi="click",mi={AUTO:"auto",TOP:"top",RIGHT:Je()?"left":"right",BOTTOM:"bottom",LEFT:Je()?"right":"left"},vi={allowList:Zr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},yi={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class bi extends Ot{constructor(e,t){super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return vi}static get DefaultType(){return yi}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),yt.off(this._element.closest(di),fi,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=yt.trigger(this._element,this.constructor.eventName("show")),t=(Ge(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(n),yt.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(ci),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.on(e,"mouseover",Ke);this._queueCallback(()=>{yt.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(yt.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(ci),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))yt.off(e,"mouseover",Ke);this._activeTrigger[gi]=!1,this._activeTrigger[hi]=!1,this._activeTrigger[pi]=!1,this._isHovered=null;this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),yt.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(li,ci),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(li),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new si({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ui]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(li)}_isShown(){return this.tip&&this.tip.classList.contains(ci)}_createPopper(e){const t=et(this._config.placement,[this,e,this._element]),n=mi[t.toUpperCase()];return De(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return et(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...et(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)yt.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger[gi]=!(t._isShown()&&t._activeTrigger[gi]),t.toggle()});else if("manual"!==t){const e=t===pi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===pi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");yt.on(this._element,e,this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?hi:pi]=!0,t._enter()}),yt.on(this._element,n,this._config.selector,e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?hi:pi]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},yt.on(this._element.closest(di),fi,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=xt.getDataAttributes(this._element);for(const e of Object.keys(t))ai.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:We(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const t=bi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}Ze(bi);const _i=".popover-header",wi=".popover-body",xi={...bi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Si={...bi.DefaultType,content:"(null|string|element|function)"};class Oi extends bi{static get Default(){return xi}static get DefaultType(){return Si}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_i]:this._getTitle(),[wi]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const t=Oi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}Ze(Oi);const Ai=".bs.scrollspy",Ei=`activate${Ai}`,Ci=`click${Ai}`,Ti=`load${Ai}.data-api`,ki="active",Li="[href]",ji=".nav-link",Pi=`${ji}, .nav-item > ${ji}, .list-group-item`,Ii={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Di={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ni extends Ot{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ii}static get DefaultType(){return Di}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=We(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(yt.off(this._config.target,Ci),yt.on(this._config.target,Ci,Li,e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,r=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:r,behavior:"smooth"});n.scrollTop=r}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const o of e){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(o));continue}const e=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&e){if(n(o),!r)return}else i||e||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=Et.find(Li,this._config.target);for(const t of e){if(!t.hash||Ve(t))continue;const e=Et.findOne(decodeURI(t.hash),this._element);ze(e)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(ki),this._activateParents(e),yt.trigger(this._element,Ei,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))Et.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(ki);else for(const t of Et.parents(e,".nav, .list-group"))for(const e of Et.prev(t,Pi))e.classList.add(ki)}_clearActiveClass(e){e.classList.remove(ki);const t=Et.find(`${Li}.${ki}`,e);for(const e of t)e.classList.remove(ki)}static jQueryInterface(e){return this.each(function(){const t=Ni.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}yt.on(window,Ti,()=>{for(const e of Et.find('[data-bs-spy="scroll"]'))Ni.getOrCreateInstance(e)}),Ze(Ni);const Fi=".bs.tab",Ri=`hide${Fi}`,Mi=`hidden${Fi}`,$i=`show${Fi}`,qi=`shown${Fi}`,Hi=`click${Fi}`,Bi=`keydown${Fi}`,Ui=`load${Fi}`,Wi="ArrowLeft",zi="ArrowRight",Vi="ArrowUp",Gi="ArrowDown",Ki="Home",Xi="End",Qi="active",Yi="fade",Ji="show",Zi=".dropdown-toggle",eo=`:not(${Zi})`,to='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',no=`${`.nav-link${eo}, .list-group-item${eo}, [role="tab"]${eo}`}, ${to}`,ro=`.${Qi}[data-bs-toggle="tab"], .${Qi}[data-bs-toggle="pill"], .${Qi}[data-bs-toggle="list"]`;class io extends Ot{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),yt.on(this._element,Bi,e=>this._keydown(e)))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?yt.trigger(t,Ri,{relatedTarget:e}):null;yt.trigger(e,$i,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){if(!e)return;e.classList.add(Qi),this._activate(Et.getElementFromSelector(e));this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),yt.trigger(e,qi,{relatedTarget:t})):e.classList.add(Ji)},e,e.classList.contains(Yi))}_deactivate(e,t){if(!e)return;e.classList.remove(Qi),e.blur(),this._deactivate(Et.getElementFromSelector(e));this._queueCallback(()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),yt.trigger(e,Mi,{relatedTarget:t})):e.classList.remove(Ji)},e,e.classList.contains(Yi))}_keydown(e){if(![Wi,zi,Vi,Gi,Ki,Xi].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter(e=>!Ve(e));let n;if([Ki,Xi].includes(e.key))n=t[e.key===Ki?0:t.length-1];else{const r=[zi,Gi].includes(e.key);n=nt(t,e.target,r,!0)}n&&(n.focus({preventScroll:!0}),io.getOrCreateInstance(n).show())}_getChildren(){return Et.find(no,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const e of t)this._setInitialAttributesOnChild(e)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=Et.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const r=(e,r)=>{const i=Et.findOne(e,n);i&&i.classList.toggle(r,t)};r(Zi,Qi),r(".dropdown-menu",Ji),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Qi)}_getInnerElement(e){return e.matches(no)?e:Et.findOne(no,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){const t=io.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}})}}yt.on(document,Hi,to,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),Ve(this)||io.getOrCreateInstance(this).show()}),yt.on(window,Ui,()=>{for(const e of Et.find(ro))io.getOrCreateInstance(e)}),Ze(io);const oo=".bs.toast",so=`mouseover${oo}`,ao=`mouseout${oo}`,lo=`focusin${oo}`,co=`focusout${oo}`,uo=`hide${oo}`,fo=`hidden${oo}`,po=`show${oo}`,ho=`shown${oo}`,go="hide",mo="show",vo="showing",yo={animation:"boolean",autohide:"boolean",delay:"number"},bo={animation:!0,autohide:!0,delay:5e3};class _o extends Ot{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return bo}static get DefaultType(){return yo}static get NAME(){return"toast"}show(){if(yt.trigger(this._element,po).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(go),Xe(this._element),this._element.classList.add(mo,vo),this._queueCallback(()=>{this._element.classList.remove(vo),yt.trigger(this._element,ho),this._maybeScheduleHide()},this._element,this._config.animation)}hide(){if(!this.isShown())return;if(yt.trigger(this._element,uo).defaultPrevented)return;this._element.classList.add(vo),this._queueCallback(()=>{this._element.classList.add(go),this._element.classList.remove(vo,mo),yt.trigger(this._element,fo)},this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(mo),super.dispose()}isShown(){return this._element.classList.contains(mo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){yt.on(this._element,so,e=>this._onInteraction(e,!0)),yt.on(this._element,ao,e=>this._onInteraction(e,!1)),yt.on(this._element,lo,e=>this._onInteraction(e,!0)),yt.on(this._element,co,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=_o.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}})}}Ct(_o),Ze(_o);n(4958),n(9902),n(7476),n(6558),n(5124),n(9740),n(7628);function wo(e,t){e.split(/\s+/).forEach(e=>{t(e)})}class xo{constructor(){this._events={}}on(e,t){wo(e,e=>{const n=this._events[e]||[];n.push(t),this._events[e]=n})}off(e,t){var n=arguments.length;0!==n?wo(e,e=>{if(1===n)return void delete this._events[e];const r=this._events[e];void 0!==r&&(r.splice(r.indexOf(t),1),this._events[e]=r)}):this._events={}}trigger(e,...t){var n=this;wo(e,e=>{const r=n._events[e];void 0!==r&&r.forEach(e=>{e.apply(n,t)})})}}const So=e=>(e=e.filter(Boolean)).length<2?e[0]||"":1==To(e)?"["+e.join("")+"]":"(?:"+e.join("|")+")",Oo=e=>{if(!Eo(e))return e.join("");let t="",n=0;const r=()=>{n>1&&(t+="{"+n+"}")};return e.forEach((i,o)=>{i!==e[o-1]?(r(),t+=i,n=1):n++}),r(),t},Ao=e=>{let t=Array.from(e);return So(t)},Eo=e=>new Set(e).size!==e.length,Co=e=>(e+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),To=e=>e.reduce((e,t)=>Math.max(e,ko(t)),0),ko=e=>Array.from(e).length,Lo=e=>{if(1===e.length)return[[e]];let t=[];const n=e.substring(1);return Lo(n).forEach(function(n){let r=n.slice(0);r[0]=e.charAt(0)+r[0],t.push(r),r=n.slice(0),r.unshift(e.charAt(0)),t.push(r)}),t},jo=[[0,65535]];let Po,Io;const Do={},No={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let e in No){let t=No[e]||"";for(let n=0;n<t.length;n++){let r=t.substring(n,n+1);Do[r]=e}}const Fo=new RegExp(Object.keys(Do).join("|")+"|[̀-ͯ·ʾʼ]","gu"),Ro=(e,t="NFKD")=>e.normalize(t),Mo=e=>Array.from(e).reduce((e,t)=>e+$o(t),""),$o=e=>(e=Ro(e).toLowerCase().replace(Fo,e=>Do[e]||""),Ro(e,"NFC"));const qo=e=>{const t={},n=(e,n)=>{const r=t[e]||new Set,i=new RegExp("^"+Ao(r)+"$","iu");n.match(i)||(r.add(Co(n)),t[e]=r)};for(let t of function*(e){for(const[t,n]of e)for(let e=t;e<=n;e++){let t=String.fromCharCode(e),n=Mo(t);n!=t.toLowerCase()&&(n.length>3||0!=n.length&&(yield{folded:n,composed:t,code_point:e}))}}(e))n(t.folded,t.folded),n(t.folded,t.composed);return t},Ho=e=>{const t=qo(e),n={};let r=[];for(let e in t){let i=t[e];i&&(n[e]=Ao(i)),e.length>1&&r.push(Co(e))}r.sort((e,t)=>t.length-e.length);const i=So(r);return Io=new RegExp("^"+i,"u"),n},Bo=(e,t=1)=>(t=Math.max(t,e.length-1),So(Lo(e).map(e=>((e,t=1)=>{let n=0;return e=e.map(e=>(Po[e]&&(n+=e.length),Po[e]||e)),n>=t?Oo(e):""})(e,t)))),Uo=(e,t=!0)=>{let n=e.length>1?1:0;return So(e.map(e=>{let r=[];const i=t?e.length():e.length()-1;for(let t=0;t<i;t++)r.push(Bo(e.substrs[t]||"",n));return Oo(r)}))},Wo=(e,t)=>{for(const n of t){if(n.start!=e.start||n.end!=e.end)continue;if(n.substrs.join("")!==e.substrs.join(""))continue;let t=e.parts;const r=e=>{for(const n of t){if(n.start===e.start&&n.substr===e.substr)return!1;if(1!=e.length&&1!=n.length){if(e.start<n.start&&e.end>n.start)return!0;if(n.start<e.start&&n.end>e.start)return!0}}return!1};if(!(n.parts.filter(r).length>0))return!0}return!1};class zo{parts;substrs;start;end;constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(e){e&&(this.parts.push(e),this.substrs.push(e.substr),this.start=Math.min(e.start,this.start),this.end=Math.max(e.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(e,t){let n=new zo,r=JSON.parse(JSON.stringify(this.parts)),i=r.pop();for(const e of r)n.add(e);let o=t.substr.substring(0,e-i.start),s=o.length;return n.add({start:i.start,end:i.start+s,length:s,substr:o}),n}}const Vo=e=>{var t;void 0===Po&&(Po=Ho(t||jo)),e=Mo(e);let n="",r=[new zo];for(let t=0;t<e.length;t++){let i=e.substring(t).match(Io);const o=e.substring(t,t+1),s=i?i[0]:null;let a=[],l=new Set;for(const e of r){const n=e.last();if(!n||1==n.length||n.end<=t)if(s){const n=s.length;e.add({start:t,end:t+n,length:n,substr:s}),l.add("1")}else e.add({start:t,end:t+1,length:1,substr:o}),l.add("2");else if(s){let r=e.clone(t,n);const i=s.length;r.add({start:t,end:t+i,length:i,substr:s}),a.push(r)}else l.add("3")}if(a.length>0){a=a.sort((e,t)=>e.length()-t.length());for(let e of a)Wo(e,r)||r.push(e)}else if(t>0&&1==l.size&&!l.has("3")){n+=Uo(r,!1);let e=new zo;const t=r[0];t&&e.add(t.last()),r=[e]}}return n+=Uo(r,!0),n},Go=(e,t)=>{if(e)return e[t]},Ko=(e,t)=>{if(e){for(var n,r=t.split(".");(n=r.shift())&&(e=e[n]););return e}},Xo=(e,t,n)=>{var r,i;return e?(e+="",null==t.regex||-1===(i=e.search(t.regex))?0:(r=t.string.length/e.length,0===i&&(r+=.5),r*n)):0},Qo=(e,t)=>{var n=e[t];if("function"==typeof n)return n;n&&!Array.isArray(n)&&(e[t]=[n])},Yo=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},Jo=(e,t)=>"number"==typeof e&&"number"==typeof t?e>t?1:e<t?-1:0:(e=Mo(e+"").toLowerCase())>(t=Mo(t+"").toLowerCase())?1:t>e?-1:0;class Zo{items;settings;constructor(e,t){this.items=e,this.settings=t||{diacritics:!0}}tokenize(e,t,n){if(!e||!e.length)return[];const r=[],i=e.split(/\s+/);var o;return n&&(o=new RegExp("^("+Object.keys(n).map(Co).join("|")+"):(.*)$")),i.forEach(e=>{let n,i=null,s=null;o&&(n=e.match(o))&&(i=n[1],e=n[2]),e.length>0&&(s=this.settings.diacritics?Vo(e)||null:Co(e),s&&t&&(s="\\b"+s)),r.push({string:e,regex:s?new RegExp(s,"iu"):null,field:i})}),r}getScoreFunction(e,t){var n=this.prepareSearch(e,t);return this._getScoreFunction(n)}_getScoreFunction(e){const t=e.tokens,n=t.length;if(!n)return function(){return 0};const r=e.options.fields,i=e.weights,o=r.length,s=e.getAttrFn;if(!o)return function(){return 1};const a=1===o?function(e,t){const n=r[0].field;return Xo(s(t,n),e,i[n]||1)}:function(e,t){var n=0;if(e.field){const r=s(t,e.field);!e.regex&&r?n+=1/o:n+=Xo(r,e,1)}else Yo(i,(r,i)=>{n+=Xo(s(t,i),e,r)});return n/o};return 1===n?function(e){return a(t[0],e)}:"and"===e.options.conjunction?function(e){var r,i=0;for(let n of t){if((r=a(n,e))<=0)return 0;i+=r}return i/n}:function(e){var r=0;return Yo(t,t=>{r+=a(t,e)}),r/n}}getSortFunction(e,t){var n=this.prepareSearch(e,t);return this._getSortFunction(n)}_getSortFunction(e){var t,n=[];const r=this,i=e.options,o=!e.query&&i.sort_empty?i.sort_empty:i.sort;if("function"==typeof o)return o.bind(this);const s=function(t,n){return"$score"===t?n.score:e.getAttrFn(r.items[n.id],t)};if(o)for(let t of o)(e.query||"$score"!==t.field)&&n.push(t);if(e.query){t=!0;for(let e of n)if("$score"===e.field){t=!1;break}t&&n.unshift({field:"$score",direction:"desc"})}else n=n.filter(e=>"$score"!==e.field);return n.length?function(e,t){var r,i;for(let o of n){if(i=o.field,r=("desc"===o.direction?-1:1)*Jo(s(i,e),s(i,t)))return r}return 0}:null}prepareSearch(e,t){const n={};var r=Object.assign({},t);if(Qo(r,"sort"),Qo(r,"sort_empty"),r.fields){Qo(r,"fields");const e=[];r.fields.forEach(t=>{"string"==typeof t&&(t={field:t,weight:1}),e.push(t),n[t.field]="weight"in t?t.weight:1}),r.fields=e}return{options:r,query:e.toLowerCase().trim(),tokens:this.tokenize(e,r.respect_word_boundaries,n),total:0,items:[],weights:n,getAttrFn:r.nesting?Ko:Go}}search(e,t){var n,r,i=this;r=this.prepareSearch(e,t),t=r.options,e=r.query;const o=t.score||i._getScoreFunction(r);e.length?Yo(i.items,(e,i)=>{n=o(e),(!1===t.filter||n>0)&&r.items.push({score:n,id:i})}):Yo(i.items,(e,t)=>{r.items.push({score:1,id:t})});const s=i._getSortFunction(r);return s&&r.items.sort(s),r.total=r.items.length,"number"==typeof t.limit&&(r.items=r.items.slice(0,t.limit)),r}}const es=e=>null==e?null:ts(e),ts=e=>"boolean"==typeof e?e?"1":"0":e+"",ns=e=>(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),rs=(e,t)=>{var n;return function(r,i){var o=this;n&&(o.loading=Math.max(o.loading-1,0),clearTimeout(n)),n=setTimeout(function(){n=null,o.loadedSearches[r]=!0,e.call(o,r,i)},t)}},is=(e,t,n)=>{var r,i=e.trigger,o={};for(r of(e.trigger=function(){var n=arguments[0];if(-1===t.indexOf(n))return i.apply(e,arguments);o[n]=arguments},n.apply(e,[]),e.trigger=i,t))r in o&&i.apply(e,o[r])},os=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},ss=(e,t,n,r)=>{e.addEventListener(t,n,r)},as=(e,t)=>!!t&&(!!t[e]&&1===(t.altKey?1:0)+(t.ctrlKey?1:0)+(t.shiftKey?1:0)+(t.metaKey?1:0)),ls=(e,t)=>{const n=e.getAttribute("id");return n||(e.setAttribute("id",t),t)},cs=e=>e.replace(/[\\"']/g,"\\$&"),us=(e,t)=>{t&&e.append(t)},ds=(e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},fs=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(ps(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},ps=e=>"string"==typeof e&&e.indexOf("<")>-1,hs=(e,t)=>{var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!1),e.dispatchEvent(n)},gs=(e,t)=>{Object.assign(e.style,t)},ms=(e,...t)=>{var n=ys(t);(e=bs(e)).map(e=>{n.map(t=>{e.classList.add(t)})})},vs=(e,...t)=>{var n=ys(t);(e=bs(e)).map(e=>{n.map(t=>{e.classList.remove(t)})})},ys=e=>{var t=[];return ds(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},bs=e=>(Array.isArray(e)||(e=[e]),e),_s=(e,t,n)=>{if(!n||n.contains(e))for(;e&&e.matches;){if(e.matches(t))return e;e=e.parentNode}},ws=(e,t=0)=>t>0?e[e.length-1]:e[0],xs=(e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n},Ss=(e,t)=>{ds(t,(t,n)=>{null==t?e.removeAttribute(n):e.setAttribute(n,""+t)})},Os=(e,t)=>{e.parentNode&&e.parentNode.replaceChild(t,e)},As=(e,t)=>{if(null===t)return;if("string"==typeof t){if(!t.length)return;t=new RegExp(t,"i")}const n=e=>3===e.nodeType?(e=>{var n=e.data.match(t);if(n&&e.data.length>0){var r=document.createElement("span");r.className="highlight";var i=e.splitText(n.index);i.splitText(n[0].length);var o=i.cloneNode(!0);return r.appendChild(o),Os(i,r),1}return 0})(e):((e=>{1!==e.nodeType||!e.childNodes||/(script|style)/i.test(e.tagName)||"highlight"===e.className&&"SPAN"===e.tagName||Array.from(e.childNodes).forEach(e=>{n(e)})})(e),0);n(e)},Es="undefined"!=typeof navigator&&/Mac/.test(navigator.userAgent)?"metaKey":"ctrlKey",Cs={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,refreshThrottle:300,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(e){return e.length>0},render:{}};function Ts(e,t){var n=Object.assign({},Cs,t),r=n.dataAttr,i=n.labelField,o=n.valueField,s=n.disabledField,a=n.optgroupField,l=n.optgroupLabelField,c=n.optgroupValueField,u=e.tagName.toLowerCase(),d=e.getAttribute("placeholder")||e.getAttribute("data-placeholder");if(!d&&!n.allowEmptyOption){let t=e.querySelector('option[value=""]');t&&(d=t.textContent)}var f={placeholder:d,options:[],optgroups:[],items:[],maxItems:null};return"select"===u?(()=>{var t,u=f.options,d={},p=1;let h=0;var g=e=>{var t=Object.assign({},e.dataset),n=r&&t[r];return"string"==typeof n&&n.length&&(t=Object.assign(t,JSON.parse(n))),t},m=(e,t)=>{var r=es(e.value);if(null!=r&&(r||n.allowEmptyOption)){if(d.hasOwnProperty(r)){if(t){var l=d[r][a];l?Array.isArray(l)?l.push(t):d[r][a]=[l,t]:d[r][a]=t}}else{var c=g(e);c[i]=c[i]||e.textContent,c[o]=c[o]||r,c[s]=c[s]||e.disabled,c[a]=c[a]||t,c.$option=e,c.$order=c.$order||++h,d[r]=c,u.push(c)}e.selected&&f.items.push(r)}};f.maxItems=e.hasAttribute("multiple")?null:1,ds(e.children,e=>{var n,r,i;"optgroup"===(t=e.tagName.toLowerCase())?((i=g(n=e))[l]=i[l]||n.getAttribute("label")||"",i[c]=i[c]||p++,i[s]=i[s]||n.disabled,i.$order=i.$order||++h,f.optgroups.push(i),r=i[c],ds(n.children,e=>{m(e,r)})):"option"===t&&m(e)})})():(()=>{const t=e.getAttribute(r);if(t)f.options=JSON.parse(t),ds(f.options,e=>{f.items.push(e[o])});else{var s=e.value.trim()||"";if(!n.allowEmptyOption&&!s.length)return;const t=s.split(n.delimiter);ds(t,e=>{const t={};t[i]=e,t[o]=e,f.options.push(t)}),f.items=t}})(),Object.assign({},Cs,f,t)}var ks=0;class Ls extends(function(e){return e.plugins={},class extends e{constructor(){super(...arguments),this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(t,n){e.plugins[t]={name:t,fn:n}}initializePlugins(e){var t,n;const r=this,i=[];if(Array.isArray(e))e.forEach(e=>{"string"==typeof e?i.push(e):(r.plugins.settings[e.name]=e.options,i.push(e.name))});else if(e)for(t in e)e.hasOwnProperty(t)&&(r.plugins.settings[t]=e[t],i.push(t));for(;n=i.shift();)r.require(n)}loadPlugin(t){var n=this,r=n.plugins,i=e.plugins[t];if(!e.plugins.hasOwnProperty(t))throw new Error('Unable to find "'+t+'" plugin');r.requested[t]=!0,r.loaded[t]=i.fn.apply(n,[n.plugins.settings[t]||{}]),r.names.push(t)}require(e){var t=this,n=t.plugins;if(!t.plugins.loaded.hasOwnProperty(e)){if(n.requested[e])throw new Error('Plugin has circular dependency ("'+e+'")');t.loadPlugin(e)}return n.loaded[e]}}}(xo)){constructor(e,t){var n;super(),this.order=0,this.isOpen=!1,this.isDisabled=!1,this.isReadOnly=!1,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],this.refreshTimeout=null,ks++;var r=fs(e);if(r.tomselect)throw new Error("Tom Select already initialized on this element");r.tomselect=this,n=(window.getComputedStyle&&window.getComputedStyle(r,null)).getPropertyValue("direction");const i=Ts(r,t);this.settings=i,this.input=r,this.tabIndex=r.tabIndex||0,this.is_select_tag="select"===r.tagName.toLowerCase(),this.rtl=/rtl/i.test(n),this.inputId=ls(r,"tomselect-"+ks),this.isRequired=r.required,this.sifter=new Zo(this.options,{diacritics:i.diacritics}),i.mode=i.mode||(1===i.maxItems?"single":"multi"),"boolean"!=typeof i.hideSelected&&(i.hideSelected="multi"===i.mode),"boolean"!=typeof i.hidePlaceholder&&(i.hidePlaceholder="multi"!==i.mode);var o=i.createFilter;"function"!=typeof o&&("string"==typeof o&&(o=new RegExp(o)),o instanceof RegExp?i.createFilter=e=>o.test(e):i.createFilter=e=>this.settings.duplicates||!this.options[e]),this.initializePlugins(i.plugins),this.setupCallbacks(),this.setupTemplates();const s=fs("<div>"),a=fs("<div>"),l=this._render("dropdown"),c=fs('<div role="listbox" tabindex="-1">'),u=this.input.getAttribute("class")||"",d=i.mode;var f;if(ms(s,i.wrapperClass,u,d),ms(a,i.controlClass),us(s,a),ms(l,i.dropdownClass,d),i.copyClassesToDropdown&&ms(l,u),ms(c,i.dropdownContentClass),us(l,c),fs(i.dropdownParent||s).appendChild(l),ps(i.controlInput)){f=fs(i.controlInput);ds(["autocorrect","autocapitalize","autocomplete","spellcheck"],e=>{r.getAttribute(e)&&Ss(f,{[e]:r.getAttribute(e)})}),f.tabIndex=-1,a.appendChild(f),this.focus_node=f}else i.controlInput?(f=fs(i.controlInput),this.focus_node=f):(f=fs("<input/>"),this.focus_node=a);this.wrapper=s,this.dropdown=l,this.dropdown_content=c,this.control=a,this.control_input=f,this.setup()}setup(){const e=this,t=e.settings,n=e.control_input,r=e.dropdown,i=e.dropdown_content,o=e.wrapper,s=e.control,a=e.input,l=e.focus_node,c={passive:!0},u=e.inputId+"-ts-dropdown";Ss(i,{id:u}),Ss(l,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":u});const d=ls(l,e.inputId+"-ts-control"),f="label[for='"+(e=>e.replace(/['"\\]/g,"\\$&"))(e.inputId)+"']",p=document.querySelector(f),h=e.focus.bind(e);if(p){ss(p,"click",h),Ss(p,{for:d});const t=ls(p,e.inputId+"-ts-label");Ss(l,{"aria-labelledby":t}),Ss(i,{"aria-labelledby":t})}if(o.style.width=a.style.width,e.plugins.names.length){const t="plugin-"+e.plugins.names.join(" plugin-");ms([o,r],t)}(null===t.maxItems||t.maxItems>1)&&e.is_select_tag&&Ss(a,{multiple:"multiple"}),t.placeholder&&Ss(n,{placeholder:t.placeholder}),!t.splitOn&&t.delimiter&&(t.splitOn=new RegExp("\\s*"+Co(t.delimiter)+"+\\s*")),t.load&&t.loadThrottle&&(t.load=rs(t.load,t.loadThrottle)),ss(r,"mousemove",()=>{e.ignoreHover=!1}),ss(r,"mouseenter",t=>{var n=_s(t.target,"[data-selectable]",r);n&&e.onOptionHover(t,n)},{capture:!0}),ss(r,"click",t=>{const n=_s(t.target,"[data-selectable]");n&&(e.onOptionSelect(t,n),os(t,!0))}),ss(s,"click",t=>{var r=_s(t.target,"[data-ts-item]",s);r&&e.onItemSelect(t,r)?os(t,!0):""==n.value&&(e.onClick(),os(t,!0))}),ss(l,"keydown",t=>e.onKeyDown(t)),ss(n,"keypress",t=>e.onKeyPress(t)),ss(n,"input",t=>e.onInput(t)),ss(l,"blur",t=>e.onBlur(t)),ss(l,"focus",t=>e.onFocus(t)),ss(n,"paste",t=>e.onPaste(t));const g=t=>{const i=t.composedPath()[0];if(!o.contains(i)&&!r.contains(i))return e.isFocused&&e.blur(),void e.inputState();i==n&&e.isOpen?t.stopPropagation():os(t,!0)},m=()=>{e.isOpen&&e.positionDropdown()};ss(document,"mousedown",g),ss(window,"scroll",m,c),ss(window,"resize",m,c),this._destroy=()=>{document.removeEventListener("mousedown",g),window.removeEventListener("scroll",m),window.removeEventListener("resize",m),p&&p.removeEventListener("click",h)},this.revertSettings={innerHTML:a.innerHTML,tabIndex:a.tabIndex},a.tabIndex=-1,a.insertAdjacentElement("afterend",e.wrapper),e.sync(!1),t.items=[],delete t.optgroups,delete t.options,ss(a,"invalid",()=>{e.isValid&&(e.isValid=!1,e.isInvalid=!0,e.refreshState())}),e.updateOriginalInput(),e.refreshItems(),e.close(!1),e.inputState(),e.isSetup=!0,a.disabled?e.disable():a.readOnly?e.setReadOnly(!0):e.enable(),e.on("change",this.onChange),ms(a,"tomselected","ts-hidden-accessible"),e.trigger("initialize"),!0===t.preload&&e.preload()}setupOptions(e=[],t=[]){this.addOptions(e),ds(t,e=>{this.registerOptionGroup(e)})}setupTemplates(){var e=this,t=e.settings.labelField,n=e.settings.optgroupLabelField,r={optgroup:e=>{let t=document.createElement("div");return t.className="optgroup",t.appendChild(e.options),t},optgroup_header:(e,t)=>'<div class="optgroup-header">'+t(e[n])+"</div>",option:(e,n)=>"<div>"+n(e[t])+"</div>",item:(e,n)=>"<div>"+n(e[t])+"</div>",option_create:(e,t)=>'<div class="create">Add <strong>'+t(e.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};e.settings.render=Object.assign({},r,e.settings.render)}setupCallbacks(){var e,t,n={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(e in n)(t=this.settings[n[e]])&&this.on(e,t)}sync(e=!0){const t=this,n=e?Ts(t.input,{delimiter:t.settings.delimiter}):t.settings;t.setupOptions(n.options,n.optgroups),t.setValue(n.items||[],!0),t.lastQuery=null}onClick(){var e=this;if(e.activeItems.length>0)return e.clearActiveItems(),void e.focus();e.isFocused&&e.isOpen?e.blur():e.focus()}onMouseDown(){}onChange(){hs(this.input,"input"),hs(this.input,"change")}onPaste(e){var t=this;t.isInputHidden||t.isLocked?os(e):t.settings.splitOn&&setTimeout(()=>{var e=t.inputValue();if(e.match(t.settings.splitOn)){var n=e.trim().split(t.settings.splitOn);ds(n,e=>{es(e)&&(this.options[e]?t.addItem(e):t.createItem(e))})}},0)}onKeyPress(e){var t=this;if(!t.isLocked){var n=String.fromCharCode(e.keyCode||e.which);return t.settings.create&&"multi"===t.settings.mode&&n===t.settings.delimiter?(t.createItem(),void os(e)):void 0}os(e)}onKeyDown(e){var t=this;if(t.ignoreHover=!0,t.isLocked)9!==e.keyCode&&os(e);else{switch(e.keyCode){case 65:if(as(Es,e)&&""==t.control_input.value)return os(e),void t.selectAll();break;case 27:return t.isOpen&&(os(e,!0),t.close()),void t.clearActiveItems();case 40:if(!t.isOpen&&t.hasOptions)t.open();else if(t.activeOption){let e=t.getAdjacent(t.activeOption,1);e&&t.setActiveOption(e)}return void os(e);case 38:if(t.activeOption){let e=t.getAdjacent(t.activeOption,-1);e&&t.setActiveOption(e)}return void os(e);case 13:return void(t.canSelect(t.activeOption)?(t.onOptionSelect(e,t.activeOption),os(e)):(t.settings.create&&t.createItem()||document.activeElement==t.control_input&&t.isOpen)&&os(e));case 37:return void t.advanceSelection(-1,e);case 39:return void t.advanceSelection(1,e);case 9:return void(t.settings.selectOnTab&&(t.canSelect(t.activeOption)&&(t.onOptionSelect(e,t.activeOption),os(e)),t.settings.create&&t.createItem()&&os(e)));case 8:case 46:return void t.deleteSelection(e)}t.isInputHidden&&!as(Es,e)&&os(e)}}onInput(e){if(this.isLocked)return;const t=this.inputValue();this.lastValue!==t&&(this.lastValue=t,""!=t?(this.refreshTimeout&&window.clearTimeout(this.refreshTimeout),this.refreshTimeout=((e,t)=>t>0?window.setTimeout(e,t):(e.call(null),null))(()=>{this.refreshTimeout=null,this._onInput()},this.settings.refreshThrottle)):this._onInput())}_onInput(){const e=this.lastValue;this.settings.shouldLoad.call(this,e)&&this.load(e),this.refreshOptions(),this.trigger("type",e)}onOptionHover(e,t){this.ignoreHover||this.setActiveOption(t,!1)}onFocus(e){var t=this,n=t.isFocused;if(t.isDisabled||t.isReadOnly)return t.blur(),void os(e);t.ignoreFocus||(t.isFocused=!0,"focus"===t.settings.preload&&t.preload(),n||t.trigger("focus"),t.activeItems.length||(t.inputState(),t.refreshOptions(!!t.settings.openOnFocus)),t.refreshState())}onBlur(e){if(!1!==document.hasFocus()){var t=this;if(t.isFocused){t.isFocused=!1,t.ignoreFocus=!1;var n=()=>{t.close(),t.setActiveItem(),t.setCaret(t.items.length),t.trigger("blur")};t.settings.create&&t.settings.createOnBlur?t.createItem(null,n):n()}}}onOptionSelect(e,t){var n,r=this;t.parentElement&&t.parentElement.matches("[data-disabled]")||(t.classList.contains("create")?r.createItem(null,()=>{r.settings.closeAfterSelect&&r.close()}):void 0!==(n=t.dataset.value)&&(r.lastQuery=null,r.addItem(n),r.settings.closeAfterSelect&&r.close(),!r.settings.hideSelected&&e.type&&/click/.test(e.type)&&r.setActiveOption(t)))}canSelect(e){return!!(this.isOpen&&e&&this.dropdown_content.contains(e))}onItemSelect(e,t){var n=this;return!n.isLocked&&"multi"===n.settings.mode&&(os(e),n.setActiveItem(t,e),!0)}canLoad(e){return!!this.settings.load&&!this.loadedSearches.hasOwnProperty(e)}load(e){const t=this;if(!t.canLoad(e))return;ms(t.wrapper,t.settings.loadingClass),t.loading++;const n=t.loadCallback.bind(t);t.settings.load.call(t,e,n)}loadCallback(e,t){const n=this;n.loading=Math.max(n.loading-1,0),n.lastQuery=null,n.clearActiveOption(),n.setupOptions(e,t),n.refreshOptions(n.isFocused&&!n.isInputHidden),n.loading||vs(n.wrapper,n.settings.loadingClass),n.trigger("load",e,t)}preload(){var e=this.wrapper.classList;e.contains("preloaded")||(e.add("preloaded"),this.load(""))}setTextboxValue(e=""){var t=this.control_input;t.value!==e&&(t.value=e,hs(t,"update"),this.lastValue=e)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(e,t){is(this,t?[]:["change"],()=>{this.clear(t),this.addItems(e,t)})}setMaxItems(e){0===e&&(e=null),this.settings.maxItems=e,this.refreshState()}setActiveItem(e,t){var n,r,i,o,s,a,l=this;if("single"!==l.settings.mode){if(!e)return l.clearActiveItems(),void(l.isFocused&&l.inputState());if("click"===(n=t&&t.type.toLowerCase())&&as("shiftKey",t)&&l.activeItems.length){for(a=l.getLastActive(),(i=Array.prototype.indexOf.call(l.control.children,a))>(o=Array.prototype.indexOf.call(l.control.children,e))&&(s=i,i=o,o=s),r=i;r<=o;r++)e=l.control.children[r],-1===l.activeItems.indexOf(e)&&l.setActiveItemClass(e);os(t)}else"click"===n&&as(Es,t)||"keydown"===n&&as("shiftKey",t)?e.classList.contains("active")?l.removeActiveItem(e):l.setActiveItemClass(e):(l.clearActiveItems(),l.setActiveItemClass(e));l.inputState(),l.isFocused||l.focus()}}setActiveItemClass(e){const t=this,n=t.control.querySelector(".last-active");n&&vs(n,"last-active"),ms(e,"active last-active"),t.trigger("item_select",e),-1==t.activeItems.indexOf(e)&&t.activeItems.push(e)}removeActiveItem(e){var t=this.activeItems.indexOf(e);this.activeItems.splice(t,1),vs(e,"active")}clearActiveItems(){vs(this.activeItems,"active"),this.activeItems=[]}setActiveOption(e,t=!0){e!==this.activeOption&&(this.clearActiveOption(),e&&(this.activeOption=e,Ss(this.focus_node,{"aria-activedescendant":e.getAttribute("id")}),Ss(e,{"aria-selected":"true"}),ms(e,"active"),t&&this.scrollToOption(e)))}scrollToOption(e,t){if(!e)return;const n=this.dropdown_content,r=n.clientHeight,i=n.scrollTop||0,o=e.offsetHeight,s=e.getBoundingClientRect().top-n.getBoundingClientRect().top+i;s+o>r+i?this.scroll(s-r+o,t):s<i&&this.scroll(s,t)}scroll(e,t){const n=this.dropdown_content;t&&(n.style.scrollBehavior=t),n.scrollTop=e,n.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(vs(this.activeOption,"active"),Ss(this.activeOption,{"aria-selected":null})),this.activeOption=null,Ss(this.focus_node,{"aria-activedescendant":null})}selectAll(){const e=this;if("single"===e.settings.mode)return;const t=e.controlChildren();t.length&&(e.inputState(),e.close(),e.activeItems=t,ds(t,t=>{e.setActiveItemClass(t)}))}inputState(){var e=this;e.control.contains(e.control_input)&&(Ss(e.control_input,{placeholder:e.settings.placeholder}),e.activeItems.length>0||!e.isFocused&&e.settings.hidePlaceholder&&e.items.length>0?(e.setTextboxValue(),e.isInputHidden=!0):(e.settings.hidePlaceholder&&e.items.length>0&&Ss(e.control_input,{placeholder:""}),e.isInputHidden=!1),e.wrapper.classList.toggle("input-hidden",e.isInputHidden))}inputValue(){return this.control_input.value.trim()}focus(){var e=this;e.isDisabled||e.isReadOnly||(e.ignoreFocus=!0,e.control_input.offsetWidth?e.control_input.focus():e.focus_node.focus(),setTimeout(()=>{e.ignoreFocus=!1,e.onFocus()},0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(e){return this.sifter.getScoreFunction(e,this.getSearchOptions())}getSearchOptions(){var e=this.settings,t=e.sortField;return"string"==typeof e.sortField&&(t=[{field:e.sortField}]),{fields:e.searchField,conjunction:e.searchConjunction,sort:t,nesting:e.nesting}}search(e){var t,n,r=this,i=this.getSearchOptions();if(r.settings.score&&"function"!=typeof(n=r.settings.score.call(r,e)))throw new Error('Tom Select "score" setting must be a function that returns a function');return e!==r.lastQuery?(r.lastQuery=e,t=r.sifter.search(e,Object.assign(i,{score:n})),r.currentResults=t):t=Object.assign({},r.currentResults),r.settings.hideSelected&&(t.items=t.items.filter(e=>{let t=es(e.id);return!(t&&-1!==r.items.indexOf(t))})),t}refreshOptions(e=!0){var t,n,r,i,o,s,a,l,c,u;const d={},f=[];var p=this,h=p.inputValue();const g=h===p.lastQuery||""==h&&null==p.lastQuery;var m=p.search(h),v=null,y=p.settings.shouldOpen||!1,b=p.dropdown_content;g&&(v=p.activeOption)&&(c=v.closest("[data-group]")),i=m.items.length,"number"==typeof p.settings.maxOptions&&(i=Math.min(i,p.settings.maxOptions)),i>0&&(y=!0);const _=(e,t)=>{let n=d[e];if(void 0!==n){let e=f[n];if(void 0!==e)return[n,e.fragment]}let r=document.createDocumentFragment();return n=f.length,f.push({fragment:r,order:t,optgroup:e}),[n,r]};for(t=0;t<i;t++){let e=m.items[t];if(!e)continue;let i=e.id,a=p.options[i];if(void 0===a)continue;let l=ts(i),u=p.getOption(l,!0);for(p.settings.hideSelected||u.classList.toggle("selected",p.items.includes(l)),o=a[p.settings.optgroupField]||"",n=0,r=(s=Array.isArray(o)?o:[o])&&s.length;n<r;n++){o=s[n];let e=a.$order,t=p.optgroups[o];void 0===t?o="":e=t.$order;const[r,l]=_(o,e);n>0&&(u=u.cloneNode(!0),Ss(u,{id:a.$id+"-clone-"+n,"aria-selected":null}),u.classList.add("ts-cloned"),vs(u,"active"),p.activeOption&&p.activeOption.dataset.value==i&&c&&c.dataset.group===o.toString()&&(v=u)),l.appendChild(u),""!=o&&(d[o]=r)}}var w;p.settings.lockOptgroupOrder&&f.sort((e,t)=>e.order-t.order),a=document.createDocumentFragment(),ds(f,e=>{let t=e.fragment,n=e.optgroup;if(!t||!t.children.length)return;let r=p.optgroups[n];if(void 0!==r){let e=document.createDocumentFragment(),n=p.render("optgroup_header",r);us(e,n),us(e,t);let i=p.render("optgroup",{group:r,options:e});us(a,i)}else us(a,t)}),b.innerHTML="",us(b,a),p.settings.highlight&&(w=b.querySelectorAll("span.highlight"),Array.prototype.forEach.call(w,function(e){var t=e.parentNode;t.replaceChild(e.firstChild,e),t.normalize()}),m.query.length&&m.tokens.length&&ds(m.tokens,e=>{As(b,e.regex)}));var x=e=>{let t=p.render(e,{input:h});return t&&(y=!0,b.insertBefore(t,b.firstChild)),t};if(p.loading?x("loading"):p.settings.shouldLoad.call(p,h)?0===m.items.length&&x("no_results"):x("not_loading"),(l=p.canCreate(h))&&(u=x("option_create")),p.hasOptions=m.items.length>0||l,y){if(m.items.length>0){if(v||"single"!==p.settings.mode||null==p.items[0]||(v=p.getOption(p.items[0])),!b.contains(v)){let e=0;u&&!p.settings.addPrecedence&&(e=1),v=p.selectable()[e]}}else u&&(v=u);e&&!p.isOpen&&(p.open(),p.scrollToOption(v,"auto")),p.setActiveOption(v)}else p.clearActiveOption(),e&&p.isOpen&&p.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(e,t=!1){const n=this;if(Array.isArray(e))return n.addOptions(e,t),!1;const r=es(e[n.settings.valueField]);return null!==r&&!n.options.hasOwnProperty(r)&&(e.$order=e.$order||++n.order,e.$id=n.inputId+"-opt-"+e.$order,n.options[r]=e,n.lastQuery=null,t&&(n.userOptions[r]=t,n.trigger("option_add",r,e)),r)}addOptions(e,t=!1){ds(e,e=>{this.addOption(e,t)})}registerOption(e){return this.addOption(e)}registerOptionGroup(e){var t=es(e[this.settings.optgroupValueField]);return null!==t&&(e.$order=e.$order||++this.order,this.optgroups[t]=e,t)}addOptionGroup(e,t){var n;t[this.settings.optgroupValueField]=e,(n=this.registerOptionGroup(t))&&this.trigger("optgroup_add",n,t)}removeOptionGroup(e){this.optgroups.hasOwnProperty(e)&&(delete this.optgroups[e],this.clearCache(),this.trigger("optgroup_remove",e))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(e,t){const n=this;var r,i;const o=es(e),s=es(t[n.settings.valueField]);if(null===o)return;const a=n.options[o];if(null==a)return;if("string"!=typeof s)throw new Error("Value must be set in option data");const l=n.getOption(o),c=n.getItem(o);if(t.$order=t.$order||a.$order,delete n.options[o],n.uncacheValue(s),n.options[s]=t,l){if(n.dropdown_content.contains(l)){const e=n._render("option",t);Os(l,e),n.activeOption===l&&n.setActiveOption(e)}l.remove()}c&&(-1!==(i=n.items.indexOf(o))&&n.items.splice(i,1,s),r=n._render("item",t),c.classList.contains("active")&&ms(r,"active"),Os(c,r)),n.lastQuery=null}removeOption(e,t){const n=this;e=ts(e),n.uncacheValue(e),delete n.userOptions[e],delete n.options[e],n.lastQuery=null,n.trigger("option_remove",e),n.removeItem(e,t)}clearOptions(e){const t=(e||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();const n={};ds(this.options,(e,r)=>{t(e,r)&&(n[r]=e)}),this.options=this.sifter.items=n,this.lastQuery=null,this.trigger("option_clear")}clearFilter(e,t){return this.items.indexOf(t)>=0}getOption(e,t=!1){const n=es(e);if(null===n)return null;const r=this.options[n];if(null!=r){if(r.$div)return r.$div;if(t)return this._render("option",r)}return null}getAdjacent(e,t,n="option"){var r;if(!e)return null;r="item"==n?this.controlChildren():this.dropdown_content.querySelectorAll("[data-selectable]");for(let n=0;n<r.length;n++)if(r[n]==e)return t>0?r[n+1]:r[n-1];return null}getItem(e){if("object"==typeof e)return e;var t=es(e);return null!==t?this.control.querySelector(`[data-value="${cs(t)}"]`):null}addItems(e,t){var n=this,r=Array.isArray(e)?e:[e];const i=(r=r.filter(e=>-1===n.items.indexOf(e)))[r.length-1];r.forEach(e=>{n.isPending=e!==i,n.addItem(e,t)})}addItem(e,t){is(this,t?[]:["change","dropdown_close"],()=>{var n,r;const i=this,o=i.settings.mode,s=es(e);if((!s||-1===i.items.indexOf(s)||("single"===o&&i.close(),"single"!==o&&i.settings.duplicates))&&null!==s&&i.options.hasOwnProperty(s)&&("single"===o&&i.clear(t),"multi"!==o||!i.isFull())){if(n=i._render("item",i.options[s]),i.control.contains(n)&&(n=n.cloneNode(!0)),r=i.isFull(),i.items.splice(i.caretPos,0,s),i.insertAtCaret(n),i.isSetup){if(!i.isPending&&i.settings.hideSelected){let e=i.getOption(s),t=i.getAdjacent(e,1);t&&i.setActiveOption(t)}i.isPending||i.settings.closeAfterSelect||i.refreshOptions(i.isFocused&&"single"!==o),0!=i.settings.closeAfterSelect&&i.isFull()?i.close():i.isPending||i.positionDropdown(),i.trigger("item_add",s,n),i.isPending||i.updateOriginalInput({silent:t})}(!i.isPending||!r&&i.isFull())&&(i.inputState(),i.refreshState())}})}removeItem(e=null,t){const n=this;if(!(e=n.getItem(e)))return;var r,i;const o=e.dataset.value;r=xs(e),e.remove(),e.classList.contains("active")&&(i=n.activeItems.indexOf(e),n.activeItems.splice(i,1),vs(e,"active")),n.items.splice(r,1),n.lastQuery=null,!n.settings.persist&&n.userOptions.hasOwnProperty(o)&&n.removeOption(o,t),r<n.caretPos&&n.setCaret(n.caretPos-1),n.updateOriginalInput({silent:t}),n.refreshState(),n.positionDropdown(),n.trigger("item_remove",o,e)}createItem(e=null,t=()=>{}){3===arguments.length&&(t=arguments[2]),"function"!=typeof t&&(t=()=>{});var n,r=this,i=r.caretPos;if(e=e||r.inputValue(),!r.canCreate(e))return t(),!1;r.lock();var o=!1,s=e=>{if(r.unlock(),!e||"object"!=typeof e)return t();var n=es(e[r.settings.valueField]);if("string"!=typeof n)return t();r.setTextboxValue(),r.addOption(e,!0),r.setCaret(i),r.addItem(n),t(e),o=!0};return n="function"==typeof r.settings.create?r.settings.create.call(this,e,s):{[r.settings.labelField]:e,[r.settings.valueField]:e},o||s(n),!0}refreshItems(){var e=this;e.lastQuery=null,e.isSetup&&e.addItems(e.items),e.updateOriginalInput(),e.refreshState()}refreshState(){const e=this;e.refreshValidityState();const t=e.isFull(),n=e.isLocked;e.wrapper.classList.toggle("rtl",e.rtl);const r=e.wrapper.classList;var i;r.toggle("focus",e.isFocused),r.toggle("disabled",e.isDisabled),r.toggle("readonly",e.isReadOnly),r.toggle("required",e.isRequired),r.toggle("invalid",!e.isValid),r.toggle("locked",n),r.toggle("full",t),r.toggle("input-active",e.isFocused&&!e.isInputHidden),r.toggle("dropdown-active",e.isOpen),r.toggle("has-options",(i=e.options,0===Object.keys(i).length)),r.toggle("has-items",e.items.length>0)}refreshValidityState(){var e=this;e.input.validity&&(e.isValid=e.input.validity.valid,e.isInvalid=!e.isValid)}isFull(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems}updateOriginalInput(e={}){const t=this;var n,r;const i=t.input.querySelector('option[value=""]');if(t.is_select_tag){const o=[],s=t.input.querySelectorAll("option:checked").length;function a(e,n,r){return e||(e=fs('<option value="'+ns(n)+'">'+ns(r)+"</option>")),e!=i&&t.input.append(e),o.push(e),(e!=i||s>0)&&(e.selected=!0),e}t.input.querySelectorAll("option:checked").forEach(e=>{e.selected=!1}),0==t.items.length&&"single"==t.settings.mode?a(i,"",""):t.items.forEach(e=>{if(n=t.options[e],r=n[t.settings.labelField]||"",o.includes(n.$option)){a(t.input.querySelector(`option[value="${cs(e)}"]:not(:checked)`),e,r)}else n.$option=a(n.$option,e,r)})}else t.input.value=t.getValue();t.isSetup&&(e.silent||t.trigger("change",t.getValue()))}open(){var e=this;e.isLocked||e.isOpen||"multi"===e.settings.mode&&e.isFull()||(e.isOpen=!0,Ss(e.focus_node,{"aria-expanded":"true"}),e.refreshState(),gs(e.dropdown,{visibility:"hidden",display:"block"}),e.positionDropdown(),gs(e.dropdown,{visibility:"visible",display:"block"}),e.focus(),e.trigger("dropdown_open",e.dropdown))}close(e=!0){var t=this,n=t.isOpen;e&&(t.setTextboxValue(),"single"===t.settings.mode&&t.items.length&&t.inputState()),t.isOpen=!1,Ss(t.focus_node,{"aria-expanded":"false"}),gs(t.dropdown,{display:"none"}),t.settings.hideSelected&&t.clearActiveOption(),t.refreshState(),n&&t.trigger("dropdown_close",t.dropdown)}positionDropdown(){if("body"===this.settings.dropdownParent){var e=this.control,t=e.getBoundingClientRect(),n=e.offsetHeight+t.top+window.scrollY,r=t.left+window.scrollX;gs(this.dropdown,{width:t.width+"px",top:n+"px",left:r+"px"})}}clear(e){var t=this;if(t.items.length){var n=t.controlChildren();ds(n,e=>{t.removeItem(e,!0)}),t.inputState(),e||t.updateOriginalInput(),t.trigger("clear")}}insertAtCaret(e){const t=this,n=t.caretPos,r=t.control;r.insertBefore(e,r.children[n]||null),t.setCaret(n+1)}deleteSelection(e){var t,n,r,i,o,s=this;t=e&&8===e.keyCode?-1:1,n={start:(o=s.control_input).selectionStart||0,length:(o.selectionEnd||0)-(o.selectionStart||0)};const a=[];if(s.activeItems.length)i=ws(s.activeItems,t),r=xs(i),t>0&&r++,ds(s.activeItems,e=>a.push(e));else if((s.isFocused||"single"===s.settings.mode)&&s.items.length){const e=s.controlChildren();let r;t<0&&0===n.start&&0===n.length?r=e[s.caretPos-1]:t>0&&n.start===s.inputValue().length&&(r=e[s.caretPos]),void 0!==r&&a.push(r)}if(!s.shouldDelete(a,e))return!1;for(os(e,!0),void 0!==r&&s.setCaret(r);a.length;)s.removeItem(a.pop());return s.inputState(),s.positionDropdown(),s.refreshOptions(!1),!0}shouldDelete(e,t){const n=e.map(e=>e.dataset.value);return!(!n.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete(n,t))}advanceSelection(e,t){var n,r,i=this;i.rtl&&(e*=-1),i.inputValue().length||(as(Es,t)||as("shiftKey",t)?(r=(n=i.getLastActive(e))?n.classList.contains("active")?i.getAdjacent(n,e,"item"):n:e>0?i.control_input.nextElementSibling:i.control_input.previousElementSibling)&&(r.classList.contains("active")&&i.removeActiveItem(n),i.setActiveItemClass(r)):i.moveCaret(e))}moveCaret(e){}getLastActive(e){let t=this.control.querySelector(".last-active");if(t)return t;var n=this.control.querySelectorAll(".active");return n?ws(n,e):void 0}setCaret(e){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.setLocked(!0)}unlock(){this.setLocked(!1)}setLocked(e=this.isReadOnly||this.isDisabled){this.isLocked=e,this.refreshState()}disable(){this.setDisabled(!0),this.close()}enable(){this.setDisabled(!1)}setDisabled(e){this.focus_node.tabIndex=e?-1:this.tabIndex,this.isDisabled=e,this.input.disabled=e,this.control_input.disabled=e,this.setLocked()}setReadOnly(e){this.isReadOnly=e,this.input.readOnly=e,this.control_input.readOnly=e,this.setLocked()}destroy(){var e=this,t=e.revertSettings;e.trigger("destroy"),e.off(),e.wrapper.remove(),e.dropdown.remove(),e.input.innerHTML=t.innerHTML,e.input.tabIndex=t.tabIndex,vs(e.input,"tomselected","ts-hidden-accessible"),e._destroy(),delete e.input.tomselect}render(e,t){var n,r;const i=this;if("function"!=typeof this.settings.render[e])return null;if(!(r=i.settings.render[e].call(this,t,ns)))return null;if(r=fs(r),"option"===e||"option_create"===e?t[i.settings.disabledField]?Ss(r,{"aria-disabled":"true"}):Ss(r,{"data-selectable":""}):"optgroup"===e&&(n=t.group[i.settings.optgroupValueField],Ss(r,{"data-group":n}),t.group[i.settings.disabledField]&&Ss(r,{"data-disabled":""})),"option"===e||"item"===e){const n=ts(t[i.settings.valueField]);Ss(r,{"data-value":n}),"item"===e?(ms(r,i.settings.itemClass),Ss(r,{"data-ts-item":""})):(ms(r,i.settings.optionClass),Ss(r,{role:"option",id:t.$id}),t.$div=r,i.options[n]=t)}return r}_render(e,t){const n=this.render(e,t);if(null==n)throw"HTMLElement expected";return n}clearCache(){ds(this.options,e=>{e.$div&&(e.$div.remove(),delete e.$div)})}uncacheValue(e){const t=this.getOption(e);t&&t.remove()}canCreate(e){return this.settings.create&&e.length>0&&this.settings.createFilter.call(this,e)}hook(e,t,n){var r=this,i=r[t];r[t]=function(){var t,o;return"after"===e&&(t=i.apply(r,arguments)),o=n.apply(r,arguments),"instead"===e?o:("before"===e&&(t=i.apply(r,arguments)),t)}}}const js=e=>"boolean"==typeof e?e?"1":"0":e+"",Ps=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},Is=e=>"string"==typeof e&&e.indexOf("<")>-1;const Ds=e=>"string"==typeof e&&e.indexOf("<")>-1;const Ns=(e,t,n,r)=>{e.addEventListener(t,n,r)},Fs=e=>"string"==typeof e&&e.indexOf("<")>-1,Rs=(e,t)=>{((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(t,(t,n)=>{null==t?e.removeAttribute(n):e.setAttribute(n,""+t)})};const Ms=e=>"string"==typeof e&&e.indexOf("<")>-1;const $s=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},qs=e=>(Array.isArray(e)||(e=[e]),e);const Hs=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Bs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},Bs=e=>"string"==typeof e&&e.indexOf("<")>-1,Us=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},Ws=e=>(Array.isArray(e)||(e=[e]),e);const zs=(e,t,n,r)=>{e.addEventListener(t,n,r)};const Vs=(e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())},Gs=(e,t,n,r)=>{e.addEventListener(t,n,r)},Ks=e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Xs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)},Xs=e=>"string"==typeof e&&e.indexOf("<")>-1;const Qs=e=>{var t=[];return((e,t)=>{if(Array.isArray(e))e.forEach(t);else for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)})(e,e=>{"string"==typeof e&&(e=e.trim().split(/[\t\n\f\r\s]/)),Array.isArray(e)&&(t=t.concat(e))}),t.filter(Boolean)},Ys=e=>(Array.isArray(e)||(e=[e]),e);Ls.define("change_listener",function(){var e,t,n,r;e=this.input,t="change",n=()=>{this.sync()},e.addEventListener(t,n,r)}),Ls.define("checkbox_options",function(e){var t=this,n=t.onOptionSelect;t.settings.hideSelected=!1;const r=Object.assign({className:"tomselect-checkbox",checkedClassNames:void 0,uncheckedClassNames:void 0},e);var i=function(e,t){t?(e.checked=!0,r.uncheckedClassNames&&e.classList.remove(...r.uncheckedClassNames),r.checkedClassNames&&e.classList.add(...r.checkedClassNames)):(e.checked=!1,r.checkedClassNames&&e.classList.remove(...r.checkedClassNames),r.uncheckedClassNames&&e.classList.add(...r.uncheckedClassNames))},o=function(e){setTimeout(()=>{var t=e.querySelector("input."+r.className);t instanceof HTMLInputElement&&i(t,e.classList.contains("selected"))},1)};t.hook("after","setupTemplates",()=>{var e=t.settings.render.option;t.settings.render.option=(n,o)=>{var s=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Is(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(e.call(t,n,o)),a=document.createElement("input");r.className&&a.classList.add(r.className),a.addEventListener("click",function(e){Ps(e)}),a.type="checkbox";const l=null==(c=n[t.settings.valueField])?null:js(c);var c;return i(a,!!(l&&t.items.indexOf(l)>-1)),s.prepend(a),s}}),t.on("item_remove",e=>{var n=t.getOption(e);n&&(n.classList.remove("selected"),o(n))}),t.on("item_add",e=>{var n=t.getOption(e);n&&o(n)}),t.hook("instead","onOptionSelect",(e,r)=>{if(r.classList.contains("selected"))return r.classList.remove("selected"),t.removeItem(r.dataset.value),t.refreshOptions(),void Ps(e,!0);n.call(t,e,r),o(r)})}),Ls.define("clear_button",function(e){const t=this,n=Object.assign({className:"clear-button",title:"Clear All",html:e=>`<div class="${e.className}" title="${e.title}">&#10799;</div>`},e);t.on("initialize",()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Ds(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(n.html(n));e.addEventListener("click",e=>{t.isLocked||(t.clear(),"single"===t.settings.mode&&t.settings.allowEmptyOption&&t.addItem(""),e.preventDefault(),e.stopPropagation())}),t.control.appendChild(e)})}),Ls.define("drag_drop",function(){var e=this;if("multi"!==e.settings.mode)return;var t=e.lock,n=e.unlock;let r,i=!0;e.hook("after","setupTemplates",()=>{var t=e.settings.render.item;e.settings.render.item=(n,o)=>{const s=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Fs(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(t.call(e,n,o));Rs(s,{draggable:"true"});const a=e=>{e.preventDefault(),s.classList.add("ts-drag-over"),l(s,r)},l=(e,t)=>{var n,r,i;void 0!==t&&(((e,t)=>{do{var n;if(e==(t=null==(n=t)?void 0:n.previousElementSibling))return!0}while(t&&t.previousElementSibling);return!1})(t,s)?(r=t,null==(i=(n=e).parentNode)||i.insertBefore(r,n.nextSibling)):((e,t)=>{var n;null==(n=e.parentNode)||n.insertBefore(t,e)})(e,t))};return Ns(s,"mousedown",e=>{i||((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e),e.stopPropagation()}),Ns(s,"dragstart",e=>{r=s,setTimeout(()=>{s.classList.add("ts-dragging")},0)}),Ns(s,"dragenter",a),Ns(s,"dragover",a),Ns(s,"dragleave",()=>{s.classList.remove("ts-drag-over")}),Ns(s,"dragend",()=>{var t;document.querySelectorAll(".ts-drag-over").forEach(e=>e.classList.remove("ts-drag-over")),null==(t=r)||t.classList.remove("ts-dragging"),r=void 0;var n=[];e.control.querySelectorAll("[data-value]").forEach(e=>{if(e.dataset.value){let t=e.dataset.value;t&&n.push(t)}}),e.setValue(n)}),s}}),e.hook("instead","lock",()=>(i=!1,t.call(e))),e.hook("instead","unlock",()=>(i=!0,n.call(e)))}),Ls.define("dropdown_header",function(e){const t=this,n=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:e=>'<div class="'+e.headerClass+'"><div class="'+e.titleRowClass+'"><span class="'+e.labelClass+'">'+e.title+'</span><a class="'+e.closeClass+'">&times;</a></div></div>'},e);t.on("initialize",()=>{var e=(e=>{if(e.jquery)return e[0];if(e instanceof HTMLElement)return e;if(Ms(e)){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}return document.querySelector(e)})(n.html(n)),r=e.querySelector("."+n.closeClass);r&&r.addEventListener("click",e=>{((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(e,!0),t.close()}),t.dropdown.insertBefore(e,t.dropdown.firstChild)})}),Ls.define("caret_position",function(){var e=this;e.hook("instead","setCaret",t=>{"single"!==e.settings.mode&&e.control.contains(e.control_input)?(t=Math.max(0,Math.min(e.items.length,t)))==e.caretPos||e.isPending||e.controlChildren().forEach((n,r)=>{r<t?e.control_input.insertAdjacentElement("beforebegin",n):e.control.appendChild(n)}):t=e.items.length,e.caretPos=t}),e.hook("instead","moveCaret",t=>{if(!e.isFocused)return;const n=e.getLastActive(t);if(n){const r=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n})(n);e.setCaret(t>0?r+1:r),e.setActiveItem(),((e,...t)=>{var n=$s(t);(e=qs(e)).map(e=>{n.map(t=>{e.classList.remove(t)})})})(n,"last-active")}else e.setCaret(e.caretPos+t)})}),Ls.define("dropdown_input",function(){const e=this;e.settings.shouldOpen=!0,e.hook("before","setup",()=>{e.focus_node=e.control,((e,...t)=>{var n=Us(t);(e=Ws(e)).map(e=>{n.map(t=>{e.classList.add(t)})})})(e.control_input,"dropdown-input");const t=Hs('<div class="dropdown-input-wrap">');t.append(e.control_input),e.dropdown.insertBefore(t,e.dropdown.firstChild);const n=Hs('<input class="items-placeholder" tabindex="-1" />');n.placeholder=e.settings.placeholder||"",e.control.append(n)}),e.on("initialize",()=>{e.control_input.addEventListener("keydown",t=>{switch(t.keyCode){case 27:return e.isOpen&&(((e,t=!1)=>{e&&(e.preventDefault(),t&&e.stopPropagation())})(t,!0),e.close()),void e.clearActiveItems();case 9:e.focus_node.tabIndex=-1}return e.onKeyDown.call(e,t)}),e.on("blur",()=>{e.focus_node.tabIndex=e.isDisabled?-1:e.tabIndex}),e.on("dropdown_open",()=>{e.control_input.focus()});const t=e.onBlur;var n,r,i,o;e.hook("instead","onBlur",n=>{if(!n||n.relatedTarget!=e.control_input)return t.call(e)}),n=e.control_input,r="blur",i=()=>e.onBlur(),n.addEventListener(r,i,o),e.hook("before","close",()=>{e.isOpen&&e.focus_node.focus({preventScroll:!0})})})}),Ls.define("input_autogrow",function(){var e=this;e.on("initialize",()=>{var t=document.createElement("span"),n=e.control_input;t.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",e.wrapper.appendChild(t);for(const e of["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"])t.style[e]=n.style[e];var r=()=>{t.textContent=n.value,n.style.width=t.clientWidth+"px"};r(),e.on("update item_add item_remove",r),zs(n,"input",r),zs(n,"keyup",r),zs(n,"blur",r),zs(n,"update",r)})}),Ls.define("no_backspace_delete",function(){var e=this,t=e.deleteSelection;this.hook("instead","deleteSelection",n=>!!e.activeItems.length&&t.call(e,n))}),Ls.define("no_active_items",function(){this.hook("instead","setActiveItem",()=>{}),this.hook("instead","selectAll",()=>{})}),Ls.define("optgroup_columns",function(){var e=this,t=e.onKeyDown;e.hook("instead","onKeyDown",n=>{var r,i,o,s;if(!e.isOpen||37!==n.keyCode&&39!==n.keyCode)return t.call(e,n);e.ignoreHover=!0,s=((e,t)=>{for(;e&&e.matches;){if(e.matches(t))return e;e=e.parentNode}})(e.activeOption,"[data-group]"),r=((e,t)=>{if(!e)return-1;t=t||e.nodeName;for(var n=0;e=e.previousElementSibling;)e.matches(t)&&n++;return n})(e.activeOption,"[data-selectable]"),s&&(s=37===n.keyCode?s.previousSibling:s.nextSibling)&&(i=(o=s.querySelectorAll("[data-selectable]"))[Math.min(o.length-1,r)])&&e.setActiveOption(i)})}),Ls.define("remove_button",function(e){const t=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},e);var n=this;if(t.append){var r='<a href="javascript:void(0)" class="'+t.className+'" tabindex="-1" title="'+((t.title+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")+'">')+t.label+"</a>";n.hook("after","setupTemplates",()=>{var e=n.settings.render.item;n.settings.render.item=(t,i)=>{var o=Ks(e.call(n,t,i)),s=Ks(r);return o.appendChild(s),Gs(s,"mousedown",e=>{Vs(e,!0)}),Gs(s,"click",e=>{n.isLocked||(Vs(e,!0),n.isLocked||n.shouldDelete([o],e)&&(n.removeItem(o),n.refreshOptions(!1),n.inputState()))}),o}})}}),Ls.define("restore_on_backspace",function(e){const t=this,n=Object.assign({text:e=>e[t.settings.labelField]},e);t.on("item_remove",function(e){if(t.isFocused&&""===t.control_input.value.trim()){var r=t.options[e];r&&t.setTextboxValue(n.text.call(t,r))}})}),Ls.define("virtual_scroll",function(){const e=this,t=e.canLoad,n=e.clearActiveOption,r=e.loadCallback;var i,o,s={},a=!1,l=[];if(e.settings.shouldLoadMore||(e.settings.shouldLoadMore=()=>{if(i.clientHeight/(i.scrollHeight-i.scrollTop)>.9)return!0;if(e.activeOption){var t=e.selectable();if(Array.from(t).indexOf(e.activeOption)>=t.length-2)return!0}return!1}),!e.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";e.settings.sortField=[{field:"$order"},{field:"$score"}];const c=t=>!("number"==typeof e.settings.maxOptions&&i.children.length>=e.settings.maxOptions)&&!(!(t in s)||!s[t]),u=(t,n)=>e.items.indexOf(n)>=0||l.indexOf(n)>=0;e.setNextUrl=(e,t)=>{s[e]=t},e.getUrl=t=>{if(t in s){const e=s[t];return s[t]=!1,e}return e.clearPagination(),e.settings.firstUrl.call(e,t)},e.clearPagination=()=>{s={}},e.hook("instead","clearActiveOption",()=>{if(!a)return n.call(e)}),e.hook("instead","canLoad",n=>n in s?c(n):t.call(e,n)),e.hook("instead","loadCallback",(t,n)=>{if(a){if(o){const n=t[0];void 0!==n&&(o.dataset.value=n[e.settings.valueField])}}else e.clearOptions(u);r.call(e,t,n),a=!1}),e.hook("after","refreshOptions",()=>{const t=e.lastValue;var n;c(t)?(n=e.render("loading_more",{query:t}))&&(n.setAttribute("data-selectable",""),o=n):t in s&&!i.querySelector(".no-results")&&(n=e.render("no_more_results",{query:t})),n&&(((e,...t)=>{var n=Qs(t);(e=Ys(e)).map(e=>{n.map(t=>{e.classList.add(t)})})})(n,e.settings.optionClass),i.append(n))}),e.on("initialize",()=>{l=Object.keys(e.options),i=e.dropdown_content,e.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},e.settings.render),i.addEventListener("scroll",()=>{e.settings.shouldLoadMore.call(e)&&c(e.lastValue)&&(a||(a=!0,e.load.call(e,e.lastValue)))})})});const Js=Ls;function Zs(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,s,a=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(a.push(r.value),a.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ea(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ea(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ea(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}globalThis.TomSelect=Js,document.addEventListener("DOMContentLoaded",function(){var e=function(e,t){return e.customProperties?'<div><span class="dropdown-item-indicator">'.concat(e.customProperties,"</span>").concat(t(e.text),"</div>"):"<div>".concat(t(e.text),"</div>")};null!==document.querySelector(".js-language-selector")&&new Js(".js-language-selector",{copyClassesToDropdown:!1,controlClass:"ts-control locale",dropdownClass:"dropdown-menu ts-dropdown",optionClass:"dropdown-item",controlInput:!1,items:[bb.cookieRead("BBLANG")],render:{item:function(t,n){return e(t,n)},option:function(t,n){return e(t,n)}}}).on("change",function(e){bb.cookieCreate("BBLANG",e,365),bb.reload()});var t=document.querySelector(".autocomplete-selector");null!==t&&new Js(".autocomplete-selector",{copyClassesToDropdown:!1,dropdownClass:"dropdown-menu ts-dropdown",optionClass:"dropdown-item",valueField:"value",labelField:"label",searchField:["label","value"],load:function(e,n){var r,i=new URL(bb.restUrl(t.dataset.resturl));i.searchParams.append("search",e),i.searchParams.append("CSRFToken",t.dataset.csrf),i.searchParams.append("per_page",5),fetch(i).then(function(e){return e.json()}).then(function(e){r=Object.entries(e.result).map(function(e){var t=Zs(e,2),n=t[0];return{label:t[1],value:n}}),n(r)})},render:{option:function(e,t){return function(e,t){return'<div class="py-2 d-flex align-items-center">\n                <span>'.concat(t(e.label),'</span>\n                <small class="text-muted ms-1 lh-1">#').concat(t(e.value),"</small>\n             </div>")}(e,t)},item:function(e,t){return"<span>".concat(t(e.label),"</span>")}}});var n=document.querySelector(".canned_ticket_response");null!==n&&new Js(".canned_ticket_response",{render:{item:function(e,t){return"<div>".concat(t(e.text),"</div>")},option:function(e,t){return"<div>".concat(t(e.text),"</div>")}}}).on("change",function(e){if(console.log(e),e){var t=new URL(bb.restUrl(n.dataset.resturl));t.searchParams.append("id",e),t.searchParams.append("CSRFToken",n.dataset.csrf),fetch(t).then(function(e){return e.json()}).then(function(e){Object.keys(editors).forEach(function(t){editors[t].editor.setData(e.result.content)})})}})});n(7372),n(5549),n(9872),n(8135),n(1048),n(6335),n(8832),n(7200),n(5424),n(6805),n(6461),n(9111),n(4802),n(9028);function ta(e){return ta="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ta(e)}function na(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ra(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?na(Object(n),!0).forEach(function(t){ia(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):na(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ia(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=ta(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ta(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ta(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oa(e){var t=ra(ra({},{hiddenClass:"hidden",visibleClass:"visible",buttonID:"back-to-top",minimum:200}),e);if(document.getElementById(t.buttonID)){var n=document.getElementById(t.buttonID);n.addEventListener("click",function(){document.body.scrollTop=0,document.documentElement.scrollTop=0}),window.onscroll=function(){document.body.scrollTop>t.minimum||document.documentElement.scrollTop>t.minimum?n.classList.replace(t.hiddenClass,t.visibleClass):n.classList.replace(t.visibleClass,t.hiddenClass)}}}var sa=n(3886);function aa(e){return function(e){if(Array.isArray(e))return la(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return la(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?la(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function la(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}globalThis.bb={post:function(e,t,n){API.makeRequest("POST",bb.restUrl(e),JSON.stringify(t),n,function(e){FOSSBilling.message(e.message,"error")}),console.error("This theme or module is using a deprecated method. Please update it to use the new API wrapper instead. Documentation: https://fossbilling.org/docs/api/javascript")},get:function(e,t,n){API.makeRequest("GET",bb.restUrl(e),t,n,function(e){FOSSBilling.message(e.message,"error")}),console.error("This theme or module is using a deprecated method. Please update it to use the new API wrapper instead. Documentation: https://fossbilling.org/docs/api/javascript")},restUrl:function(e){return e.indexOf("http://")>-1||e.indexOf("https://")>-1?e:sa('meta[property="bb:url"]').attr("content")+"index.php?_url=/api/"+e},error:function(e,t){FOSSBilling.message("".concat(e," (").concat(t,")"),"error")},msg:function(e,t){FOSSBilling.message(e,t),console.error("This theme or module is using a deprecated method. Please update it to use FOSSBilling.message() instead of bb.msg().")},redirect:function(e){void 0===e&&this.reload(),window.location=e},reload:function(){window.location.reload(!0)},load:function(e,t){var n="";return sa.ajax({url:e,data:t,type:"GET",success:function(e){n=e},async:!1}),n},_afterComplete:function(e,t){var n=e.getAttribute("data-api-jsonp");if(null!==n&&window.hasOwnProperty(n))return window[n](t);e.classList.contains("bb-rm-tr")?e.closest("tr").classList.add("highlight"):e.hasAttribute("data-api-redirect")?window.location=e.getAttribute("data-api-redirect"):e.hasAttribute("data-api-reload")?window.location.reload():e.hasAttribute("data-api-msg")?FOSSBilling.message(e.getAttribute("data-api-msg"),"success"):t&&FOSSBilling.message("Form updated","success")},apiForm:function(){var e=document.getElementsByClassName("api-form");if(e.length>0)for(var t=function(){var t=e[n];t.addEventListener("submit",function(e){e.preventDefault();var n,r=new FormData(t);if("undefined"!=typeof editors&&Array.isArray(editors)&&editors.length>0){var i=!1;if(Object.keys(editors).forEach(function(e){i=!editors[e].required||""!==editors[e].editor.getData(),r.set(e,editors[e].editor.getData())}),!i)return FOSSBilling.message("At least one of the required fields are empty","error")}n="get"!==t.getAttribute("method").toLowerCase()?r.serializeJSON():r.serialize();var o=document.querySelectorAll("button:not([disabled])");o.forEach(function(e){e.setAttribute("disabled","true")}),API.makeRequest(t.getAttribute("method"),bb.restUrl(t.getAttribute("action")),n,function(e){return o.forEach(function(e){e.removeAttribute("disabled")}),bb._afterComplete(t,e)},function(e){o.forEach(function(e){e.removeAttribute("disabled")}),FOSSBilling.message("".concat(e.message," (").concat(e.code,")"),"error")})})},n=0;n<e.length;n++)t()},apiLink:function(){var e=document.getElementsByClassName("api-link");if(e.length>0)for(var t=function(){var t=e[n];t.addEventListener("click",function(e){return e.preventDefault(),t.dataset.apiConfirm?Modals.create({type:t.dataset.apiType?t.dataset.apiType:"small-confirm",confirmButton:t.dataset.apiConfirmBtn?t.dataset.apiConfirmBtn:"Confirm",content:t.dataset.apiConfirmContent?t.dataset.apiConfirmContent:"",confirmButtonColor:t.dataset.apiConfirmBtnColor?t.dataset.apiConfirmBtnColor:"primary",title:t.dataset.apiConfirm,confirmCallback:function(){API.makeRequest("GET",bb.restUrl(t.getAttribute("href")),{},function(e){return bb._afterComplete(t,e)},function(e){FOSSBilling.message("".concat(e.message," (").concat(e.code,")"),"error")})}}):t.dataset.apiPrompt?Modals.create({type:"prompt",title:t.dataset.apiPromptTitle,label:t.dataset.apiPromptText?t.dataset.apiPromptText:"Label",value:t.dataset.apiPromptDefault?t.dataset.apiPromptDefault:"",promptConfirmCallback:function(e){if(e){var n={};n[t.dataset.apiPromptKey]=e,API.makeRequest("GET",bb.restUrl(t.getAttribute("href")),n,function(e){return bb._afterComplete(t,e)},function(e){FOSSBilling.message("".concat(e.message," (").concat(e.code,")"),"error")})}}}):API.makeRequest("GET",bb.restUrl(t.getAttribute("href")),{},function(e){return bb._afterComplete(t,e)},function(e){FOSSBilling.message("".concat(e.message," (").concat(e.code,")"),"error")}),!1})},n=0;n<e.length;n++)t()},menuAutoActive:function(){sa("ul#menu li a").filter(function(){return document.location.href==this.href}).parents("li").addClass("active")},cookieCreate:function(e,t,n){if(n){var r=new Date;r.setTime(r.getTime()+24*n*60*60*1e3);var i="; expires="+r.toGMTString()}else i="";document.cookie=e+"="+t+i+"; path=/"},cookieRead:function(e){for(var t=e+"=",n=document.cookie.split(";"),r=0;r<n.length;r++){for(var i=n[r];" "==i.charAt(0);)i=i.substring(1,i.length);if(0==i.indexOf(t))return i.substring(t.length,i.length)}return null},insertToTextarea:function(e,t){var n=document.getElementById(e),r=n.scrollTop,i=0,o=n.selectionStart||"0"==n.selectionStart?"ff":!!document.selection&&"ie";"ie"==o?(n.focus(),(s=document.selection.createRange()).moveStart("character",-n.value.length),i=s.text.length):"ff"==o&&(i=n.selectionStart);var s,a=n.value.substring(0,i),l=n.value.substring(i,n.value.length);(n.value=a+t+l,i+=t.length,"ie"==o)?(n.focus(),(s=document.selection.createRange()).moveStart("character",-n.value.length),s.moveStart("character",i),s.moveEnd("character",0),s.select()):"ff"==o&&(n.selectionStart=i,n.selectionEnd=i,n.focus());return n.scrollTop=r,"undefined"!=typeof CKEDITOR&&CKEDITOR.instances[e].insertText(t),!1},currency:function(e,t,n,r){return e=parseFloat(e)*parseFloat(t),void 0!==r&&(e*=r),e.toFixed(2)+" "+n}},sa.fn.simpleTabs=function(){sa(this).find(".tab_content").hide(),sa(this).find("ul.tabs li:first").addClass("activeTab").show(),sa(this).find(".tab_content:first").show(),sa("ul.tabs li").on("click",function(){sa(this).parent().parent().find("ul.tabs li").removeClass("activeTab"),sa(this).addClass("activeTab"),sa(this).parent().parent().find(".tab_content").hide();var e=sa(this).find("a").attr("href");return sa(e).show(),!1}),sa(document.location.hash).length&&(sa('a[href="'+document.location.hash+'"]').parent().trigger(),sa(window).scrollTop(window.location.href.indexOf("#")))},globalThis.FOSSBilling={message:function(e){var t;switch(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info"){case"error":t="danger";break;case"warning":t="warning";break;default:t="primary"}var n=document.querySelector(".toast-container"),r=document.createElement("div");n.appendChild(r),r.classList.add("toast","show"),r.setAttribute("role","alert"),r.setAttribute("aria-live","assertive"),r.setAttribute("aria-atomic","true");var i=document.createElement("div");i.className="toast-header";var o=document.createElement("span");o.className="p-2 border border-light bg-".concat(t," rounded-circle me-2"),i.appendChild(o);var s=document.createElement("strong");s.className="me-auto",s.textContent="System message",i.appendChild(s);var a=document.createElement("button");a.type="button",a.className="btn-close",a.setAttribute("data-bs-dismiss","toast"),a.setAttribute("aria-label","Close"),i.appendChild(a),r.appendChild(i);var l=document.createElement("div");l.className="toast-body",l.textContent=e,r.appendChild(l),r.addEventListener("hidden.bs.toast",function(){n.removeChild(r)}),new bootstrap.Toast(r).show()}},sa(function(){sa(document).ajaxStart(function(){sa(".loading").show()}).ajaxStop(function(){sa(".loading").hide()}),sa("form.api-form").length&&bb.apiForm(),sa("a.api-link").length&&bb.apiLink(),FOSSBilling.backToTop=oa,FOSSBilling.backToTop(),sa("div.simpleTabs").simpleTabs(),sa(document).on("click","div.msg span.close",function(){return sa(this).parent().slideUp(70),!1}),sa(".hideit").on("click",function(){sa(this).fadeOut(400)})}),globalThis.$=globalThis.jQuery=i(),globalThis.bootstrap=t,document.addEventListener("DOMContentLoaded",function(){aa(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bi(e)}),globalThis.flashMessage=function(e){var t=e.message,n=void 0===t?"":t,r=e.reload,i=void 0!==r&&r,o=e.type,s=void 0===o?"info":o,a="flash-message",l=sessionStorage.getItem(a);if(""===n&&l)return FOSSBilling.message(l,s),void sessionStorage.removeItem(a);n&&(sessionStorage.setItem(a,n),"boolean"==typeof i&&i?bb.reload():"string"==typeof i&&bb.redirect(i))},flashMessage({}),document.querySelectorAll("input[required], textarea[required]").forEach(function(e){var t=e.previousElementSibling;if(!e.parentElement.parentElement.classList.contains("auth")&&t&&"label"===t.tagName.toLowerCase()){var n=document.createElement("span");n.textContent=" *",n.classList.add("text-danger"),t.appendChild(n)}}),document.querySelectorAll("select.currency_selector").forEach(function(e){e.addEventListener("change",function(){API.guest.post("cart/set_currency",{currency:e.value},function(e){location.reload()},function(e){FOSSBilling.message(e)})})})})})()})();